{"name": "@baidu/comate-stack", "version": "0.0.1", "private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS=--max-old-space-size=12144 vite build && vite build --mode script", "generate-icons": "sh scripts/icons/generate-icons.sh", "postinstall": "patch-package", "lint": "skr lint", "lint-less": "npx @koopa-cli/glob less", "lint-strict": "node scripts/lint-strict.js", "lint-type": "tsc", "start": "vite", "test": "vitest run"}, "husky": {"hooks": {"pre-push": "yarn lint-strict && yarn lint && yarn lint-type"}}, "dependencies": {"@ant-design/icons": "^5.3.6", "@ant-design/pro-card": "^2.5.4", "@ant-design/pro-components": "^2.7.10", "@ant-design/x": "^1.2.0", "@apollo/client": "^3.5.10", "@baidu/devops-code": "^0.0.9", "@baidu/devops-components": "^0.0.25", "@baidu/devops-design": "^0.1.13", "@baidu/devops-log": "^0.0.13", "@baidu/devops-shared": "^0.4.12", "@baidu/devops-staff": "^0.0.3", "@baidu/devops-track": "^0.0.3", "@baidu/ee-icon": "^9.27.0", "@baidu/icloud-performance": "^0.0.4", "@baidu/is-utf8": "^1.0.0", "@baidu/k-card": "^0.9.10", "@baidu/k-guest": "0.6.6", "@baidu/k-log": "^0.0.3", "@baidu/k-plugin-helper": "^0.3.1", "@baidu/ku-mega-sdk": "^2.0.8", "@baidu/morpho": "^2.7.511-rd-0106-173223.0", "@baidu/performance-owl": "0.0.3", "@baidu/realtime-editor": "^2.1.195-GJZ-aigc-14", "@baidu/spy": "^1.0.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.2.0", "@codemirror/language-data": "^6.3.1", "@codemirror/lint": "^6.4.2", "@ctrl/tinycolor": "^4.1.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.0.5", "@emotion/css": "^11.11.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@formkit/auto-animate": "^0.7.0", "@heroicons/react": "^2.0.15", "@js-preview/docx": "^1.6.4", "@js-preview/excel": "^1.7.14", "@js-preview/pdf": "^2.0.10", "@microlink/react-json-view": "^1.24.0", "@otakustay/react-source-view": "^0.0.1", "@otakustay/source-tokenizer": "^1.0.0", "@panda-design/components": "^0.5.5", "@panda-design/path-form": "^0.0.18", "@panda-design/router": "^0.0.25", "@react-three/drei": "^9.115.0", "@react-three/fiber": "^8.17.10", "@uiw/react-codemirror": "^4.21.20", "@uiw/react-split": "^5.9.0", "ace-builds": "^1.16.0", "ag-grid-react": "^33.3.0", "ansi-to-html": "^0.7.2", "antd": "^5.26.3", "axios": "^1.4.0", "axios-interface": "^2.1.1", "canvas-confetti": "^1.6.0", "change-case": "^5.4.2", "chroma-js": "^2.1.1", "clipboard": "^2.0.10", "constate": "^3.3.2", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "custom-protocol-check": "^1.3.0", "date-fns": "^2.30.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "emoji-mart": "5.2.1", "file-type": "^16.0.1", "for-editor": "^0.3.5", "for-editor-herb": "^2.3.7", "github-markdown-css": "^5.5.1", "graphql": "^16.3.0", "hast-util-from-html-isomorphic": "^2.0.0", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "huse": "^2.0.4", "iframe-interface": "^0.0.2", "js-md5": "^0.7.3", "json-stable-stringify": "^1.1.1", "konva": "^9.3.18", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "material-file-icons": "^2.4.0", "postinstall-postinstall": "^2.1.0", "pptx-preview": "^1.0.5", "prism-color-variables": "^1.0.1", "prism-themes": "^1.9.0", "pubsub-js": "^1.9.5", "query-string": "^9.0.0", "rc-util": "^5.43.0", "re-resizable": "^6.9.11", "react": "^18.3.1", "react-ace": "^10.1.0", "react-beautiful-dnd": "^13.1.1", "react-calendar-heatmap": "^1.8.0", "react-copy-to-clipboard": "^5.1.0", "react-diff-view": "^3.2.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-error-boundary": "^4.0.2", "react-full-screen": "^1.1.1", "react-highlight-words": "^0.20.0", "react-icons": "^4.8.0", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.5", "react-konva": "18.2.10", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.0.19", "react-router-dom": "^6.28.0", "react-router-template-link": "^2.7.2", "react-suspense-boundary": "^2.3.1", "react-syntax-highlighter": "^15.5.0", "react-timeago": "^6.2.1", "reactflow": "^11.10.1", "refractor": "^4.8.1", "region-core": "^11.9.3", "rehype-autolink-headings": "^7.1.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-math": "^6.0.0", "rxjs": "^7.5.5", "rxjs-hooks": "^0.7.0", "search-query-parser": "^1.6.0", "short-unique-id": "^4.4.4", "sockjs-client": "^1.6.0", "three": "^0.169.0", "unidiff": "^1.0.4", "use-antd-resizable-header": "^3.1.0", "use-image": "^1.1.1", "use-short-key": "^0.0.3", "uuid": "^9.0.0", "wavesurfer.js": "^6.0.1", "web-highlighter": "^0.7.4", "web-tree-sitter": "^0.25.3", "xml-formatter": "^3.6.5"}, "devDependencies": {"@baidu/ee-dev-login": "^5.1.0", "@baidu/reskript-plugins": "^5.0.2", "@emotion/babel-plugin": "^11.11.0", "@monaco-editor/react": "^4.6.0", "@originjs/vite-plugin-federation": "^1.3.6", "@reskript/cli": "^6.2.1", "@reskript/cli-lint": "^6.2.1", "@reskript/config-lint": "^6.2.1", "@reskript/portal": "^6.2.1", "@reskript/settings": "^6.2.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^13.5.0", "@types/canvas-confetti": "^1.6.0", "@types/crypto-js": "^4.2.2", "@types/decode-uri-component": "^0.2.2", "@types/emoji-mart": "^3.0.9", "@types/jest": "^27.5.2", "@types/js-md5": "^0.7.0", "@types/js-yaml": "^4", "@types/json-stable-stringify": "^1.0.36", "@types/lodash": "^4.14.194", "@types/node": "^16.18.12", "@types/react": "^18.0.28", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^18.0.11", "@types/react-highlight-words": "^0.16.4", "@types/react-infinite-scroller": "^1.2.5", "@types/react-syntax-highlighter": "15.5.0", "@types/sockjs-client": "^1.5.1", "@types/ua-parser-js": "^0.7.36", "@types/uuid": "^9.0.1", "@vitejs/plugin-react": "^4.0.1", "axios-mock-adapter": "^1.20.0", "core-js": "^3.21.1", "esbuild": "^0.19.2", "esbuild-plugin-react-virtualized": "^1.0.4", "eslint": "^8.43.0", "husky": "4.3.8", "jsdom": "^26.1.0", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "^13.2.3", "patch-package": "^8.0.0", "react-json-editor-ajrm": "^2.5.14", "react-monaco-editor": "^0.40.0", "rollup-plugin-visualizer": "^5.14.0", "stylelint": "^15.9.0", "typescript": "^5.3.2", "vite": "^5.0.2", "vite-plugin-svgr": "^3.2.0", "vite-plugin-top-level-await": "^1.5.0", "vitest": "^3.2.2"}, "resolutions": {"rc-util": "5.43.0", "strip-ansi": "6.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "volta": {"node": "20.19.4"}}