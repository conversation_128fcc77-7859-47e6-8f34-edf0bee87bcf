import {ReactNode, CSSProperties} from 'react';
import {To, NavLinkProps} from 'react-router-dom';

export interface MenuItemType {
    type?: string;
    icon?: ReactNode;
    title: string;
    shortTitle?: string;
    to?: To;
    isActive?: boolean;
    onClick?: () => void;
    className?: NavLinkProps['className'];
    style?: NavLinkProps['style'];
    children?: MenuItemType[];
    childrenElement?: ReactNode;
    // logo 扩展，icon链接和标题链接
    titleTo?: To;
    iconTo?: To;
}

export interface MenuProps {
    logo: MenuItemType;
    items?: MenuItemType[];
    // 菜单兼容写法
    childrenElement?: ReactNode;
    // 折叠相关
    enableCollapse?: boolean;
    collapsed?: boolean;
    onCollapse?: (value: boolean) => void;
    // external icon 相关
    isExternal?: (to: To) => boolean;
    externalIcon?: ReactNode;
    className?: string;
    style?: CSSProperties;
}
