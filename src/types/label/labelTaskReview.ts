export interface AssignedTaskInfo {
    labelStudioProjectId: number;
    taskType: string;
    groupType: 'SET_DATA' | 'SET_GROUP';
    taskMethod: 'AVG_RANDOM'| 'AVG_ORDER';
    sponsor?: string;
    modifier?: string;
    groupMsgs: GroupItem[];
}

export interface GroupItem {
    groupId: number;
    groupCount: number;
    labelOwners: string[];
    reviewOwners: string[];
    sampleReviewSwitch: 'CLOSE' | 'OPEN';
    sampleReviewProportion: number;
    sampleReviewCount: number;
    reviewPassCount?: number;
    reviewFailedCount?: number;
    completeCount?: number;
    uncompleteCount?: number;
}

export interface ParamsPostCheckTask {
    labelStudioProjectId: number;
    result: string;
    reason: string;
}

export interface ReviewResult {
    allCount: number;
    reviewFailedCount: number;
}

export interface CheckRecord {
    id: number;
    labelStudioProjectid: number;
    operator: string;
    result: 'PASS' | 'FAILED' | 'CLEAR';
    reason: string | null;
    gmtCreate: string;
    gmtModified: string;
}

export interface AssignedTaskDetail {
    reviewResult: ReviewResult;
    reviewTask: AssignedTaskInfo; // 审核+标注分组信息
    checkRecord: CheckRecord;
}

export interface ParamsGetLabelAssignCount {
    labelStudioProjectId: number;
}

export interface ReviewDetail {
    allTaskIds: number[];
    allCount: number;
    completeCount: number;
    uncompleteCount: number;
}

export interface AnnotateDetail {
    allTaskIds: number[];
    allCount: number;
    completeCount: number;
    uncompleteCount: number;
}

export interface LabelAssignCount {
    reviewer: boolean;
    reviewDetail: ReviewDetail | null;
    annotator: boolean;
    annotateDetail: AnnotateDetail | null;
}

export interface ParamsGetReviewResultItem {
    taskId: number;
}

export interface ReviewResultItem {
    result: ReviewResultItemDetail;
    history: ReviewResultItemDetail[];
}

export interface ReviewResultItemDetail {
    id: number;
    taskId: number;
    taskAnnotationId: number;
    operator: string;
    labelReviewTaskId: number;
    result: string;
    reason: string | null;
    gmtCreate: string;
    gmtModified: string;
}

export interface ParamsPostReviewResultItem {
    labelStudioProjectId: number;
    reviewRecordId?: number; // 当为新增时，可以为空
    taskId?: number;
    taskAnnotationId?: number;
    result: string;
    reason?: string;
}

export interface TaskExecutors {
    reviewers: string[];
    labelers: string[];
}
