import {Path} from '@panda-design/path-form';
import {ChatMessage} from '../staff/chat';

/* eslint-disable max-lines */
export interface MCPSpaceMember {
    label: string;
    value: string;
    role: string;
    nameType: string;
}

export interface MCPSpace {
    id: number;
    name?: string;
    description?: string;
    cloudAccountIds?: string[];
    member?: MCPSpaceMember[];
}

export interface MCPServerParam {
    name: string;
    description: string;
    dataType: string;
    required: boolean;
    value: string;
    defaultValue?: string;
}

export interface MCPServerTool {
    id: number;
    name: string;
    description: string;
    status: string;
}

export interface MCPToolItem {
    id: number;
    name: string;
    toolKey: string;
    serviceId: number;
    toolStatus: string;
    description: string;
    toolParams: {
        toolParams: BaseParam[];
        serverParams: BaseParam[];
    };
    toolConf: {
        responseTemplate: string;
        apiDefinition: APIInfo;
        openapiConf: ApiDefinition;
    };
}

export interface APIInfo {
    endpoint: string;
    params: any;
    systemParams: BaseParam[];
}

export interface ServerConf {
    serverSourceType: string;
    serverConfig: string;
    overview: string;
    // 是个对象
    serverExtension: any;
    serverParams: ServerParam[];
}

export interface ServerParam {
    name: string;
    type: string;
    description: string;
    defaultValue: string;
    isHide: boolean;
    required: boolean;
}

export interface Visibility {
    type: string;
    content: string;
}

export interface BaseParam {
    name: string;
    example?: string;
    exampleValue?: string;
    description?: string;
    relateApiParamPath?: string;
    type?: string;
    refParam?: string;
    dataType?: string;
    required: boolean;
    value?: string; // 输入值时
    children?: BaseParam[]; // body时
    key?: string; // 记录 paramList 中的 refParams
    fieldPath?: Path; // 记录路径
}

export type MCPServerStatus = 'draft' | 'release';
export type MCPServerType = 'openapi' | 'script' | 'external';
export type MCPServerProtocolType = 'SSE' | 'STDIO';
export type MCPServerPublishType = [] | ['workspace'] | ['hub'];

export interface MCPServerBase {
    id: number;
    workspaceId: number;
    viewCount: number;
    favorite: boolean;
    name: string;
    serverKey: string;
    description: string;
    serverSourceType: MCPServerType;
    serverProtocolType: MCPServerProtocolType;
    serverStatus: MCPServerStatus;
    serverConf: ServerConf;
    serverParams: null | MCPServerParam[];
    serverPublishType: MCPServerPublishType;
    labels?: SpaceLabel[];
    icon: string | null;
    lastModifyTime: string;
    lastModifyUser: string;
    publishTime: string;
    publishUser: string;
    /* 官方示例的server包括下边两个字段 */
    officialExample?: boolean;
    originalId?: number;
    /* 后端缺失 */
    departmentName: string;
    official?: boolean;
    contacts: string[];
}

export interface SpaceLabel {
    id: number;
    labelValue: string;
    labelType: string;
    content?: string;
}

export interface RequestBody {
    type: string;
    parameters: any[];
    jsonSchema: JSONSchema;
    example: string;
}

export interface JSONSchema {
    type: string;
    items: JSONSchema | {type: string};
    properties: Record<string, Description | JSONSchema>;
    required: string[];
    description?: string;
    name?: string;
    'x-iapi-orders': string[];
    'x-iapi-ignore-properties': any[];
}

export interface Description {
    type: string;
    description?: string;
    examples?: string[];
    format?: string;
    enum?: string[];
    value?: string;
}

export interface ApiDefinition {
    name: string;
    type: string;
    path: string;
    method: string;
    description: string;
    operation_id: string;
    tags: string[];
    auth: Record<string, unknown>;
    parameters: Record<string, BaseParam[]>;
    requestBody: RequestBody;
    responseBody: Record<string, unknown>;
    fileName?: string;
}

export interface ApplicationBase {
    applicationId: number;
    applicationName: string;
    ifSub: boolean;
}

export interface AppListForMCPServerBase {
    workspaceId: number;
    workspaceName: string;
    role: string;
    applications: ApplicationBase[];
}

export type MCPChatStatus = 'loading' | 'success' | 'error';
export type MCPMessageRole = 'user' | 'assistant' | 'system';
export type MCPToolCallStatus = 'success' | 'pending' | 'fail' | 'running';

export interface MCPToolInfo {
    type: 'mcpTool';
    serverName: string;
    toolName: string;
}

export interface MCPToolOutput {
    mimeType: string;
    content: string;
}

export interface MCPToolCall {
    type: 'toolCall';
    tool: MCPToolInfo;
    input: string; // stringified JSON
    status: MCPToolCallStatus;
    output: MCPToolOutput[];
}

export interface MCPChatConfig {
    modelId: string;
    systemPrompt: string;
}

export interface MCPChatSession {
    messages: ChatMessage[];
}
