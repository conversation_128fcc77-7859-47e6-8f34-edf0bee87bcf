/* eslint-disable max-lines, complexity */
import {css} from '@emotion/css';
import {Divider, Form, FormInstance, Space} from 'antd';
import {Button, message, Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {InfoCircleFilled} from '@ant-design/icons';
import {assign, omit, pick} from 'lodash';
import {
    ModelLineUpItemTypeEnum,
    apiAssociateCard,
    apiCreateTask,
    apiModelLineUpInfoPost,
    apiSpaceLimitTaskCheckPost,
} from '@/api/ievalue/task';
import {
    EvaluateShowMethodEnum,
    StrategyRunningModeEnum,
} from '@/constants/ievalue/evaluate';
import {
    CooperateTypeEnum,
    OrdermadeParamAuthWayEnum,
    TargetEnum,
    TaskPredictRoundEnum,
    TaskPredictTypeEnum,
    TaskStageEnum,
} from '@/constants/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {TaskUrl} from '@/links/ievalue/task';
import {useModelCaseLimitList} from '@/hooks/ievalue/prompt';
import {convertModelSelectsToModelParams} from '../../../StepDetail';
import {getDefaultBlindParamsData, getDefaultRejectRateParamsData, getDefaultSamplingRateParamsData} from '../../utils';
import {BasicConfig} from '../BasicConfig';
import {DatasetConfig} from '../DatasetConfig';
import {EvaluateConfig} from '../EvaluateConfig';
import ModelWaitInfo from '../ModelWaitInfo';
import {ModelCaseLimitAlert} from '../ModelCaseLimitAlert';
import {
    useCreateTaskIsFeedback,
    useCreateTaskIsAutoEvaluate,
    useCreateTaskTemplateStageTypes,
} from '../CreateTaskProvider/DatasetProvider';
import {
    useCreateTaskIsQuickEvaluate,
    useCreateTaskTarget,
    useCreateTaskTemplateID,
    useCreateTaskIsCreateTemplate,
} from '../CreateTaskProvider/CreateTaskBaseProvider';
import {useCreateTaskTemplateData} from '../CreateTaskProvider/DatasetProvider';

interface CreateTaskFormProps {
    form: FormInstance;
    initialValues?: any;
    templateButtonText?: string;
    onTemplateFinish?: (data: any) => Promise<void>;
    onCancel?: () => void;
}
export const SevenDayMinutes = 7 * 24 * 60;

export const CreateTaskForm = ({
    form,
    initialValues,
    templateButtonText,
    onTemplateFinish,
    onCancel,
}: CreateTaskFormProps) => {
    const [isLoading, {on: startLoading, off: stopLoading}] =
        useBoolean(false);
    const navigate = useNavigate();
    const spaceCode = useSpaceCodeSafe();
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const isFeedback = useCreateTaskIsFeedback();
    const isAutoEvaluate = useCreateTaskIsAutoEvaluate();
    const target = useCreateTaskTarget();
    const templateID = useCreateTaskTemplateID();
    const stageTypes = useCreateTaskTemplateStageTypes();
    // const strategyList = useCreateTaskStrategyList();
    const modelCaseLimitList = useModelCaseLimitList();
    const isCreateTemplate = useCreateTaskIsCreateTemplate();
    const templateData = useCreateTaskTemplateData();

    const DefaultFormData: any = useMemo(
        () => {
            return {
                predictRound: TaskPredictRoundEnum.ONCE,
                datasetType: 'new',
                evaluateMode:
                    isAutoEvaluate || isFeedback
                        ? StrategyRunningModeEnum.AUTO
                        : StrategyRunningModeEnum.MANUAL,
                blind: false,
                blindParams: getDefaultBlindParamsData(templateData),
                cooperateType: CooperateTypeEnum.ALONE,
                proportion: false,
                proportionParams: [],
                isSampling: false,
                samplingRateParams: getDefaultSamplingRateParamsData(templateData, 100),
                autoSkip: false,
                withReject: false,
                rejectRateParams: getDefaultRejectRateParamsData(templateData, 100),
                showMethod: EvaluateShowMethodEnum.CARD,
                evaluateParam: JSON.stringify({}, null, 2),
                reportFormat: 'DefaultFormat',
                datasetFormat: 1,
                dispatchType: isFeedback,
                isMultiEvaluatePredict: true,
                ordermadeParam: {authWay: OrdermadeParamAuthWayEnum.DEFAULT},
                predictParam: {isBatch: false},
                ...initialValues,
            };
        },
        [initialValues, isAutoEvaluate, isFeedback, templateData]
    );

    const showEvaluateParam = useMemo(
        () => {
            return (isAutoEvaluate || isFeedback) && target !== 'Petite';
        },
        [isAutoEvaluate, isFeedback, target]
    );

    const associateCard = async (taskID: number, cards: any[]) => {
        try {
            const params = [];
            for (const i of cards) {
                const {title, id, type, spacePrefixCode, status, sequence} =
                    i;
                params.push({
                    taskID,
                    title,
                    cardID: id,
                    cardType: type.name,
                    space: spacePrefixCode,
                    status: status,
                    sequence: sequence,
                });
            }
            await apiAssociateCard(params);
        } catch (e) {
            if (e instanceof Error) {
                message.error(e.message);
            }
        }
    };

    const formatCreateTaskData = useCallback(
        (data: any) => {
            const autoMetricList =
                data?.autoMetric?.filter((item: any) => item.checked) ?? [];
            const manualMetricList =
                data?.manualMetric?.filter((item: any) => item.checked) ?? [];
            const manualAutoComputeMetricList =
                data?.manualAutoComputeMetric?.filter(
                    (item: any) => item.checked
                ) ?? [];
            const evaluateParam = JSON.parse(data?.evaluateParam || '{}');
            const promptVersionIDs = [
                TargetEnum.Prompt,
                TargetEnum.PromptFlow,
            ].includes(target)
                ? target === TargetEnum.Prompt
                    ? data?.promptHistoryIDs?.map((e: number[]) => e[1])
                    : data?.promptHistoryIDs
                : undefined;
            return {
                trainTaskID: data?.trainTaskID, // comateStack 任务ID
                isAutoTransfer: data?.isAutoTransfer, // 是否自动流转
                comeFrom: data?.comeFrom, // comeFrom 任务来源
                promptHistoryID: data?.promptHistoryID, // promptHistoryID 关联promptID
                templateID,
                spaceCode,
                target,
                name: data?.name,
                note: data?.note ?? '',
                modelParams: data?.modelSelects?.map(
                    convertModelSelectsToModelParams
                ),
                datasetID: data?.datasetID,
                mapTemplateID: data?.mapTemplateID,
                datasetName: data?.datasetName,
                predictRound: data?.predictRound,
                predictType:
                    data?.predictRound === TaskPredictRoundEnum.ONCE
                        ? TaskPredictTypeEnum.MANUAL
                        : stageTypes?.includes(TaskStageEnum.PREDICTING)
                            ? TaskPredictTypeEnum.STATIC
                            : data?.isMultiEvaluatePredict
                                ? TaskPredictTypeEnum.DYNAMIC
                                : TaskPredictTypeEnum.OFFLINE,
                priority: '10',
                evaluateMode: data?.evaluateMode,
                cooperateType: data?.cooperateType ?? CooperateTypeEnum.ALONE,
                blind: data?.blind ? 1 : 0,
                blindParams: data?.blindParams,
                showMethod: data?.showMethod ?? 'CARD',
                reportFormat: data?.reportFormat,
                isRoundScoring: data?.isRoundScoring ? 1 : 0,
                isSessionAutoScore: data?.isSessionAutoScore ? 1 : 0,
                isSampling: data?.isSampling ? 1 : 0,
                samplingRateParams: data?.samplingRateParams,
                autoSkip: data?.autoSkip ? 1 : 0,
                withReject: data?.withReject ? 1 : 0,
                rejectRateParams: data?.rejectRateParams,
                proportion: data?.proportion ? 1 : 0,
                proportionParams: data?.proportionParams,
                datasetFormat: data?.datasetFormat,
                dispatchType: data?.dispatchType ? 1 : 0,
                autoMetric: autoMetricList,
                autoPolicy: data?.autoPolicy,
                manualMetric: manualMetricList,
                manualAutoComputeMetric: manualAutoComputeMetricList,
                manualPolicy: data?.manualPolicy,
                hasFeedback: data?.hasFeedback ? 1 : 0,
                files: data?.files || [],
                taskType:
                    (target === TargetEnum.LLM
                        && !data?.modelSelects?.length)
                        || isQuickEvaluate
                        ? 'fastEvaluation'
                        : '',
                ...(showEvaluateParam ? {evaluateParam} : {}),
                cards: data.cards ?? [],
                ordermadeParam: data?.ordermadeParam,
                predictParam: data?.predictParam,
                promptVersionIDs,
                tags: data?.tags || [],
                dirID: data?.dirID,
            };
        },
        [
            isQuickEvaluate,
            showEvaluateParam,
            spaceCode,
            stageTypes,
            target,
            templateID,
        ]
    );

    const createTask = useCallback(
        async (createData: any) => {
            startLoading();
            try {
                const taskData = await apiCreateTask(createData);
                if (taskData.ID) {
                    message.success('创建成功');
                    createData?.cards?.length > 0
                        && associateCard(taskData.ID, createData.cards);
                    navigate(
                        TaskUrl.taskDetail.toUrl({
                            spaceCode,
                            taskID: taskData.ID,
                        })
                    );
                }
            } catch (e) {
                if (e instanceof Error) {
                    message.error(e.message);
                }
            } finally {
                stopLoading();
            }
        },
        [startLoading, navigate, spaceCode, stopLoading]
    );

    const checkModelLineUpInfo = useCallback(
        async (data: any) => {
            const models = data?.modelParams
                ?.map((item: any) => item?.modelID)
                ?.join(',');
            const promptHistoryIDs = data?.promptVersionIDs?.join(',');
            const modelLineUpInfoList = await apiModelLineUpInfoPost({
                models,
                promptHistoryIDs,
                dataSetID: data?.datasetID,
            });
            const waitList = modelLineUpInfoList?.filter(
                item => item.waitingMinutes > 0
            );
            const moreThanSevenDayList = modelLineUpInfoList?.filter(
                item =>
                    item.type === ModelLineUpItemTypeEnum.DEMO
                    && item.waitingMinutes > SevenDayMinutes
            );
            const overLimitCountList = modelLineUpInfoList?.filter(
                item =>
                    item.type === ModelLineUpItemTypeEnum.PAYMENT
                    && ((modelCaseLimitList.includes(item.model)
                        && item.caseCount > 200)
                        || item.caseCount > 500)
            );
            if (moreThanSevenDayList?.length > 0) {
                Modal.warning({
                    width: 500,
                    title: '暂停提交任务',
                    content: (
                        <ModelWaitInfo
                            waitList={moreThanSevenDayList}
                            firstTitle={
                                <div>
                                    当前任务选择的试用模型排队等待推理的时间超过7天：
                                </div>
                            }
                            // eslint-disable-next-line max-len
                            secondTitle={
                                <div>
                                    平台<b style={{color: 'red'}}>暂停</b>
                                    上述模型提交推理任务。请等待排队缓解后重试或使用自己的AKSK注册模型来推理。
                                </div>
                            }
                        />
                    ),
                    okText: '确定',
                });
                return;
            }
            if (waitList?.length > 0) {
                Modal.confirm({
                    width: 500,
                    title: '推理排队提醒',
                    icon: <InfoCircleFilled style={{color: '#035fff'}} />,
                    content: (
                        <ModelWaitInfo
                            waitList={waitList}
                            firstTitle={
                                <div>
                                    当前任务选择的试用模型需要排队等待推理：
                                </div>
                            }
                            secondTitle={<div>请确认是否要继续创建任务？</div>}
                        />
                    ),
                    onOk: async () => {
                        await createTask(data);
                    },
                });
                return;
            }
            if (overLimitCountList?.length > 0) {
                Modal.warning({
                    width: 600,
                    title: '提交数量超限',
                    content: (
                        <ModelCaseLimitAlert
                            overLimitCountList={overLimitCountList}
                            firstTitle={
                                <div>当前任务选择的模型已超过数量限制：</div>
                            }
                            secondTitle={
                                <div>
                                    推理模型case数限制为200条，其余模型限制为500条。
                                    请尝试以下解决方案：
                                    <ol
                                        style={{
                                            margin: '8px 0',
                                            paddingLeft: 20,
                                        }}
                                    >
                                        <li>减少任务数量至限制范围内</li>
                                        <li>等待排队缓解后重试</li>
                                        <li>使用自己的AKSK注册模型进行推理</li>
                                    </ol>
                                </div>
                            }
                        />
                    ),
                    okText: '确定',
                });
                return;
            }
            await createTask(data);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [createTask]
    );

    const onSubmit = useCallback(
        async (data: any) => {
            const createData = formatCreateTaskData(data);
            if (stageTypes?.includes(TaskStageEnum.PREDICTING)) {
                const checkData = pick(createData, [
                    'templateID',
                    'target',
                    'modelParams',
                    'datasetID',
                    'autoMetric',
                    'autoPolicy',
                    'ordermadeParam',
                    'promptVersionIDs',
                ]);
                const result = await apiSpaceLimitTaskCheckPost(checkData);
                if (!result?.haveQuota) {
                    Modal.confirm({
                        width: 500,
                        title: '试用模型超过使用限额',
                        icon: <InfoCircleFilled style={{color: '#035fff'}} />,
                        content:
                            '您可以选择“返回”更换其他非试用模型，或者选择“使用收费模型”继续创建任务。注意：使用收费模型会产生费用，平台会给当前项目关联的资源账号出账单。',
                        onOk: async () => {
                            const newCreateData = assign(
                                createData,
                                omit(result, ['haveQuota'])
                            );
                            await checkModelLineUpInfo(newCreateData);
                        },
                        cancelText: '返回',
                        cancelButtonProps: {
                            type: 'primary',
                        },
                        okButtonProps: {
                            type: 'default',
                        },
                        okText: '使用收费模型',
                        autoFocusButton: 'cancel',
                    });
                    return;
                }
                await checkModelLineUpInfo(createData);
                return;
            }
            await createTask(createData);
        },
        [formatCreateTaskData, stageTypes, createTask, checkModelLineUpInfo]
    );

    const handleTemplateFinish = useCallback(
        async () => {
            startLoading();
            try {
                await form.validateFields();
                const data = form.getFieldsValue();
                const createData = formatCreateTaskData(data);
                await onTemplateFinish(createData);
            } catch (e) {
                if (e instanceof Error) {
                    message.error(e.message);
                }
            } finally {
                stopLoading();
            }
        },
        [
            startLoading,
            form,
            formatCreateTaskData,
            onTemplateFinish,
            stopLoading,
        ]
    );

    const handleCancel = useCallback(
        async () => {
            if (onCancel) {
                onCancel();
            } else {
                navigate(
                    TaskUrl.taskTemplateSelect.toUrl({
                        spaceCode,
                        target: 'LLM',
                    })
                );
            }
        },
        [onCancel, navigate, spaceCode]
    );

    const onValuesChange = useCallback(
        (change: any) => {
            // 切换时清除datasetID
            if (change?.datasetType || change?.datasetFormat !== undefined) {
                form.setFieldValue('datasetID', '');
                form.setFieldValue('datasetName', '');
                form.setFieldValue('files', []);
            }
            // if (change?.autoPolicy) {
            //     const currentStrategy = strategyList.find(
            //         item => item.ID === change?.autoPolicy
            //     );
            //     form.setFieldValue(
            //         'evaluateParam',
            //         JSON.stringify(
            //             currentStrategy.runningMode
            //                 === StrategyRunningModeEnum.YIYANDIY
            //                 ? YIYANDIY_EVALUATEPARAMS
            //                 : {}
            //         )
            //     );
            // }
        },
        [form]
    );

    return (
        <Form
            className={css`
                min-width: 650px;
                .ant-5-select {
                    max-width: 500px;
                }
            `}
            scrollToFirstError
            form={form}
            name="basic"
            labelCol={{flex: '150px'}}
            labelAlign="left"
            initialValues={DefaultFormData}
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            autoComplete="off"
        >
            <BasicConfig />
            <DatasetConfig />
            <EvaluateConfig />
            <Divider />
            <Space style={{marginLeft: 150}}>
                {!isCreateTemplate && (
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={isLoading}
                    >
                        完成
                    </Button>
                )}
                {onTemplateFinish && (
                    <Button
                        type="primary"
                        onClick={handleTemplateFinish}
                        loading={isLoading}
                    >
                        {templateButtonText ?? '保存为模板'}
                    </Button>
                )}
                <Button onClick={handleCancel}>取消</Button>
            </Space>
        </Form>
    );
};
