import {Form, Switch} from 'antd';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {SamplingRatioParamItem} from '@/api/ievalue/task';
import {useCreateTaskTemplateStageTypes} from '../../CreateTaskProvider/DatasetProvider';
import {
    useCreateTaskTarget,
    useCreateTaskIsQuickEvaluate,
} from '../../CreateTaskProvider/CreateTaskBaseProvider';
import SamplingRatioParam from './SamplingRatioParam';

export const SamplingAuditFormItem = () => {
    const target = useCreateTaskTarget();
    const form = Form.useFormInstance();
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const isSampling = Form.useWatch('isSampling', form);
    const stageTypes = useCreateTaskTemplateStageTypes();

    return stageTypes?.includes(TaskStageEnum.AUDITING_FORWARD)
        && !isQuickEvaluate
        && target !== 'Petite' ? (
            <>
                <Form.Item
                    label="抽样"
                    name="isSampling"
                    rules={[{required: true, message: '请选择是否抽样'}]}
                    valuePropName="checked"
                >
                    <Switch />
                </Form.Item>
                {isSampling ? (
                    <>
                        <Form.Item
                            label="抽样比例"
                            name="samplingRateParams"
                            rules={[
                                {
                                    validator: async (
                                        _,
                                        valueList: SamplingRatioParamItem[]
                                    ) => {
                                        if (
                                            valueList.some(
                                                item => !item.samplingRatio
                                            )
                                        ) {
                                            return Promise.reject(
                                                new Error('抽样比例不能为空')
                                            );
                                        }
                                        return Promise.resolve();
                                    },
                                },
                            ]}
                        >
                            <SamplingRatioParam />
                        </Form.Item>
                        <Form.Item
                            label="抽样自动流转"
                            rules={[{required: true, message: '请选择是否自动流转'}]}
                            name="autoSkip"
                            valuePropName="checked"
                        >
                            <Switch />
                        </Form.Item>
                    </>
                ) : (
                    <></>
                )}
            </>
        ) : (
            <></>
        );
};
