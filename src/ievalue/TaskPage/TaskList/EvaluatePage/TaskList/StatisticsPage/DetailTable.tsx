import {Table, Typography} from 'antd';
import {useMemo} from 'react';
import dayjs from 'dayjs';
import {marginBottom} from '@panda-design/components';
import {Link} from 'react-router-dom';
import styled from '@emotion/styled';
import {TaskUrl} from '@/links/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    HumanEfficiencyDetailData,
    taskDetail,
    taskDetailData,
} from '@/api/ievalue/task';
import TimeStatsChart from './TimeStatsChart';
import ExpandedTable from './ExpandedTable';

interface Props {
    dataSource?: HumanEfficiencyDetailData | taskDetailData | null;
    loading?: boolean;
    detailTableType?: string;
    isEfficiencyTab?: boolean;
}

const WhiteSpacePreText = styled.div`
    white-space: pre-wrap;
`;

const DetailTable = ({dataSource, loading, detailTableType}: Props) => {
    const spaceCode = useSpaceCodeSafe();

    const columns = useMemo(
        () => {
            const commonColumns = [
                {
                    title: '任务名称',
                    dataIndex: 'taskName',
                    key: 'taskName',
                    sorter: (a: taskDetail, b: taskDetail) =>
                        a?.taskName.localeCompare(b?.taskName, 'zh-CN'),
                    ellipsis: true,
                    render: (name: string, record: taskDetail) => (
                        <Link
                            to={TaskUrl.taskDetail.toUrl({spaceCode, taskID: record.taskID})}
                            target="_blank"
                        >
                            <Typography.Text style={{color: 'inherit'}}>
                                {name}
                            </Typography.Text>
                        </Link>
                    ),
                },
                {
                    title: '创建时间',
                    dataIndex: 'createTime',
                    key: 'createTime',
                    width: 180,
                    sorter: (a: taskDetail, b: taskDetail) =>
                        dayjs(a?.createTime).valueOf() - dayjs(b?.createTime).valueOf(),
                    render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
                },
                {
                    title: '用例总数',
                    width: 120,
                    dataIndex: 'caseTotalCount',
                    key: 'caseTotalCount',
                },
            ];
            const timeColumns = [
                {
                    title: '总时间（分钟）',
                    width: 120,
                    dataIndex: 'totalMinutes',
                    key: 'totalMinutes',
                },
            ];

            const accuracyColumns = [
                {
                    title: '任务准确率',
                    width: 280,
                    dataIndex: 'evaluateAccuracy',
                    key: 'evaluateAccuracy',
                    render: (text: string) => {
                        return <WhiteSpacePreText>{text}</WhiteSpacePreText>;
                    },
                },
                {
                    title: '进展',
                    width: 280,
                    dataIndex: 'progressInfo',
                    key: 'progressInfo',
                    render: (text: string) => {
                        return <WhiteSpacePreText>{text}</WhiteSpacePreText>;
                    },
                },
                {
                    title: '完成时间',
                    width: 240,
                    dataIndex: 'finishedTime',
                    key: 'finishedTime',
                    render: (text: string) => {
                        return <WhiteSpacePreText>{text}</WhiteSpacePreText>;
                    },
                },
            ];

            const lowQualityLabelColumns = [
                {
                    title: '低质量标签',
                    width: 200,
                    dataIndex: 'lowQualityLabel',
                    key: 'lowQualityLabel',
                    render: (text: any) => {
                        if (text === null || text === undefined) {
                            return '--';
                        }
                        return (
                            <Typography.Text
                                ellipsis={{tooltip: text}}
                                style={{maxWidth: 180}}
                            >
                                {text}
                            </Typography.Text>
                        );
                    },
                },
            ];

            const zeroSessionColumns = [
                {
                    title: 'session维度1/0分',
                    children: [
                        {
                            title: '0分数量',
                            width: 130,
                            dataIndex: 'zeroSessionCount',
                            key: 'zeroSessionCount',
                        },
                        {
                            title: '0分占比',
                            width: 130,
                            dataIndex: 'zeroSessionPercentage',
                            key: 'zeroSessionPercentage',
                        },
                    ],
                },
            ];

            if (detailTableType === 'evaluateAccuracy') {
                return [...commonColumns, ...accuracyColumns];
            }

            if (detailTableType === 'notPass') {
                return [...commonColumns, ...lowQualityLabelColumns];
            }

            if (detailTableType === 'zeroScoreSession') {
                return [...commonColumns, ...zeroSessionColumns];
            }

            return [...commonColumns, ...timeColumns];
        },
        [detailTableType, spaceCode]
    );

    const hasTimeStats = useMemo(
        () => {
            if (!dataSource || !('avgTimeSpent' in dataSource)) {
                return false;
            }
            return (
                dataSource.avgTimeSpent !== undefined
                || dataSource.maxTimeSpent !== undefined
                || dataSource.medianTimeSpent !== undefined
                || dataSource.minTimeSpent !== undefined
            );
        },
        [dataSource]
    );

    return (
        <div style={{margin: '20px 0'}}>
            {hasTimeStats && (
                <TimeStatsChart
                    dataSource={dataSource}
                    loading={loading}
                    detailTableType={detailTableType}
                />
            )}
            <Typography.Title level={5} className={marginBottom(10)}>
                任务列表
            </Typography.Title>
            <Table
                bordered
                scroll={{x: 'max-content'}}
                columns={columns}
                dataSource={dataSource?.taskList || []}
                loading={loading}
                rowKey="index"
                pagination={{pageSize: 100}}
                expandable={{
                    expandedRowRender: record => <ExpandedTable taskID={record.taskID} />,
                }}
            />
        </div>
    );
};

export default DetailTable;
