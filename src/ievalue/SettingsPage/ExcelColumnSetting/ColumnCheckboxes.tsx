import {Checkbox, Divider, Descriptions} from 'antd';
import styled from '@emotion/styled';
import {Text} from '@panda-design/components';
import {ColumnData} from '@/api/ievalue/space';
import {TaskStageEnum, TaskStagMap} from '@/constants/ievalue/task';

const CheckboxGroup = styled.div`
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px 0;
`;

const CheckboxItem = styled.div`
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
`;

const CheckboxHeader = styled.div`
    display: flex;
    align-items: center;
`;

const CheckboxDetails = styled.div`
    margin-left: 22px;
    padding: 4px 0;
`;


const getRangeText = (range: string) => {
    return TaskStagMap[range as TaskStageEnum] || range;
};

interface Props {
    category: 'taskColumn' | 'modelColumn';
    data: ColumnData | null;
    onCheckboxChange: (category: string, columnCode: string, checked: boolean) => void;
    onSelectAll: (category: string, checked: boolean) => void;
}

export const ColumnCheckboxes = ({category, data, onCheckboxChange, onSelectAll}: Props) => {
    if (!data) {
        return null;
    }

    const isAllSelected = data[category]?.every(item => item.output);
    const selectedCount = data[category]?.filter(item => item.output).length;
    const isIndeterminate = selectedCount > 0 && selectedCount < data[category].length;

    return (
        <CheckboxGroup>
            <Checkbox
                checked={isAllSelected}
                indeterminate={isIndeterminate}
                onChange={e => onSelectAll(category, e.target.checked)}
            >
                全选
            </Checkbox>
            <Divider style={{margin: '5px 0'}} />
            {data[category]?.map(item => (
                <CheckboxItem key={item.columnCode}>
                    <CheckboxHeader>
                        <Checkbox
                            checked={item.output}
                            onChange={e => onCheckboxChange(category, item.columnCode, e.target.checked)}
                        >
                            {item.columnName}
                        </Checkbox>
                        <Text>
                            ( {item.columnCode} )
                        </Text>
                    </CheckboxHeader>
                    <CheckboxDetails>
                        <Descriptions size="small" column={3}>
                            <Descriptions.Item label="适用范围">
                                {item.range.map(getRangeText).join(', ')}
                            </Descriptions.Item>
                            <Descriptions.Item label="描述">{item.desc}</Descriptions.Item>

                        </Descriptions>
                    </CheckboxDetails>
                </CheckboxItem>
            ))}
        </CheckboxGroup>
    );
};
