import styled from '@emotion/styled';
import {Radio, RadioChangeEvent, Space, Table, Tag, Typography} from 'antd';
import {ColumnProps} from 'antd/es/table';
import {useRequest} from 'huse';
import {useCallback, useState} from 'react';
import {apiStrategyList, StagetegyItem} from '@/api/ievalue/case';
import {PandaEmpty} from '@/design/PandaEmpty';
import {
    StrategyGenreEnum,
    StrategyGenreMap,
    StrategyRunningModeEnum,
} from '@/constants/ievalue/evaluate';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {isRuntimeOnline} from '@/utils/ievalue';
import {PaddingTopLayout} from '../BasicSetting';
import {DeleteStrategyButton} from './DeleteStrategyButton';
import {UpdateStrategyButton} from './UpdateStrategyButton';
import OptimizeStatusButton from './components/OptimizeStatusButton';
import ShowOptimizeResultModal from './components/ShowOptimizeResultModal';
import GenerateEvaluatePolicyButton from './components/GenerateEvaluatePolicyButton';
import NewGenerateEvaluatePolicyButton from './components/NewGenerateEvaluatePolicyButton';

export const FullWidthSpace = styled(Space)`
    padding: 16px 0;
    width: 100%;
`;

function NoModel() {
    return (
        <div
            style={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            }}
        >
            <PandaEmpty type="data" description={<>你还没有评估策略</>} />
        </div>
    );
}

export const StrategySetting = () => {
    const spaceCode = useSpaceCodeSafe();
    const [genre, setGenre] = useState(StrategyGenreEnum.MANUAL);
    const {
        refresh,
        data: strategyList,
        pending,
    } = useRequest(apiStrategyList, {
        spaceCode,
        genre,
    });
    const handleGenreChange = useCallback(
        (e: RadioChangeEvent) => {
            setGenre(e.target.value);
        },
        []
    );

    const handleRefresh = useCallback(
        async () => {
            refresh();
        },
        [refresh]
    );

    const columns: Array<ColumnProps<StagetegyItem>> = [
        {
            title: '策略名称',
            key: 'name',
            dataIndex: 'name',
            width: 200,
            ellipsis: true,
        },
        {
            title: '评估维度',
            key: 'metric',
            dataIndex: 'metric',
            render: (_, record) => {
                return (
                    <>
                        {record.metric
                            && record.metric.length > 0
                            && record.metric.map(item => {
                                return (
                                    <Tag key={item.metric}>{item.metric}</Tag>
                                );
                            })}
                    </>
                );
            },
        },
        {
            title: '操作',
            dataIndex: 'option',
            key: 'option',
            width: genre === StrategyGenreEnum.AUTO ? 210 : 140,
            render: (_, record) => {
                return (
                    <Space size={2}>
                        {record?.runningMode
                            === StrategyRunningModeEnum.YIYANDIY && (
                            <OptimizeStatusButton
                                policyID={record.ID}
                                metricList={record.metric}
                                status={record.status}
                                spaceCode={record.spaceCode}
                                onFinish={refresh}
                            />
                        )}
                        <UpdateStrategyButton
                            strategyItem={record}
                            onRefresh={handleRefresh}
                            genre={genre}
                        />
                        <DeleteStrategyButton
                            strategyItem={record}
                            onRefresh={refresh}
                        />
                    </Space>
                );
            },
        },
    ];
    return (
        <PaddingTopLayout>
            <Typography.Title level={4}>评估策略</Typography.Title>
            <FullWidthSpace direction="vertical">
                <Radio.Group
                    onChange={handleGenreChange}
                    value={genre}
                    style={{marginBottom: 8}}
                >
                    <Radio.Button value={StrategyGenreEnum.MANUAL}>
                        {StrategyGenreMap[StrategyGenreEnum.MANUAL]}
                    </Radio.Button>
                    <Radio.Button value={StrategyGenreEnum.AUTO}>
                        {StrategyGenreMap[StrategyGenreEnum.AUTO]}
                    </Radio.Button>
                </Radio.Group>
                <Space>
                    <UpdateStrategyButton
                        onRefresh={handleRefresh}
                        genre={genre}
                    />
                    {genre === StrategyGenreEnum.AUTO && (
                        <>
                            <GenerateEvaluatePolicyButton
                                onFinish={handleRefresh}
                            />
                            {!isRuntimeOnline() && <NewGenerateEvaluatePolicyButton onFinish={handleRefresh} />}
                        </>
                    )}
                </Space>
                {strategyList?.length === 0 && !pending ? (
                    <NoModel />
                ) : (
                    <Table<StagetegyItem>
                        rowKey="ID"
                        columns={columns}
                        dataSource={strategyList}
                        loading={pending}
                        pagination={false}
                        size="small"
                    />
                )}
                <ShowOptimizeResultModal />
            </FullWidthSpace>
        </PaddingTopLayout>
    );
};
