import {Checkbox, Form} from 'antd';
import {
    StrategyMetricGenreEnum,
} from '@/constants/ievalue/evaluate';
import {Help} from '@/design/Help';

const NotRequiredFormItem = () => {
    const form = Form.useFormInstance();
    const metricGenre = Form.useWatch('genre', form);

    return metricGenre === StrategyMetricGenreEnum.MANUAL
        ? (
            <Form.Item name="notRequired" valuePropName="checked">
                <Checkbox>
                    非必填
                    <Help>
                        当前维度是否必填。勾选后任务执行过程中将不再校验当前维度是否已经完成评估。
                    </Help>
                </Checkbox>
            </Form.Item>
        ) : (
            <></>
        );
};

export default NotRequiredFormItem;
