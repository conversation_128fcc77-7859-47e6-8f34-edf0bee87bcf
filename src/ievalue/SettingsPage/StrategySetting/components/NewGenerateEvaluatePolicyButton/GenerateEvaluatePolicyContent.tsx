import {Alert, Form, FormInstance, Input, Select, Typography} from 'antd';
import {useEffect, useMemo, useState} from 'react';
import {css} from '@emotion/css';
import {StrategyRunningModeEnum} from '@/constants/ievalue/evaluate';
import QianFanModelSelectParam from '../OptimizeEvaluateToolButton/QianFanModelSelectParam';
import ImportSampleFileButton from './ImportSampleFileButton';
import ColMetricFormList from './ColMetricFormList';

interface ImportMetricContentProps {
    form: FormInstance;
    onFinish: (value: any) => void;
    policyID: number;
}

const formCss = css`
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;
    .ant-5-form-css-var {
        --ant-5-form-item-margin-bottom: 10px !important;
    }
    .ant-5-alert {
        font-size: 12px;
        margin-bottom: 10px;
        --ant-5-alert-default-padding: 4px 8px !important;
    }
    ::-webkit-scrollbar {
        display: none;
    }
`;

const GenerateEvaluatePolicyContent = ({
    form,
    onFinish,
    policyID,
}: ImportMetricContentProps) => {
    const [mapList, setMapList] = useState<string[]>([]);

    const colMapList = useMemo(
        () => {
            return [
                {
                    label: 'Query',
                    name: 'colQuery',
                    key: 'colQuery',
                    required: true,
                },
                {
                    label: 'Response',
                    name: 'colResponse',
                    key: 'colResponse',
                    required: true,
                },
            ];
        },
        []
    );

    const mapSelectOptions = useMemo(
        () =>
            mapList?.map(item => ({
                label: item,
                value: item,
            })),
        [mapList]
    );

    useEffect(
        () => {
            if (mapList) {
                form.resetFields(colMapList.map(item => ['task', item.name]));
            }
        },
        [colMapList, form, mapList]
    );

    return (
        <Form
            form={form}
            onFinish={onFinish}
            labelCol={{span: 3}}
            className={formCss}
            labelAlign="left"
            initialValues={{
                task: {
                    taskVer: StrategyRunningModeEnum.YIYANSAMPLE,
                    policyID,
                    colMetric: [{}],
                },
            }}
            onValuesChange={(changedValues: any) => {
                if (changedValues?.task?.colMetric) {
                    form.validateFields();
                }
            }}
        >
            <Alert
                message={
                    <>
                        请添加您的样本数据，样本数据至少包含
                        <strong>5条样本</strong>，样本数据总量
                        <strong>不超过100条</strong>
                        ，建议保证每个分值都存在对应的样本
                    </>
                }
                type="info"
                showIcon
            />
            {!!policyID && (
                <Form.Item name={['task', 'policyID']} hidden>
                    <Input />
                </Form.Item>
            )}
            <Form.Item name={['task', 'taskVer']} hidden>
                <Input />
            </Form.Item>
            <Form.Item
                label="策略名称"
                name={['policy', 'name']}
                rules={[{required: true, message: '请输入策略名称'}]}
            >
                <Input placeholder="请输入策略名称，如XX场景的评估策略" />
            </Form.Item>
            <Form.Item
                label="样本数据"
                name={['task', 'sampleURL']}
                rules={[{required: true, message: '请选择或者上传数据集'}]}
            >
                <ImportSampleFileButton setMapList={setMapList} />
            </Form.Item>
            <Form.Item
                label="裁判员模型"
                name={['task', 'evalModelID']}
                rules={[{required: true, message: '请选择评估模型'}]}
                tooltip="您选择的评估模型，将用于评估策略生成过程中的自我评估验证。建议您选择和实际评估场景一致的模型。"
            >
                <QianFanModelSelectParam />
            </Form.Item>
            <Typography.Text strong>数据列配置</Typography.Text>
            {colMapList.map(item => (
                <Form.Item
                    label={
                        <Typography.Text ellipsis={{tooltip: item.label}}>
                            {item.label}
                        </Typography.Text>
                    }
                    name={['task', item.name]}
                    key={item.key}
                    rules={[
                        {
                            required: item?.required,
                            message: `请选择${item.label}`,
                        },
                    ]}
                >
                    <Select
                        allowClear
                        options={mapSelectOptions}
                        placeholder={`请选择样本数据中的${item.label}`}
                    />
                </Form.Item>
            ))}
            <ColMetricFormList mapSelectOptions={mapSelectOptions} />
        </Form>
    );
};

export default GenerateEvaluatePolicyContent;
