import {
    CheckOutlined,
    CloudUploadOutlined,
    InboxOutlined,
} from '@ant-design/icons';
import {message, Modal, Button} from '@panda-design/components';
import {Space, Spin, Typography, Upload} from 'antd';
import {useBoolean} from 'huse';
import {useCallback, useState} from 'react';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {apiFileExcelUpload, FileExcelUploadRes} from '@/api/ievalue/dataset';
import {FormItemParamProps} from '@/types/common/form';

const One_MB = 1024 * 1024;
interface ImportZipModalProps {
    open: boolean;
    onFinish: (fileData: FileExcelUploadRes, fileName: string) => void;
    onClose: () => void;
}

interface SampleURLFormParamProps extends FormItemParamProps<string> {
    setMapList: (value: string[]) => void;
}

const ImportSampleFileModal = ({
    open,
    onClose,
    onFinish,
}: ImportZipModalProps) => {
    const [uploading, setUploading] = useState(false);

    const handleFileChange = async ({file}: any) => {
        if (file?.status === 'removed') {
            return;
        }
        setUploading(true);
        if (file.size < One_MB) {
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('limit', '30');
                const fileData = await apiFileExcelUpload(formData);
                onFinish(fileData, file?.name || '');
            } catch (error) {
                /* empty */
            }
        } else {
            message.error('文件过大，请上传小于1MB的文件！');
        }
        setUploading(false);
    };

    return (
        <Modal
            title="文件上传"
            open={open}
            destroyOnClose
            centered
            onCancel={onClose}
            width={800}
            footer={null}
            maskClosable={false}
        >
            <Spin spinning={uploading} tip="正在上传文件...">
                <Upload.Dragger
                    maxCount={1}
                    accept=".xls, .xlsx"
                    onChange={handleFileChange}
                    beforeUpload={() => false}
                    fileList={[]}
                >
                    <InboxOutlined
                        style={{fontSize: '48px', color: '#1677ff'}}
                    />
                    <p
                        className="ant-upload-text"
                        style={{fontSize: '14px', color: '#1677ff'}}
                    >
                        点击上传，或者拖拽上传
                    </p>
                    <p className="ant-upload-hint">
                        文件仅支持excel格式，文件大小不超过1MB，只能上传一个，多次上传会覆盖
                    </p>
                </Upload.Dragger>
            </Spin>
        </Modal>
    );
};

const ImportSampleFileButton = ({
    onChange,
    setMapList,
}: SampleURLFormParamProps) => {
    const [open, {on, off}] = useBoolean(false);
    const [displayName, setDisplayName] = useState('');

    const handleFinish = useCallback(
        async (fileData: FileExcelUploadRes, fileName: string) => {
            try {
                message.success('文件上传成功');
                onChange(fileData.fileURL);
                setMapList(fileData.headers);
                setDisplayName(fileName);
                off();
            } catch (error) {
                /* empty */
            }
        },
        [off, onChange, setMapList]
    );

    return (
        <>
            <Space>
                <Button
                    icon={<CloudUploadOutlined />}
                    type="primary"
                    size="small"
                    onClick={on}
                >
                    文件上传
                </Button>
                {displayName && (
                    <>
                        <Typography.Text
                            ellipsis={{tooltip: displayName}}
                            style={{maxWidth: 300}}
                        >
                            {displayName}
                        </Typography.Text>
                        <CheckOutlined style={{color: 'green'}} />
                    </>
                )}
            </Space>
            <CustomBoundary.Loading>
                {open && (
                    <ImportSampleFileModal
                        open={open}
                        onClose={off}
                        onFinish={handleFinish}
                    />
                )}
            </CustomBoundary.Loading>
        </>
    );
};

export default ImportSampleFileButton;
