import {Button, ButtonProps, Modal, message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {Form} from 'antd';
import {useCallback, useState} from 'react';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FormOnFinish} from '@/utils/ievalue/typing';
import {apiEvaluatePolicyGenerate} from '@/api/ievalue/evaluate';
import GenerateEvaluatePolicyContent from './GenerateEvaluatePolicyContent';
export interface GenerateEvaluatePolicyProps {
    onFinish: () => Promise<void>;
    policyID?: number;
}

type GenerateEvaluatePolicyButtonProps = GenerateEvaluatePolicyProps &
    ButtonProps;

const NewGenerateEvaluatePolicyButton = ({
    onFinish,
    policyID,
    ...props
}: GenerateEvaluatePolicyButtonProps) => {
    const [open, {off, on}] = useBoolean();
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    const handleOk = useCallback(
        () => {
            form.submit();
        },
        [form]
    );

    const handleOpen = useCallback(
        () => {
            form.resetFields();
            on();
        },
        [form, on]
    );

    const handleFinish: FormOnFinish<any> = useCallback(
        async formData => {
            setLoading(true);
            try {
                await apiEvaluatePolicyGenerate(formData);
                await onFinish();
                off();
            } catch (e) {
                if (e instanceof Error) {
                    message.error(`生成新的维度失败，${e.message ?? ''}`);
                }
            } finally {
                setLoading(false);
            }
        },
        [off, onFinish]
    );

    return (
        <>
            <Button onClick={handleOpen} {...props}>
                {policyID ? '生成新的维度' : '生成评估策略'}
            </Button>
            {open && (
                <Modal
                    title="生成评估策略"
                    width="1200px"
                    open={open}
                    onCancel={off}
                    onOk={handleOk}
                    maskClosable={false}
                    okText="生成"
                    okButtonProps={{loading}}
                >
                    <CustomBoundary.Loading>
                        <GenerateEvaluatePolicyContent
                            onFinish={handleFinish}
                            policyID={policyID}
                            form={form}
                        />
                    </CustomBoundary.Loading>
                </Modal>
            )}
        </>
    );
};

export default NewGenerateEvaluatePolicyButton;
