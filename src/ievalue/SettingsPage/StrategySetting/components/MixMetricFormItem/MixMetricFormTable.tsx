/* eslint-disable max-lines */
import {DeleteOutlined, HolderOutlined, PlusOutlined} from '@ant-design/icons';
import {
    Button,
    Form,
    Popconfirm,
    Space,
    Table,
    TableColumnsType,
    Typography,
} from 'antd';
import {
    useCallback,
    useContext,
    useMemo,
    HTMLAttributes,
    createContext,
    FC,
    CSSProperties,
} from 'react';
import type {DragEndEvent} from '@dnd-kit/core';
import {DndContext} from '@dnd-kit/core';
import type {SyntheticListenerMap} from '@dnd-kit/core/dist/hooks/utilities';
import {restrictToVerticalAxis} from '@dnd-kit/modifiers';
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {MetricItem} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    StrategyMetricGenreEnum,
    StrategyMetricGenreMap,
    StrategyMetricSelectTypeEnum,
    StrategyMetricSelectTypeMap,
    StrategyRunningModeEnum,
} from '@/constants/ievalue/evaluate';
import {FormItemParamProps} from '@/types/common/form';
import {apiEvaluatePolicyInfo} from '@/api/ievalue/model';
import ChoicesPreview from '../ChoicesPreview';
import ImportEvaluateToolButton from '../ImportEvaluateToolButton';
import ImportMetricButton from '../ImportMetricButton';
import {UpdateMetricButton} from '../UpdateMetricButton';
import OptimizeTaskListButton from '../OptimizeTaskListButton';
import NewGenerateEvaluatePolicyButton from '../NewGenerateEvaluatePolicyButton';
import {MixMetricFormItemProps} from '.';

interface RowContextProps {
    setActivatorNodeRef?: (element: HTMLElement | null) => void;
    listeners?: SyntheticListenerMap;
}

interface RowProps extends HTMLAttributes<HTMLTableRowElement> {
    'data-row-key': string;
}
const RowContext = createContext<RowContextProps>({});

const DragHandle: FC = () => {
    const {setActivatorNodeRef, listeners} = useContext(RowContext);
    return (
        <Button
            type="text"
            size="small"
            icon={<HolderOutlined />}
            style={{cursor: 'move'}}
            ref={setActivatorNodeRef}
            {...listeners}
        />
    );
};

const Row: FC<RowProps> = props => {
    const {
        attributes,
        listeners,
        setNodeRef,
        setActivatorNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({id: props['data-row-key']});

    const style: CSSProperties = {
        ...props.style,
        transform: CSS.Translate.toString(transform),
        transition,
        ...(isDragging ? {position: 'relative', zIndex: 9999} : {}),
    };

    const contextValue = useMemo<RowContextProps>(
        () => ({setActivatorNodeRef, listeners}),
        [setActivatorNodeRef, listeners]
    );

    return (
        <RowContext.Provider value={contextValue}>
            <tr {...props} ref={setNodeRef} style={style} {...attributes} />
        </RowContext.Provider>
    );
};

type EvaluateMetricFormItemProps = FormItemParamProps<MetricItem[]> &
    MixMetricFormItemProps;

const MixMetricFormTable = ({
    value,
    onChange,
    runningMode,
    strategyGenre,
}: EvaluateMetricFormItemProps) => {
    const form = Form.useFormInstance();
    const policyID = Form.useWatch('ID');
    // const status = Form.useWatch('status');
    const add = useCallback(
        (item: MetricItem) => {
            if (value?.some(i => i.metric === item.metric)) {
                throw new Error('维度名称或维度标识已存在, 请勿重复添加');
            }
            onChange?.([...(value ?? []), item]);
        },
        [onChange, value]
    );

    const batchAdd = useCallback(
        (items: MetricItem[]) => {
            if (
                value?.some(i =>
                    items.find(item => i.metric === item.metric)
                )
            ) {
                throw new Error('维度名称或维度标识已存在, 请勿重复添加');
            }
            onChange?.([...(value ?? []), ...items]);
        },
        [onChange, value]
    );

    const update = useCallback(
        (item: MetricItem, updateMetric?: string) => {
            if (
                value
                    ?.filter(i => i.metric !== updateMetric)
                    ?.some(i => i.metric === item.metric)
            ) {
                throw new Error('维度标识已存在, 请勿重复添加');
            }
            const resultValue = [...(value ?? [])];
            const index = resultValue?.findIndex(
                i => i.metric === updateMetric
            );
            resultValue?.splice(index, 1, item);
            onChange?.(resultValue);
        },
        [onChange, value]
    );

    const remove = useCallback(
        (item: MetricItem) => {
            const list = value?.filter(i => i.metric !== item.metric) ?? [];
            onChange?.(list);
        },
        [onChange, value]
    );

    const columns: TableColumnsType<MetricItem> = [
        {
            key: 'sort',
            align: 'center',
            width: 40,
            render: () => <DragHandle />,
        },
        {
            title: '维度标识',
            dataIndex: 'metric',
            key: 'metric',
            ellipsis: true,
        },
        {
            title: '取值选项',
            dataIndex: 'choices',
            key: 'choices',
            width: 80,
            hidden: [
                StrategyRunningModeEnum.YIYANDIY,
                StrategyRunningModeEnum.YIYANSAMPLE,
            ].includes(runningMode),
            render(_, record) {
                return <ChoicesPreview record={record} />;
            },
        },
        {
            title: '维度初始标准',
            dataIndex: 'note',
            key: 'note',
            width: 350,
            hidden: runningMode !== StrategyRunningModeEnum.YIYANSAMPLE,
            ellipsis: true,
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 80,
            hidden: runningMode !== StrategyRunningModeEnum.YIYANSAMPLE,
        },
        {
            title: '维度类型',
            dataIndex: 'category',
            key: 'category',
            width: 300,
            hidden: runningMode !== StrategyRunningModeEnum.YIYANDIY,
        },
        {
            title: '选择类型',
            dataIndex: 'selectType',
            key: 'selectType',
            width: 80,
            hidden: runningMode !== StrategyRunningModeEnum.MANUAL,
            render: (type: StrategyMetricSelectTypeEnum) => {
                return StrategyMetricSelectTypeMap[type];
            },
        },
        {
            title: '维度描述',
            dataIndex: 'explain',
            key: 'explain',
            hidden: [
                StrategyRunningModeEnum.YIYANDIY,
                StrategyRunningModeEnum.YIYANSAMPLE,
            ].includes(runningMode),
            width: 250,
            render: (explain: string) => {
                return (
                    <Typography.Text
                        style={{maxWidth: 240}}
                        ellipsis={{tooltip: explain}}
                    >
                        {explain}
                    </Typography.Text>
                );
            },
        },
        {
            title: '维度类型',
            dataIndex: 'genre',
            key: 'genre',
            width: 100,
            render: genre => {
                return StrategyMetricGenreMap[genre as StrategyMetricGenreEnum];
            },
        },
        {
            title: '操作',
            dataIndex: 'option',
            key: 'option',
            width: 140,
            render: (_, record) => {
                return (
                    <FlexLayout gap={4}>
                        <UpdateMetricButton
                            runningMode={runningMode}
                            onFinish={update}
                            metricItem={record}
                        />
                        <Popconfirm
                            title="删除维度"
                            placement="topRight"
                            description="确定删除维度吗？"
                            okText="删除"
                            cancelText="取消"
                            onConfirm={() => remove(record)}
                        >
                            <Button type="text" icon={<DeleteOutlined />}>
                                删除
                            </Button>
                        </Popconfirm>
                    </FlexLayout>
                );
            },
        },
    ];
    const onDragEnd = ({active, over}: DragEndEvent) => {
        if (active.id !== over?.id) {
            const activeIndex = value.findIndex(
                record => record.metric === active?.id
            );
            const overIndex = value.findIndex(
                record => record.metric === over?.id
            );
            onChange(arrayMove(value, activeIndex, overIndex));
        }
    };

    const handleOptFinish = useCallback(
        async () => {
            const policyInfo = await apiEvaluatePolicyInfo({
                ID: policyID,
            });
            form.setFieldsValue({
                status: policyInfo?.status,
                metric: policyInfo?.metric ?? [],
            });
        },
        [form, policyID]
    );

    return (
        <div>
            <DndContext
                modifiers={[restrictToVerticalAxis]}
                onDragEnd={onDragEnd}
            >
                <SortableContext
                    items={value?.map(i => i.metric)}
                    strategy={verticalListSortingStrategy}
                >
                    <Table
                        size="small"
                        pagination={false}
                        rowKey="metric"
                        components={{body: {row: Row}}}
                        columns={columns}
                        dataSource={value}
                    />
                </SortableContext>
            </DndContext>
            <Space>
                {runningMode === StrategyRunningModeEnum.YIYANSAMPLE ? (
                    policyID && <NewGenerateEvaluatePolicyButton
                        type="text"
                        icon={<PlusOutlined />}
                        onFinish={handleOptFinish}
                        policyID={policyID}
                    />
                ) : (
                    <UpdateMetricButton
                        runningMode={runningMode}
                        onFinish={add}
                    />
                )}
                {runningMode === StrategyRunningModeEnum.YIYANDIY ? (
                    <>
                        <ImportEvaluateToolButton onFinish={batchAdd} />
                        {policyID && (
                            <>
                                {/* <OptimizeStatusButton
                                    policyID={policyID}
                                    metricList={value}
                                    status={status}
                                    onFinish={handleOptFinish}
                                /> */}
                                <OptimizeTaskListButton
                                    policyID={policyID}
                                    onFinish={handleOptFinish}
                                />
                            </>
                        )}
                    </>
                ) : (
                    <ImportMetricButton
                        runningMode={runningMode}
                        onFinish={add}
                        strategyGenre={strategyGenre}
                    />
                )}
            </Space>
        </div>
    );
};

export default MixMetricFormTable;
