import {css} from '@emotion/css';
import {Divider, Flex, Space, Typography} from 'antd';
import styled from '@emotion/styled';
import {HomeFunctionsListItem} from '@/api/ievalue/home';
import FavoriteStar from './FavoriteStar';
import {TagslPanel} from './TagslPanel';
import {DetailButton} from './DetailButton';
import {ExperienceButton} from './ExperienceButton';
import {FirstWordIcon} from './FirstWordIcon';
export const hiddenFavoriteStar = css`
    opacity: 0;
`;
const Container = styled.div`
    border-radius: 4px;
    background: #fafcfc;
    border: 1px solid #d9d9d9;
    padding: 16px;
    margin: 6px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    :hover {
        border: 1px solid #0080ff;
        .${hiddenFavoriteStar} {
            opacity: 1 !important;
        }
    }
`;

export const EntryCard = ({
    item,
    index,
    refresh,
}: {
    item: HomeFunctionsListItem;
    index: number;
    refresh: () => void;
}) => {
    return (
        <Container>
            <Flex justify="space-between" align="center">
                <Flex gap={8} style={{flex: 1}}>
                    <FirstWordIcon name={item.name} index={index} />
                    <Typography.Title
                        level={5}
                        style={{flex: 1}}
                        ellipsis={{tooltip: item.name, rows: 1}}
                    >
                        {item.name}
                    </Typography.Title>
                </Flex>
                <FavoriteStar
                    className={hiddenFavoriteStar}
                    functionID={item.id}
                    refresh={refresh}
                    isFavorite={item.isFavorite}
                />
            </Flex>
            <TagslPanel tags={item.tags} />

            <Typography.Paragraph
                ellipsis={{tooltip: item.desc, rows: 1}}
                style={{fontSize: 12, color: '#545454', marginBottom: 0}}
            >
                {item.desc}
            </Typography.Paragraph>
            <Divider style={{margin: 0}} />
            <Flex justify="space-between" align="center" gap={16}>
                <Typography.Text
                    style={{flex: 1}}
                    ellipsis={{tooltip: item.provider}}
                >
                    提供方：{item.provider}
                </Typography.Text>
                <Space size={4}>
                    <DetailButton info={item} refresh={refresh} />
                    <ExperienceButton info={item} />
                </Space>
            </Flex>
        </Container>
    );
};
