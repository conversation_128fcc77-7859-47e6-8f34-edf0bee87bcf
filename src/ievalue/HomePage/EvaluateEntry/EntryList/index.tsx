import {List} from 'antd';
import {useCallback} from 'react';
import {HomeFunctionsListItem} from '@/api/ievalue/home';
import {PandaEmpty} from '@/design/PandaEmpty';
import {EntryCard} from './EntryCard';

export const EntryList = ({
    dataSource,
    refresh,
    type,
}: {
    dataSource: HomeFunctionsListItem[];
    refresh: () => void;
    type: string;
}) => {
    const itemRender = useCallback(
        (item: HomeFunctionsListItem, index: number) => {
            return (
                <div key={type + item.id} style={{padding: 0, margin: 0}}>
                    <EntryCard item={item} refresh={refresh} index={index} />
                </div>
            );
        },
        [refresh, type]
    );
    return (
        <List
            grid={{
                gutter: 12,
                column: 4,
                xs: 1,
                sm: 1,
                md: 1,
                lg: 2,
                xl: 3,
                xxl: 4,
            }}
            locale={{
                emptyText: (
                    <PandaEmpty
                        type="data"
                        description="您还没收藏常用场景，快去收藏一个吧！"
                        style={{height: 150}}
                    />
                ),
            }}
            dataSource={dataSource}
            renderItem={itemRender}
        />
    );
};
