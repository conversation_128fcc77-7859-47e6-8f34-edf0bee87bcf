import Label from '@baidu/devops-design/es/components/Label';
import {css} from '@emotion/css';
import {Flex, Tooltip} from 'antd';
import {FunctionsFilterEvaluateItem} from '@/api/ievalue/home';

const labelStyle = css`
    &:not([aria-disabled]):hover::after{
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        pointer-events: none;
        // 添加圆角效果，否则 hover 效果会显示方形边框
        border-radius: 12px;
        background: rgba(0, 0, 0, .02);
    }
`;

export const SelectPanel = ({
    value,
    onChange,
    options,
}: {
    value?: number;
    onChange?: (value: number) => void;
    options: FunctionsFilterEvaluateItem[];
}) => {
    return (
        <Flex
            gap={10}
            wrap="wrap"
            className={css`
                padding: 0 16px;
                position: relative;
                ::before {
                    content: "";
                    position: absolute;
                    top: 4px;
                    left: -1px;
                    width: 1px;
                    height: 16px;
                    background-color: #e5e5e5;
                }
            `}
        >
            {options?.map((option: any) => (
                <Tooltip key={option.id} trigger={['click']} title={option.isActive ? undefined : '开发中，敬请期待'}>
                    <Label
                        key={option.id}
                        className={labelStyle}
                        style={{borderRadius: '12px', cursor: 'pointer'}}
                        textColor={value === option.id ? '#0080FF' : (option.isActive ? '#545454' : '#aaa')}
                        backgroundColor={
                            value === option.id ? '#E5F2FF' : (option.isActive ? '#F2F2F2' : '#F6F6F6')
                        }
                        onClick={() => {
                            if (option.isActive) {
                                onChange(option.id);
                            }
                        }}
                        name={option.name}
                    />
                </Tooltip>
            ))}
        </Flex>
    );
};
