import {css} from '@emotion/css';
import {Button} from '@panda-design/components';
import {Flex} from 'antd';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {openWithProps} from '@/regions/ievalue/prompt/openRegion';
import {FeedbackLink} from '@/links/ievalue/home';
import {AccessAbilityModal, accessAbilityKey} from './AccessAbilityModal';

const containerCss = css`
    border: 1px solid #0047b3;
    border-radius: 6px;
    background-color: #fff;
    height: 68px;
    width: 120px;
`;
const lineCss = css`
    height: 1px;
    width: calc(100% - 20px);
    background-color: #0080FF;
    margin: 0 10px;
`;
export const AbilityEntry = () => {
    const onShowAccessAbility = useCallback(
        () => {
            openWithProps(accessAbilityKey, {});
        },
        []
    );
    const navigate = useNavigate();
    const onFeedback = useCallback(
        () => {
            const url = FeedbackLink.toUrl();
            navigate(url);
        },
        [navigate]
    );

    return (
        <Flex
            vertical
            className={containerCss}
            align="center"
            justify="space-around"
        >
            <Button
                type="link"
                size="small"
                style={{width: '100%', flex: 1}}
                onClick={onFeedback}
            >
                我有需求
            </Button>
            <div className={lineCss} />
            <Button
                type="link"
                size="small"
                style={{width: '100%', flex: 1}}
                onClick={onShowAccessAbility}
            > 我要接入
            </Button>
            <AccessAbilityModal />
        </Flex>
    );
};
