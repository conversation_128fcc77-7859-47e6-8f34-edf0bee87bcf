import styled from '@emotion/styled';
import {Tabs} from 'antd';
import {useMemo, useState} from 'react';
import {message} from '@panda-design/components';
import {colors} from '@/constants/colors';
import {gradientTabsCss} from '@/styles/components';
import {LabFeatures} from '../LabFeatures';
import {EvaluateEntry} from '../EvaluateEntry';
const Container = styled.div`
    background: ${colors.white};
    border-radius: 6px;
    margin-top: 10px;
    padding: 0 20px 18px 20px;
`;

export const TabsPanel = () => {
    const items = useMemo(
        () => [{
            key: 'evaluate',
            label: '评估能力',
            children: <EvaluateEntry />,
        }, {
            key: 'prompt',
            label: 'Prompt能力',
            children: <LabFeatures />,
        }, {
            key: 'datasource',
            label: '数据能力',
            children: '开发中，敬请期待！',
        }],
        []
    );
    const [activeKey, setActiveKey] = useState<string>();

    return (
        <Container>
            <Tabs
                items={items}
                className={gradientTabsCss}
                activeKey={activeKey}
                onChange={key => {
                    if (key === 'datasource') {
                        message.warning('开发中，敬请期待！');
                    } else {
                        setActiveKey(key);
                    }
                }}
            />
        </Container>
    );
};
