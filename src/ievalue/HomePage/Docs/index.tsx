import styled from '@emotion/styled';
import {Typography} from 'antd';
import {useEffect} from 'react';
import {colors} from '@/constants/colors';
import {
    DocLink,
    QALink,
    FunctionCallExampleLink,
    IEValueDocLink,
} from '@/links/ievalueExternal';
import {HomeDocument} from '@/api/ievalue/home';
import {useHomeDocumentRegion, loadHomeDocumentRegion} from '@/regions/ievalue/home/<USER>';
import {createLink} from '@/links/createLink';
import {APP_NAME} from '@/constants/app';
import {ListItem} from './ListItem';

const Container = styled.div`
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
`;

const CardLayout = styled.div`
    padding: 20px;
    border-radius: 6px;
    background: ${colors.white};
`;


const TitleContainer = styled.div`
    display: flex;
    justify-content: space-between;
`;

export const Docs = () => {
    const data = useHomeDocumentRegion() as HomeDocument;
    useEffect(
        () => {
            loadHomeDocumentRegion();
        },
        []
    );
    return (
        <Container>
            <CardLayout>
                <TitleContainer>
                    <Typography.Title level={4}>使用手册</Typography.Title>
                    {APP_NAME === 'ievalue'
                        ? <IEValueDocLink style={{color: '#0080FF'}}>更多</IEValueDocLink>
                        : <DocLink style={{color: '#0080FF'}}>更多</DocLink>}
                </TitleContainer>
                {data?.helpCenter?.map(item => (
                    <ListItem
                        key={item.id}
                        text={item.name}
                        Link={createLink(item.url)}
                        linkColor={'#0080FF'}
                    />
                ))}
            </CardLayout>
            <CardLayout>
                <TitleContainer>
                    <Typography.Title level={4}>常见问题</Typography.Title>
                    <QALink>更多</QALink>
                </TitleContainer>
                <ListItem
                    text="创建的评估任务支持删除或者编辑吗？"
                    answer="点击评估任务的详情页右上角的三个点，可以对当前任务进行删除或编辑操作。删除操作需要为任务的创建人或空间管理员，编辑操作当前只支持编辑任务名称与描述。"
                />
                <ListItem
                    text="Prompt开发页面模型参数中的Functions如何填写？"
                    answer={<>必须是JSON格式，详细格式可参考<FunctionCallExampleLink>示例</FunctionCallExampleLink></>}
                />
                <ListItem
                    text="运行结果提示“Access token invaild or no longer vaild”是什么原因？"
                    answer="prompt对应模型的apikey缺失或有误，请检查修改。"
                />
                <ListItem
                    text="iEvalue平台Prompt快速评估页面，选择的对比版本个数是否有限制？"
                    answer="版本个数没有限制，但添加评估用例的时候，下面展示的条数限制10条，不过可以上传评估集进行更大数据量的评估。"
                />
                <ListItem
                    text="Prompt关联iCafe时，为什么iCafe空间查询不到？"
                    answer="iCafe空间下拉列表展示的是当前登录人有权限的空间，展示的是iCafe空间名称，需要通过空间名称搜索。"
                />
            </CardLayout>
        </Container>
    );
};
