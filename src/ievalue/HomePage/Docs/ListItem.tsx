import {ComponentType, ReactNode, useReducer} from 'react';
import styled from '@emotion/styled';
import {colors} from '@/constants/colors';

const Container = styled.div`
    margin-top: 16px;
    display: flex;
    cursor: pointer;
`;

const Point = styled.div`
    flex-shrink: 0;
    display: inline-block;
    background-color: ${colors['gray-6']};
    margin-top: 9px; // line-height 是 22，小圆点高度 4px，上下都是 9px
    width: 4px;
    height: 4px;
    border-radius: 50%;
    margin-right: 10px;
`;

const Answer = styled.div`
    margin-top: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    background: ${colors['gray-2']};
    color: ${colors['gray-8']};
    word-break: break-all;
`;

const ChildrenThrough = ({children}: {children: ReactNode}) => <>{children}</>;

interface Props {
    text: string;
    // 两者有其一
    Link?: ComponentType<any>;
    answer?: ReactNode;
    // Link 颜色调整
    linkColor?: string;
}

export const ListItem = ({text, Link = ChildrenThrough, answer, linkColor}: Props) => {
    const [showAnswer, toggle] = useReducer(v => !v, false);

    return (
        <>
            <Link style={{color: linkColor}}>
                <Container onClick={answer ? toggle : undefined}>
                    <Point />
                    <span>{text}</span>
                </Container>
            </Link>
            {showAnswer && <Answer>{answer}</Answer>}
        </>
    );
};
