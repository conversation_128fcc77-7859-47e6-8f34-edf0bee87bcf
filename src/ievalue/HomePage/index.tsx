import {useEffect} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {css} from '@emotion/css';
import {PageLayout} from '@/design/Layouts/PageLayout';
import {loadBannerList} from '@/hooks/ievalue/challengeDashboard';
import {BannerTypeEnum} from '@/constants/ievalue/prompt';
import {loadLabCourseTabs} from '@/regions/ievalue/home/<USER>';
import {loadModelList} from '@/regions/promptTuning/modelList';
import {loadSpaceDetail} from '@/regions/ievalue/space/spaceDetail';
import {SpeedGenerateModal} from './PromptGenerate/SpeedGenerateModal';
import {Course} from './Course/Course';
import {Docs} from './Docs';
import {HomeBanner} from './HomeBanner';
import {TabsPanel} from './TabsPanel';
import {AbilityEntry} from './AbilityEntry';

const Container = styled.div`
    display: flex;
    margin-top: 12px;
    gap: 12px;
`;
const Left = styled.div`
    flex: 1;
`;

const HomePage = () => {
    useEffect(
        () => {
            loadBannerList({bannerType: BannerTypeEnum.Home});
            loadLabCourseTabs();
            // 诊断用 isQualityTest，调优用 isEvolvePredict
            loadModelList({spaceCode: 'public', isQualityTest: 1});
            loadModelList({spaceCode: 'public', isEvolvePredict: 1});
            loadSpaceDetail({spaceCode: 'public'});
        },
        []
    );

    return (
        <PageLayout style={{background: '#F7F9FB'}}>
            <Flex gap={12}>
                <div className={css`width: calc(100% - 132px); border-radius: 6px; overflow: hidden;`}>
                    <HomeBanner />
                </div>
                <AbilityEntry />
            </Flex>

            <TabsPanel />
            <SpeedGenerateModal />
            <Container>
                <Left>
                    <Course />
                </Left>
                <Docs />
            </Container>
        </PageLayout>
    );
};

export default HomePage;
