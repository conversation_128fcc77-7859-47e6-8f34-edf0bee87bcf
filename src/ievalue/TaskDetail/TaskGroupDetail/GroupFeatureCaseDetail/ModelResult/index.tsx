/* eslint-disable max-lines */
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import Split from '@uiw/react-split';
import {Col, Input, Row, Space} from 'antd';
import {debounce, head} from 'lodash';
import {useCallback, useMemo, ReactNode} from 'react';
import {Boundary} from 'react-suspense-boundary';
import {
    apiCaseRecordFeatureUpdate,
    RecordFeatureItem,
} from '@/api/ievalue/case';
import backgroundImage from '@/assets/ievalue/img/dl_card_bj.png';
import {CaseHistoryTypeEnum} from '@/constants/ievalue/case';
import {
    useStrategyTaskList,
    useTaskCaseFeatureID,
    useTaskGroupFeatureID,
    useTaskInfo,
    useTaskStageID,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {LayoutOptionEnum} from '@/constants/ievalue/taskDetail';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {GSB_MANUAL_POLICY_ID} from '@/constants/ievalue/evaluate';
import {loadFeatureRecordListRegion} from '@/regions/ievalue/task/featureRecordList';
import {FullScreenComponent} from '@/components/TaskGroup/FullScreen';
import {AddTagButtoon, ModelTag} from './ModelTag';
import FeatureScoreSelect from './FeatureScoreSelect';

const ScorePanel = styled(FlexLayout)`
    border-left: 1px solid #e1e1e1;
`;

interface ModelResultProps {
    recordItem: RecordFeatureItem;
    title: ReactNode;
    remarkVisible: boolean;
    layout: LayoutOptionEnum;
}


export const ModelResult = ({
    recordItem,
    title,
    remarkVisible,
    layout,
}: ModelResultProps) => {
    const [taskInfo] = useTaskInfo();
    const taskID = useTaskTaskID();
    const stageID = useTaskStageID();
    const groupFeatureID = useTaskGroupFeatureID();
    const caseFeatureID = useTaskCaseFeatureID();
    const [strategyList] = useStrategyTaskList();
    const strategyMetricList = useMemo(
        () => {
            return head(strategyList)?.metric ?? [];
        },
        [strategyList]
    );
    const historyTagList = useMemo(
        () => {
            return recordItem.tags;
        },
        [recordItem]
    );
    const isGSBStrategy = taskInfo?.manualPolicy === GSB_MANUAL_POLICY_ID;

    const handleAddTag = useCallback(
        async (tagName: string) => {
            try {
                let resultTag: string[] = [];
                if (tagName === 'LIKE') {
                    resultTag = historyTagList.includes(tagName)
                        ? historyTagList.filter(item => item !== tagName)
                        : [...historyTagList, tagName].filter(
                            item => item !== 'NOTLIKE'
                        );
                } else if (tagName === 'NOTLIKE') {
                    resultTag = historyTagList.includes(tagName)
                        ? historyTagList.filter(item => item !== tagName)
                        : [...historyTagList, tagName].filter(
                            item => item !== 'LIKE'
                        );
                } else {
                    resultTag = historyTagList.includes(tagName)
                        ? historyTagList.filter(item => item !== tagName)
                        : [...historyTagList, tagName];
                }
                const diff = [
                    {
                        type: CaseHistoryTypeEnum.RECORD,
                        caseFeatureID,
                        groupFeatureID,
                        predictRecordFeatureID: recordItem.ID,
                        field: 'tags',
                        origin: historyTagList.join(','),
                        turnTo: resultTag.join(','),
                    },
                ];
                await apiCaseRecordFeatureUpdate({
                    predictRecordFeatureID: recordItem.ID,
                    stageID,
                    caseFeatureID,
                    groupFeatureID,
                    tags: resultTag,
                    diff,
                });
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error, 'caseEvaluateUpsert error');
            } finally {
                await loadFeatureRecordListRegion({
                    taskID,
                    caseFeatureID,
                    stageID,
                });
            }
        },
        [
            caseFeatureID,
            groupFeatureID,
            recordItem.ID,
            historyTagList,
            stageID,
            taskID,
        ]
    );

    const handleChange = useCallback(
        async ({target}: any) => {
            try {
                const updateScore = target?.value;
                await apiCaseRecordFeatureUpdate({
                    predictRecordFeatureID: recordItem.ID,
                    stageID,
                    caseFeatureID,
                    groupFeatureID,
                    note: updateScore,
                    diff: [
                        {
                            type: CaseHistoryTypeEnum.RECORD,
                            caseFeatureID,
                            groupFeatureID,
                            predictRecordFeatureID: recordItem.ID,
                            field: 'note',
                            origin: `${recordItem.note}`,
                            turnTo: `${updateScore}`,
                        },
                    ],
                });
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error, 'caseEvaluateUpsert error');
            } finally {
                await loadFeatureRecordListRegion({
                    taskID,
                    caseFeatureID,
                    stageID,
                });
            }
        },
        [recordItem, stageID, caseFeatureID, groupFeatureID, taskID]
    );
    return (
        <div
            style={{
                minWidth:
                    layout === LayoutOptionEnum.Horizontal ? '530px' : '100%',
                flex: '1 0',
            }}
            id={`id${recordItem.ID}`}
        >
            {layout === LayoutOptionEnum.Vertical ? (
                <Row gutter={[8, 6]} wrap={false} id={`id${recordItem.ID}`}>
                    <Col flex="auto">
                        <Split
                            lineBar
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                        >
                            <div
                                style={{
                                    width: isGSBStrategy ? '100%' : '80%',
                                    minWidth: 600,
                                }}
                            >
                                <FullScreenComponent
                                    title={
                                        <FlexLayout
                                            style={{width: '100%'}}
                                            justify="space-between"
                                            align="center"
                                        >
                                            <Space>
                                                {title}
                                                <Boundary>
                                                    <AddTagButtoon
                                                        historyTagList={
                                                            recordItem.tags
                                                        }
                                                        handleAddTag={
                                                            handleAddTag
                                                        }
                                                    />
                                                </Boundary>
                                            </Space>
                                            <ModelTag
                                                handleAddTag={handleAddTag}
                                                historyTagList={recordItem.tags}
                                            />
                                        </FlexLayout>
                                    }
                                    content={recordItem.output}
                                    reasoningContent={recordItem?.reasoningContent}
                                    searchResults={recordItem?.searchResults}
                                    showCharCount
                                    showDiff
                                    caseEvaluateItem={recordItem}
                                    enableScratchWords
                                    recordID={recordItem.ID}
                                    predictRecordID={recordItem.ID}
                                />
                            </div>
                            {!isGSBStrategy && (
                                <ScorePanel
                                    justify="space-around"
                                    direction="column"
                                    style={{
                                        width: '20%',
                                        background: '#fff',
                                        minWidth: 200,
                                        padding: 10,
                                    }}
                                >
                                    <FlexLayout
                                        wrap="wrap"
                                        gap={8}
                                        align="center"
                                    >
                                        {strategyMetricList?.map(
                                            metricItem => {
                                                return (
                                                    <FeatureScoreSelect
                                                        key={metricItem.metric}
                                                        metricItem={metricItem}
                                                        recordItem={recordItem}
                                                    />
                                                );
                                            }
                                        )}
                                    </FlexLayout>
                                </ScorePanel>
                            )}
                        </Split>
                    </Col>
                    {remarkVisible && !isGSBStrategy && (
                        <Col flex={'0 0 300px'}>
                            <div
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    padding: '4px',
                                    opacity: '0.8',
                                    background: '#FFFFFF',
                                    borderRadius: '6px',
                                }}
                            >
                                {remarkVisible && (
                                    <Input.TextArea
                                        style={{height: '100%'}}
                                        name="note"
                                        defaultValue={recordItem.note}
                                        draggable={false}
                                        bordered={false}
                                        placeholder="请输入备注"
                                        onChange={debounce(handleChange, 1000)}
                                    />
                                )}
                            </div>
                        </Col>
                    )}
                </Row>
            ) : (
                <FlexLayout
                    direction="column"
                    justify="space-between"
                    style={{height: '100%'}}
                    gap={2}
                >
                    <div style={{flex: '3 0 auto', width: '100%'}}>
                        <FullScreenComponent
                            title={
                                <FlexLayout
                                    style={{width: '100%'}}
                                    justify="space-between"
                                    align="center"
                                >
                                    {title}
                                    <ModelTag
                                        handleAddTag={handleAddTag}
                                        historyTagList={recordItem.tags}
                                    />
                                </FlexLayout>
                            }
                            content={recordItem.output}
                            reasoningContent={recordItem?.reasoningContent}
                            searchResults={recordItem?.searchResults}
                            showCharCount
                            showDiff
                            caseEvaluateItem={recordItem}
                            enableScratchWords
                            recordID={recordItem.ID}
                            predictRecordID={recordItem.ID}
                        />
                    </div>
                    {!isGSBStrategy && (
                        <ScorePanel
                            className={css`
                                background-image: url(${backgroundImage});
                                background-size: 100% 100%;
                            `}
                            justify="space-around"
                            direction="column"
                            gap={8}
                            style={{
                                width: '100%',
                                padding: '10px 20px',
                                flex: '0 0 auto',
                                borderRadius: '6px',
                            }}
                        >
                            <FlexLayout wrap="wrap" gap={8} align="center">
                                {strategyMetricList?.map(metricItem => {
                                    return (
                                        <FeatureScoreSelect
                                            key={metricItem.metric}
                                            metricItem={metricItem}
                                            recordItem={recordItem}
                                        />
                                    );
                                })}
                            </FlexLayout>
                            <Boundary>
                                <AddTagButtoon
                                    historyTagList={recordItem.tags}
                                    handleAddTag={handleAddTag}
                                />
                            </Boundary>
                            <div
                                style={{
                                    width: '100%',
                                    height: '150px',
                                    padding: '4px',
                                    opacity: '0.8',
                                    background: '#FFFFFF',
                                    borderRadius: '6px',
                                }}
                            >
                                <Input.TextArea
                                    style={{height: '100%', resize: 'none'}}
                                    name="note"
                                    defaultValue={recordItem.note}
                                    draggable={false}
                                    bordered={false}
                                    placeholder="请输入备注"
                                    onChange={debounce(handleChange, 1000)}
                                />
                            </div>
                        </ScorePanel>
                    )}
                </FlexLayout>
            )}
        </div>
    );
};
