import {Input, Typography} from 'antd';
import {debounce} from 'lodash';
import {useCallback} from 'react';
import {AutoMetricItem} from '@/api/ievalue/case';
import {apiEvaluateMetricUpdate} from '@/api/ievalue/evaluate';
import {FlexLayout} from '@/components/ievalue/FlexLayout';

interface FeedbackNoteProps {
    item: AutoMetricItem;
}

export const FeedbackNote = ({item}: FeedbackNoteProps) => {
    const handleChange = useCallback(
        async ({target}: any) => {
            try {
                await apiEvaluateMetricUpdate({
                    ID: item.ID,
                    note: target?.value ?? '',
                });
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error, 'FeedbackNote update error');
            }
        },
        [item.ID]
    );
    return (
        <FlexLayout direction="column" gap={8} style={{width: '100%'}}>
            <Typography.Text strong>人工补充</Typography.Text>
            <div
                style={{
                    width: '100%',
                    height: '60px',
                    padding: '4px',
                    opacity: '0.8',
                    background: '#FFFFFF',
                    borderRadius: '6px',
                }}
            >
                <Input.TextArea
                    style={{height: '100%', resize: 'none'}}
                    name="note"
                    defaultValue={item?.note ?? ''}
                    draggable={false}
                    variant="borderless"
                    placeholder="请输入备注"
                    onChange={debounce(handleChange, 1000)}
                />
            </div>
        </FlexLayout>
    );
};
