import {Button, Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {PlusOutlined} from '@ant-design/icons';
import {Avatar} from 'antd';
import {apiChatRecordCreate} from '@/api/ievalue/case';
import {FormOnFinish} from '@/utils/ievalue/typing';
import {useCaseStageType, useTaskInfo, useTaskTaskID} from '@/hooks/ievalue/task';
import {EditorHerb} from '@/components/ievalue/EditorHerb';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {CaseStageStatusEnum} from '@/constants/ievalue/case';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';

interface CreateChatRecordButtonProps {
    preID: number;
    predictRecordID: number;
    chatTaskID: string;
    onFinish: () => void;
}

const CreateChatRecordButton = ({
    preID,
    predictRecordID,
    chatTaskID,
    onFinish,
}: CreateChatRecordButtonProps) => {
    const [taskInfo] = useTaskInfo();
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const stageType = useCaseStageType();
    const [isModalVisible, {on: openModal, off: closeModal}] =
        useBoolean(false);
    const [input, setInput] = useState('');
    const [output, setOutput] = useState('');
    const [loading, {on: startLoading, off: stopLoading}] = useBoolean(false);
    const taskID = useTaskTaskID();

    const disabled = useMemo(
        () => {
            return stageType !== TaskStageEnum.EVALUATING || groupCaseInfo?.stageStatus === CaseStageStatusEnum.SUCCESS;
        },
        [groupCaseInfo?.stageStatus, stageType]
    );

    useEffect(
        () => {
            if (isModalVisible) {
                setInput('');
                setOutput('');
            }
        },
        [isModalVisible]
    );

    const handleFinish: FormOnFinish<any> = useCallback(
        async () => {
            startLoading();
            try {
                await apiChatRecordCreate({
                    preID,
                    predictRecordID,
                    taskID,
                    chatTaskID,
                    input,
                    output,
                });
            } catch (e) {
                // eslint-disable-next-line no-console
                console.log('error', e);
            } finally {
                await onFinish?.();
                closeModal();
                stopLoading();
            }
        },
        [chatTaskID, closeModal, input, onFinish, output, preID, predictRecordID, startLoading, stopLoading, taskID]
    );
    return (
        <>
            <Button
                disabledReason="只有评估阶段未提交时，才可以新增对话记录"
                disabled={disabled}
                type="text"
                size="small"
                block
                icon={<PlusOutlined />}
                onClick={openModal}
            >
                新增
            </Button>
            <Modal
                width="1300px"
                title="新增"
                open={isModalVisible}
                onCancel={closeModal}
                onOk={handleFinish}
                destroyOnClose
                confirmLoading={loading}
                maskClosable={false}
            >
                <FlexLayout direction="column" gap={8}>
                    <FlexLayout gap={8} style={{width: '100%'}}>
                        <Avatar
                            size={24}
                            style={{
                                backgroundColor: '#c7c7c7',
                                color: '#000000',
                                fontSize: 12,
                            }}
                        >
                            Q
                        </Avatar>
                        <EditorHerb
                            value={input}
                            onChange={setInput}
                            datasetID={taskInfo.datasetID}
                            style={{height: 180}}
                        />
                    </FlexLayout>
                    <FlexLayout gap={8} style={{width: '100%'}}>
                        <Avatar
                            size={24}
                            style={{
                                backgroundColor: '#5c9aff',
                                color: '#ffffff',
                                fontSize: 12,
                            }}
                        >
                            A
                        </Avatar>
                        <EditorHerb
                            value={output}
                            onChange={setOutput}
                            datasetID={taskInfo.datasetID}
                            style={{height: 300}}
                        />
                    </FlexLayout>
                </FlexLayout>
            </Modal>
        </>
    );
};

export default CreateChatRecordButton;
