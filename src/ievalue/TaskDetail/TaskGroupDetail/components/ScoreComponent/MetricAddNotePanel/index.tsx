import {ChangeEvent, useMemo} from 'react';
import {Form, Input} from 'antd';
import {debounce} from 'lodash';
import {
    apiTaskPolicyDimensionNoteUpdate,
    MetricItem,
} from '@/api/ievalue/case';

interface AddTagPanelProps {
    refresh?: () => void;
    metricItem: MetricItem;
    predictRecordID: number;
    note?: string;
}

const MetricAddNotePanel = ({
    refresh,
    metricItem,
    predictRecordID,
    note,
}: AddTagPanelProps) => {
    const debouncedNoteChange = useMemo(
        () => {
            const updateNote = async (e: ChangeEvent<HTMLTextAreaElement>) => {
                const note = e.target.value;
                try {
                    await apiTaskPolicyDimensionNoteUpdate({
                        predictRecordID,
                        metric: metricItem.metric,
                        note,
                    });
                } catch (error) {
                /* empty */
                } finally {
                    await refresh?.();
                }
            };
            return debounce(updateNote, 500);
        },
        [metricItem.metric, predictRecordID, refresh]
    );

    if (!metricItem?.hasNote) {
        return null;
    }

    return (
        <Form.Item
            label={`${metricItem.desc}的备注`}
            required={!!metricItem?.isNoteRequired}
            style={{width: '100%'}}
        >
            <Input.TextArea
                rows={1}
                defaultValue={note}
                draggable={false}
                placeholder={`请输入${metricItem.desc}的备注`}
                onChange={debouncedNoteChange}
            />
        </Form.Item>
    );
};

export default MetricAddNotePanel;
