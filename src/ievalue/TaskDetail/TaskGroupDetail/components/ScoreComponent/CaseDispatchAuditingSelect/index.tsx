/* eslint-disable complexity */
import {debounce} from 'lodash';
import styled from '@emotion/styled';
import {Form, Input, Select, Space} from 'antd';
import {useMemo, useState, useCallback, ChangeEvent} from 'react';
import {
    ChoiceItem,
    UpdateDiffItem,
    UpdateCaseEvaluateScoreItem,
    apiCaseEvaluateUpsert,
    apiTaskPolicyDimensionNoteUpdate,
} from '@/api/ievalue/case';
import {CaseHistoryTypeEnum, CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useTaskInfo, useCaseStageType} from '@/hooks/ievalue/task';
import {getScoreItemByMetric} from '@/ievalue/TaskDetail/utils';
import {ScoreGroupProps} from '@/ievalue/TaskDetail/CaseProcessPanel/ModelResult/ScoreGroup';
import MetricAddTagPanel from '../../TagPanelComponent/MetricAddTagPanel';

const getScoreNameByScore = (choices: ChoiceItem[], score: number) => {
    return choices?.find(item => item.score === score)?.name ?? '';
};

const StyleSpace = styled(Space)`
    .ant-5-form-css-var {
        --ant-5-form-item-margin-bottom: 0px !important;
    }
    .ant-5-form-item {
        font-weight: 500 !important;
        .ant-5-row > .ant-5-col > label {
            color: black !important;
        }
    }
`;

const CaseDispatchAuditingSelect = ({
    groupInfo,
    recordItem,
    metricItem,
    blockHandle,
    groupCaseInfo,
    needUpdateRecord,
    handleAutoUpdateSort,
    handleRefresh,
}: ScoreGroupProps) => {
    const [taskInfo] = useTaskInfo();
    const stageType = useCaseStageType();
    const scoreSelectWidth = useMemo(
        () => {
            const maxLength = metricItem?.choices?.reduce((maxLength, item) => {
                return Math.max(maxLength, item.name.length);
            }, 3);
            return `${Math.max(maxLength * 23.5, 80)}px`;
        },
        [metricItem?.choices]
    );

    const originScoreItem = getScoreItemByMetric(
        metricItem.metric,
        recordItem.origin
    );
    const diffScoreItem = getScoreItemByMetric(
        metricItem.metric,
        recordItem?.diff
    );
    const [value, setValue] = useState(originScoreItem?.score);

    const handleChange = useCallback(
        async (updateScoreName: string, updateScore: number) => {
            if (blockHandle) {
                return;
            }
            let diff: UpdateDiffItem[] = [];
            let score: UpdateCaseEvaluateScoreItem[] = [];
            setValue(pre => {
                const originScoreName = getScoreNameByScore(
                    metricItem?.choices,
                    pre
                );
                diff = [
                    {
                        type: CaseHistoryTypeEnum.RECORD,
                        caseID: groupCaseInfo.caseID,
                        groupID: groupCaseInfo.groupID,
                        recordID: recordItem.origin.predictRecordID,
                        field: metricItem.metric,
                        origin: originScoreName,
                        turnTo: updateScoreName,
                    },
                ];
                score = [
                    {
                        metric: metricItem.metric,
                        scoreName: updateScoreName,
                        score: updateScore,
                        noScoring: !!metricItem?.noScoring,
                        notRequired: !!metricItem?.notRequired,
                    },
                ];
                return needUpdateRecord ? updateScore : pre;
            });
            try {
                await apiCaseEvaluateUpsert({
                    accepted: 0,
                    predictRecordID: recordItem.origin.predictRecordID,
                    stageID: groupCaseInfo.stageID,
                    groupID: groupCaseInfo.groupID,
                    caseID: groupCaseInfo.caseID,
                    taskID: taskInfo.ID,
                    score,
                    diff,
                });
                await handleAutoUpdateSort?.(metricItem.metric, updateScore);
            } catch (error) {
                /* empty */
            } finally {
                await handleRefresh?.();
            }
        },
        [
            blockHandle,
            groupCaseInfo.caseID,
            groupCaseInfo.groupID,
            groupCaseInfo.stageID,
            handleAutoUpdateSort,
            handleRefresh,
            metricItem,
            needUpdateRecord,
            recordItem.origin.predictRecordID,
            taskInfo.ID,
        ]
    );

    const handleSelect = useCallback(
        (_: number, option: ChoiceItem) => {
            const {name, score} = option;
            handleChange(name, score);
        },
        [handleChange]
    );

    const debouncedNoteChange = useMemo(
        () => {
            const updateNote = async (e: ChangeEvent<HTMLTextAreaElement>) => {
                const note = e.target.value;
                try {
                    await apiTaskPolicyDimensionNoteUpdate({
                        predictRecordID: recordItem.origin.predictRecordID,
                        metric: metricItem.metric,
                        note,
                    });
                } catch (error) {
                /* empty */
                } finally {
                    await handleRefresh?.();
                }
            };
            return debounce(updateNote, 500);
        },
        [handleRefresh, metricItem.metric, recordItem.origin.predictRecordID]
    );

    return (
        <StyleSpace align="center" wrap>
            <Form.Item
                label={metricItem.desc}
                required={!metricItem?.notRequired}
            >
                <Select<any, any>
                    size="small"
                    style={{width: scoreSelectWidth}}
                    placeholder="请选择"
                    {...([
                        TaskStageEnum.ACCEPTING,
                        TaskStageEnum.AUDITING,
                    ].includes(stageType)
                    || (stageType === TaskStageEnum.EVALUATING
                        && groupInfo.status === CaseStatusEnum.REJECTED)
                    || blockHandle
                        ? {
                            value,
                        }
                        : {
                            defaultValue:
                                  stageType === TaskStageEnum.AUDITING_FORWARD
                                      ? diffScoreItem?.score ?? value
                                      : value,
                        })}
                    fieldNames={{label: 'name', value: 'score'}}
                    options={metricItem.choices}
                    onChange={handleSelect}
                    disabled={taskInfo.stage === 'TERMINATED' || blockHandle}
                />
            </Form.Item>
            <MetricAddTagPanel
                blockHandle={blockHandle}
                refresh={handleRefresh}
                metricItem={metricItem}
                predictRecordID={recordItem.origin.predictRecordID}
                caseID={groupCaseInfo.caseID}
                stageID={groupCaseInfo.stageID}
                groupID={groupCaseInfo.groupID}
                taskID={taskInfo.ID}
                selectList={originScoreItem?.tags}
            />
            {!!metricItem?.hasNote && (
                <Form.Item
                    label={`${metricItem.desc}的备注`}
                    required={!!metricItem?.isNoteRequired}
                >
                    <Input.TextArea
                        rows={1}
                        defaultValue={originScoreItem?.note}
                        draggable={false}
                        placeholder={`请输入${metricItem.desc}的备注`}
                        onChange={debouncedNoteChange}
                    />
                </Form.Item>
            )}
        </StyleSpace>
    );
};

export default CaseDispatchAuditingSelect;
