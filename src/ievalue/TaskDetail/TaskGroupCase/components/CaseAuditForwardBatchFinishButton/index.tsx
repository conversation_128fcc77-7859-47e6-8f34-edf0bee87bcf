import {Button, message} from '@panda-design/components';
import {ComponentProps, useCallback} from 'react';
import {useBoolean} from 'huse';
import {apiAuditForwardBatchFinishByCaseID} from '@/api/ievalue/case';
import {useTaskTaskID} from '@/hooks/ievalue/task';

export interface CaseAuthBatchUpdateProps {
    caseIDs: number[];
    onFinish: () => void;
}

type CaseAuthBatchUpdateButtonProps = ComponentProps<typeof Button> &
    CaseAuthBatchUpdateProps;

const CaseAuditForwardBatchFinishButton = ({
    onFinish,
    caseIDs,
    ...props
}: CaseAuthBatchUpdateButtonProps) => {
    const taskID = useTaskTaskID();
    const [loading, {on: startLoading, off: stopLoading}] = useBoolean(false);

    const handleClick = useCallback(
        async () => {
            startLoading();
            try {
                const result = await apiAuditForwardBatchFinishByCaseID({
                    taskID,
                    caseIDs,
                });
                message.success(
                    '批量完成审核成功，其中有'
                    + result.unprocessedNum
                    + '条case不满足批量处理条件已自动跳过！'
                );
            } catch (e) {
                /* empty */
            } finally {
                onFinish();
                stopLoading();
            }
        },
        [caseIDs, onFinish, startLoading, stopLoading, taskID]
    );

    return caseIDs?.length > 0 ? (
        <Button loading={loading} {...props} onClick={handleClick}>
            批量完成审核
        </Button>
    ) : (
        <></>
    );
};

export default CaseAuditForwardBatchFinishButton;
