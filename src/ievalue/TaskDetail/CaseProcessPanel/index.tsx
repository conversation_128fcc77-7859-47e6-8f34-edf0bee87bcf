/* eslint-disable max-len */
/* eslint-disable max-lines */
/* eslint-disable complexity */
import {
    AppstoreOutlined,
    BarsOutlined,
    LeftOutlined,
    RightOutlined,
} from '@ant-design/icons';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {
    Button,
    Checkbox,
    Col,
    Divider,
    Row,
    Segmented,
    Space,
    Tag,
    Typography,
} from 'antd';
import {useElementSize} from 'huse';
import {pick} from 'lodash';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Boundary} from 'react-suspense-boundary';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {CaseCustomColumnSelect} from '@/components/ievalue/CaseCustomColumnSelect';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {GSB_MANUAL_POLICY_ID} from '@/constants/ievalue/evaluate';
import {
    OnceDisplayOptionEnum,
    OnceDisplayOptions,
    TaskStageEnum,
} from '@/constants/ievalue/task';
import {LayoutOptionEnum} from '@/constants/ievalue/taskDetail';
import {ModelName} from '@/components/Evaluate/ModelName';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGroupCaseID,
    useGroupCaseStageTimeInitialize,
    useLocalStorageCustomList,
    useLocalStorageDisplayList,
    useLocalStorageEvaluateLayout,
    useTaskInfo,
} from '@/hooks/ievalue/task';
import {convertObjectToMarkdownTable} from '@/utils/ievalue/task';
import {EvaluateDiffProvider} from '@/providers/TaskDetailProviders/EvaluateDiffProvider';
import {EvaluateCommonProvider, useEvaluateCommonCategoryInfo, useEvaluateCommonGroupCaseInfo, useEvaluateCommonGroupCaseInfoRefresh} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {FullScreenComponent} from '@/components/TaskGroup/FullScreen';
import {MultiModalContent} from '@/components/TaskGroup/MultiModalContent';
import {hasMultiModalData} from '@/components/Evaluate/TaskDetailUtils/util';
import {CaseProcessFooter} from './CaseProcessFooter';
import {EvaluateRuleButton} from './EvaluateRuleButton';
import {FinishEvaluate} from './FinishEvaluate';
import GSBSort from './GSBSort';
import {ModelResult} from './ModelResult';
import {SatisfactionSort} from './SatisfactionSort';
import EvaluateTerminatedTaskButton from './EvaluateTerminatedTaskButton';
import {EvaluateReferenceButton} from './EvaluateReferenceButton';
import CaseStatusTag from './CaseStatusTag';

const Header = styled.div`
    flex: 0 1 auto;
`;

const ActionBar = styled.div`
    flex: 0 1 auto;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 100;
    height: 40px;
`;

const Content = styled(FlexLayout)`
    flex: 4 0 auto;
    overflow: auto;
    width: 100%;
    padding: 12px 20px;
    background-image: linear-gradient(180deg, #c8daf1 2%, #f3f5f9 100%);
    border-radius: 6px;
    margin-bottom: 8px;
`;

const CaseProcess = () => {
    const [taskInfo] = useTaskInfo();
    const {caseEvaluateRecords, caseEvaluateRefresh} = useCaseEvaluateList();
    const [remarkVisible, setRemarkVisible] = useState(false);
    const [displayList, setDisplayList] = useLocalStorageDisplayList();
    const [layoutState, setLayoutState] = useLocalStorageEvaluateLayout();
    const stageType = useCaseStageType();
    const {customSelectList, customGroupList, groupName, setCustomData} =
        useLocalStorageCustomList();
    const [allCustomSelectList, setAllCustomSelectList] = useState<string[]>(
        []
    );
    const caseID = useGroupCaseID();
    const contentRef = useRef<HTMLDivElement>(null);
    const [ref, size] = useElementSize();
    const onceRef = useRef(true);
    const categoryInfo = useEvaluateCommonCategoryInfo();
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const groupCaseInfoRefresh = useEvaluateCommonGroupCaseInfoRefresh();

    // 反馈阶段默认只有横向支持展示
    useEffect(
        () => {
            if (stageType === TaskStageEnum.FEEDBACK) {
                setLayoutState(LayoutOptionEnum.Horizontal);
            }
        },
        [setLayoutState, stageType]
    );

    const contentStyle = useMemo(
        () => {
            if (layoutState === LayoutOptionEnum.Vertical) {
                const height = window.innerHeight - (size?.height ?? 0) - 165;
                if (height < 200) {
                    return {maxHeight: `${200}px`, minHeight: `${200}px`};
                }
                return {maxHeight: `${height}px`, minHeight: `${200}px`};
            }
            return {};
        },
        // 横纵布局切换时，重新计算高度
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [size, layoutState]
    );

    const customInfoStr = useMemo(
        () => {
            const customInfoObj = JSON.parse(groupCaseInfo?.optionalRaw || '{}');
            const headerList = groupCaseInfo?.header?.split(',') || [];
            return convertObjectToMarkdownTable(
                pick(customInfoObj, customSelectList), headerList
            );
        },
        [customSelectList, groupCaseInfo?.header, groupCaseInfo?.optionalRaw]
    );

    const handleRemarkVisibleChange = useCallback(
        () => {
            setRemarkVisible(visible => !visible);
        },
        []
    );

    useEffect(
        () => {
            contentRef.current?.scrollTo(0, 0);
        },
        [caseID]
    );

    useEffect(
        () => {
            if (groupCaseInfo?.optionalRaw && onceRef.current) {
                onceRef.current = false;
                const customInfoObj = JSON.parse(
                    groupCaseInfo?.optionalRaw || '{}'
                );
                const allSelectList = Object.keys(customInfoObj);
                setAllCustomSelectList(allSelectList);
                if (!customSelectList?.length) {
                    setCustomData({list: allSelectList});
                }
            }
        },
        [customSelectList, groupCaseInfo?.optionalRaw, setCustomData]
    );
    return (
        <>
            {groupCaseInfo && (
                <FlexLayout
                    className={css`
                        width: 100%;
                        height: calc(100vh - 80px);
                        overflow-y: auto;
                        ::-webkit-scrollbar {
                            display: none;
                        }
                    `}
                    direction="column"
                    align="stretch"
                >
                    <Header ref={ref}>
                        <ActionBar>
                            <FlexLayout
                                justify="space-between"
                                style={{alignItems: 'center'}}
                            >
                                <Space style={{height: '100%'}}>
                                    <Typography.Title
                                        level={4}
                                        style={{maxWidth: 280}}
                                        ellipsis={{
                                            tooltip: groupCaseInfo?.groupName || '-',
                                        }}
                                    >
                                        {groupCaseInfo.groupName}
                                    </Typography.Title>
                                    <Tag>{groupCaseInfo.stageName}</Tag>
                                    <CaseStatusTag />
                                </Space>
                                <Space split="|">
                                    <Checkbox.Group
                                        options={OnceDisplayOptions}
                                        value={displayList}
                                        onChange={setDisplayList}
                                    />
                                    <Segmented
                                        value={layoutState}
                                        options={[
                                            {
                                                label: LayoutOptionEnum.Vertical,
                                                value: LayoutOptionEnum.Vertical,
                                                icon: <BarsOutlined />,
                                            },
                                            {
                                                label: LayoutOptionEnum.Horizontal,
                                                value: LayoutOptionEnum.Horizontal,
                                                icon: <AppstoreOutlined />,
                                            },
                                        ]}
                                        onChange={setLayoutState}
                                    />
                                    <EvaluateReferenceButton
                                        data={groupCaseInfo?.referenceIndicator}
                                        predictRecordID={caseEvaluateRecords?.[0]?.origin?.predictRecordID}
                                    />
                                    <EvaluateRuleButton />
                                    <FinishEvaluate />
                                    <EvaluateTerminatedTaskButton />
                                </Space>
                            </FlexLayout>
                            <Divider style={{margin: '8px 0'}} />
                        </ActionBar>
                        {!!displayList?.length && (
                            <Row
                                gutter={[16, 12]}
                                style={{padding: '12px 0'}}
                            >
                                {displayList.includes(
                                    OnceDisplayOptionEnum.Query
                                ) && (
                                    <Col flex={'2 1'}>
                                        <FullScreenComponent
                                            canCollapse
                                            title={
                                                <Space>
                                                    <Typography.Text strong>
                                                        Query
                                                    </Typography.Text>
                                                    {categoryInfo && (
                                                        <Tag>
                                                            <Typography.Text ellipsis={{tooltip: categoryInfo}} style={{maxWidth: 200}}>
                                                                {categoryInfo}
                                                            </Typography.Text>
                                                        </Tag>
                                                    )}
                                                </Space>
                                            }
                                            showDiff
                                            background="rgba(245,231,222,0.5)"
                                            content={groupCaseInfo.query}
                                            onRefresh={groupCaseInfoRefresh}
                                            editKey="input"
                                        />
                                    </Col>
                                )}
                                {displayList.includes(
                                    OnceDisplayOptionEnum.Query
                                )
                                    && hasMultiModalData(groupCaseInfo?.multiModal) && (
                                    <Col flex={'2 1'}>
                                        <FullScreenComponent
                                            canCollapse
                                            title={
                                                <Typography.Text strong>
                                                    多模态数据
                                                </Typography.Text>
                                            }
                                            background="rgba(245,231,222,0.5)"
                                            content={<MultiModalContent multiModal={groupCaseInfo?.multiModal} />}
                                        />
                                    </Col>
                                )}
                                {displayList.includes(
                                    OnceDisplayOptionEnum.AnswerVisible
                                ) && (
                                    <Col flex={'2 1'}>
                                        <FullScreenComponent
                                            canCollapse
                                            title={
                                                <Typography.Text strong>
                                                    参考答案
                                                </Typography.Text>
                                            }
                                            showDiff
                                            background="rgba(226,232,240,0.5)"
                                            content={
                                                groupCaseInfo.referenceOutput
                                            }
                                            onRefresh={groupCaseInfoRefresh}
                                            editKey="referenceOutput"
                                        />
                                    </Col>
                                )}
                                {displayList.includes(
                                    OnceDisplayOptionEnum.CustomVisible
                                ) && (
                                    <Col span={24}>
                                        <FullScreenComponent
                                            canCollapse
                                            title={
                                                <Space
                                                    align="baseline"
                                                    id="customInfoPanel"
                                                >
                                                    <Typography.Text strong>
                                                        自定义内容
                                                    </Typography.Text>
                                                    <CaseCustomColumnSelect
                                                        options={
                                                            allCustomSelectList
                                                        }
                                                        customSelectList={
                                                            customSelectList
                                                        }
                                                        setCustomData={
                                                            setCustomData
                                                        }
                                                        customGroupList={
                                                            customGroupList
                                                        }
                                                        groupName={groupName}
                                                        // eslint-disable-next-line react/jsx-no-bind
                                                        getPopupContainer={() =>
                                                            document.getElementById(
                                                                'customInfoPanel'
                                                            )
                                                        }
                                                    />
                                                </Space>
                                            }
                                            background="#F7F7F7"
                                            content={customInfoStr}
                                        />
                                    </Col>
                                )}
                            </Row>
                        )}
                    </Header>
                    <Content
                        direction={
                            layoutState === LayoutOptionEnum.Horizontal
                                ? 'row'
                                : 'column'
                        }
                        gap={8}
                        ref={contentRef}
                        align="stretch"
                        style={contentStyle}
                        id="ScratchWordsContainer"
                    >
                        {layoutState === LayoutOptionEnum.Vertical
                            && taskInfo?.manualPolicy !== GSB_MANUAL_POLICY_ID && (
                            <Row gutter={8}>
                                <Col flex="auto">
                                    <Space.Compact block>
                                        <div style={{flex: '4'}}>
                                            结果
                                        </div>
                                        <div
                                            style={{
                                                flex: remarkVisible
                                                    ? '1'
                                                    : '0.8',
                                            }}
                                        >
                                            打分
                                        </div>
                                    </Space.Compact>
                                </Col>
                                <Col
                                    flex={
                                        remarkVisible
                                            ? '0 0 300px'
                                            : '0 0 60px'
                                    }
                                >
                                    <Space align="center" size={0}>
                                        备注
                                        <Button
                                            size="small"
                                            type="text"
                                            icon={
                                                remarkVisible ? (
                                                    <RightOutlined
                                                        style={{
                                                            fontSize:
                                                                    '12px',
                                                        }}
                                                    />
                                                ) : (
                                                    <LeftOutlined
                                                        style={{
                                                            fontSize:
                                                                    '12px',
                                                        }}
                                                    />
                                                )
                                            }
                                            onClick={
                                                handleRemarkVisibleChange
                                            }
                                        />
                                    </Space>
                                </Col>
                            </Row>
                        )}
                        {caseEvaluateRecords?.map((item, index) => {
                            return (
                                <ModelResult
                                    title={
                                        <ModelName
                                            realName={
                                                caseEvaluateRecords[index]
                                                    .origin.model
                                            }
                                            pretendName={`结果${index + 1}`}
                                        />
                                    }
                                    key={
                                        caseEvaluateRecords[index].origin
                                            .predictRecordID
                                    }
                                    recordItem={item}
                                    onRefresh={caseEvaluateRefresh}
                                    groupCaseInfo={groupCaseInfo}
                                    remarkVisible={remarkVisible}
                                    layout={layoutState}
                                />
                            );
                        })}
                    </Content>
                    <Row>
                        <Col span={24}>
                            <Boundary>
                                {taskInfo?.manualPolicy === GSB_MANUAL_POLICY_ID ? (
                                    <GSBSort />
                                ) : (
                                    <SatisfactionSort />
                                )}
                            </Boundary>
                        </Col>
                    </Row>
                    <CaseProcessFooter
                        caseEvaluateRecords={caseEvaluateRecords}
                    />
                    {/* <SelectAction /> */}
                </FlexLayout>
            )}
        </>
    );
};

export const CaseProcessPanel = () => {
    useGroupCaseStageTimeInitialize();
    return (
        <CustomBoundary.FullSizeLoading>
            <EvaluateDiffProvider>
                <EvaluateCommonProvider>
                    <CaseProcess />
                </EvaluateCommonProvider>
            </EvaluateDiffProvider>
        </CustomBoundary.FullSizeLoading>
    );
};

export default CaseProcessPanel;
