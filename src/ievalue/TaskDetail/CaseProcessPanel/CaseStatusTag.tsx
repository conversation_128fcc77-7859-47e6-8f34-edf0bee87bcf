import {Tag} from 'antd';
import {memo} from 'react';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {useCaseStageType} from '@/hooks/ievalue/task';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';

export const CaseStatusTag = memo(() => {
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const stageType = useCaseStageType();

    if (
        [CaseStatusEnum.REJECT, CaseStatusEnum.SUBMIT_DISPUTE].includes(
            groupCaseInfo.status
        )
    ) {
        switch (stageType) {
            case TaskStageEnum.EVALUATING:
                return groupCaseInfo.status === CaseStatusEnum.REJECT ? (
                    <Tag color="red">被打回</Tag>
                ) : null;
            case TaskStageEnum.AUDITING_FORWARD:
                return groupCaseInfo.status === CaseStatusEnum.REJECT ? (
                    <Tag color="red">被打回</Tag>
                ) : (
                    <Tag color="volcano">再次审核</Tag>
                );
        }
    }

    return null;
});

export default CaseStatusTag;
