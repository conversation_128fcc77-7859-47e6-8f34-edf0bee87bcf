import {Button, message} from '@panda-design/components';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {
    apiCaseAcceptancePass,
    apiCaseAuditForwardPass,
    apiCaseAuditPass,
    apiCaseEvaluatePass,
} from '@/api/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    useGroupCaseID,
    useIsMegAgentLowQ,
    useTaskCaseStats,
    useTaskStageID,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {TaskUrl} from '@/links/ievalue/task';

export const CaseDispatchSubmitButton = () => {
    const taskID = useTaskTaskID();
    const stageID = useTaskStageID();
    const caseID = useGroupCaseID();
    const navigate = useNavigate();
    const spaceCode = useSpaceCodeSafe();
    const [TaskCaseStatsObj] = useTaskCaseStats();
    const isMegAgentLowQ = useIsMegAgentLowQ();

    const caseStageType = TaskCaseStatsObj?.[stageID].stageType;

    const handleSubmit = useCallback(
        async () => {
            switch (caseStageType) {
                case TaskStageEnum.EVALUATING:
                    await apiCaseEvaluatePass({caseID, taskID});
                    break;
                case TaskStageEnum.AUDITING:
                    await apiCaseAuditPass({caseID, taskID});
                    break;
                case TaskStageEnum.AUDITING_FORWARD: {
                    const result = await apiCaseAuditForwardPass({caseID, taskID, stageID});
                    if (result?.reachedThreshold) {
                        message.info('抽样比例已达成，可以随时结束当前阶段');
                    }
                    break;
                }
                case TaskStageEnum.ACCEPTING:
                case TaskStageEnum.FEEDBACK:
                    await apiCaseAcceptancePass({caseID, taskID});
                    break;
            }
        },
        [caseID, caseStageType, stageID, taskID]
    );

    const handleClick = useCallback(
        async () => {
            try {
                await handleSubmit();
                navigate(
                    TaskUrl.taskDetail.toUrl({
                        spaceCode,
                        taskID,
                    })
                );
            } catch (error) {
                //
            }
        },
        [handleSubmit, navigate, spaceCode, taskID]
    );

    if (!isMegAgentLowQ) {
        return null;
    }

    return (
        <Button type="primary" onClick={handleClick}>
            提交
        </Button>
    );
};
