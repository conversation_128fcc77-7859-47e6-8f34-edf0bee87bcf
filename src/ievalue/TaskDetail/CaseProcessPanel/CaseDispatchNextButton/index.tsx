import {Button, message, Modal} from '@panda-design/components';
import {memo, useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {InfoCircleFilled} from '@ant-design/icons';
import {useBoolean} from 'huse';
import {Space} from 'antd';
import {
    apiCaseAcceptancePass,
    apiCaseAuditForwardPass,
    apiCaseAuditPass,
    apiCaseEvaluatePass,
    apiGroupCaseRandom,
} from '@/api/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    useCaseStageType,
    useGroupCaseID,
    useIsChatbot,
    useTaskInfo,
    useTaskStageID,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {TaskUrl} from '@/links/ievalue/task';
import {
    apiTaskAuditForwardBatchFinish,
    apiTaskAuditForwardRejectThreshold,
    apiTaskInspectionThreshold,
} from '@/api/ievalue/task';
import {setWithRejectTaskModalRegion} from '@/regions/ievalue/task/withRejectTaskModalRegion';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import RejectModal from './RejectModal';

export const CaseDispatchNextButton = memo(() => {
    const taskID = useTaskTaskID();
    const stageID = useTaskStageID();
    const caseID = useGroupCaseID();
    const navigate = useNavigate();
    const spaceCode = useSpaceCodeSafe();
    const isChatbot = useIsChatbot();
    const [taskInfo] = useTaskInfo();
    const caseStageType = useCaseStageType();
    const [loading, {on: startLoading, off: stopLoading}] = useBoolean(false);
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();

    const isAuditForwardReject = useMemo(
        () => {
            return (
                caseStageType === TaskStageEnum.AUDITING_FORWARD
            && !!taskInfo?.withReject
            && groupCaseInfo?.status !== CaseStatusEnum.REJECT
            );
        },
        [caseStageType, groupCaseInfo?.status, taskInfo?.withReject]
    );

    const handleSubmit = useCallback(
        async () => {
            switch (caseStageType) {
                case TaskStageEnum.EVALUATING:
                    await apiCaseEvaluatePass({caseID, taskID});
                    break;
                case TaskStageEnum.AUDITING:
                    await apiCaseAuditPass({caseID, taskID});
                    break;
                case TaskStageEnum.AUDITING_FORWARD: {
                    await apiCaseAuditForwardPass({
                        caseID,
                        taskID,
                        stageID,
                    });
                    break;
                }
                case TaskStageEnum.ACCEPTING:
                case TaskStageEnum.FEEDBACK:
                    await apiCaseAcceptancePass({caseID, taskID});
                    break;
            }
        },
        [caseID, caseStageType, stageID, taskID]
    );

    const handleCaseRandomTask = useCallback(
        async () => {
            try {
                const caseItem = await apiGroupCaseRandom({taskID, stageID});
                if (!!caseItem && typeof caseItem === 'object') {
                    const caseProcess = isChatbot
                        ? TaskUrl.taskChatbotCaseProcess
                        : TaskUrl.taskCaseProcess;
                    const newUrl = `${caseProcess.toUrl({
                        spaceCode,
                        taskID,
                        groupID: caseItem.groupID,
                        stageID: caseItem.stageID,
                        caseID: caseItem.ID,
                    })}?dispatchType=case`;
                    navigate(newUrl);
                } else {
                    message.warning(
                        typeof caseItem === 'string' ? caseItem : '没有可操作的题目'
                    );
                    navigate(
                        TaskUrl.taskGroupDetail.toUrl({
                            spaceCode,
                            taskID,
                            groupID: 0,
                            stageID: 'all',
                        })
                    );
                }
            } catch (error) {
            //
            }
        },
        [isChatbot, navigate, spaceCode, stageID, taskID]
    );

    const handleCancelSamplingTask = useCallback(
        async () => {
            try {
                if (taskInfo?.withReject) {
                    await apiTaskAuditForwardBatchFinish({
                        taskID: taskInfo?.ID,
                        stageID,
                    });
                    message.success('批量流转完成，请在任务详情页查看结果');
                    navigate(
                        TaskUrl.taskGroupDetail.toUrl({
                            spaceCode,
                            taskID,
                            groupID: 0,
                            stageID: 'all',
                        })
                    );
                }
            } catch (error) {
                console.error('apiTaskAuditForwardBatchFinish error', error);
            }
        },
        [
            navigate,
            spaceCode,
            stageID,
            taskID,
            taskInfo?.ID,
            taskInfo?.withReject,
        ]
    );

    const handleSamplingTask = useCallback(
        async (handleFinish: () => Promise<void>) => {
            if (
                caseStageType === TaskStageEnum.AUDITING_FORWARD
                && !!taskInfo.isSampling
            ) {
                const result = await apiTaskInspectionThreshold({
                    taskID: taskInfo?.ID,
                    stageID,
                });
                if (result?.reached) {
                    Modal.confirm({
                        width: 500,
                        icon: <InfoCircleFilled style={{color: '#035fff'}} />,
                        content: taskInfo?.withReject
                            ? '当前阶段审核完成的用例数已经达到设置的抽样比例阈值，您可以将余下用例批量流转至下一阶段（如果当前阶段为任务最后一个阶段，则可以结束任务）。您是否要继续进行审核？'
                            : '当前任务已经达到设置的抽样审核比例阈值，可以结束任务。您是否仍要继续审核',
                        onOk: handleFinish,
                        cancelText: taskInfo?.withReject ? '批量流转' : '取消',
                        cancelButtonProps: {
                            type: 'primary',
                        },
                        onCancel: handleCancelSamplingTask,
                        okButtonProps: {
                            type: 'default',
                        },
                        okText: '继续审核',
                        autoFocusButton: 'cancel',
                    });
                    return;
                }
                await handleFinish();
                return;
            }
            await handleFinish();
        },
        [
            caseStageType,
            handleCancelSamplingTask,
            stageID,
            taskInfo?.ID,
            taskInfo.isSampling,
            taskInfo?.withReject,
        ]
    );

    const handleRejectTask = useCallback(
        async (handleFinish: () => Promise<void>) => {
            if (isAuditForwardReject) {
                const result = await apiTaskAuditForwardRejectThreshold({
                    taskID: taskInfo?.ID,
                    caseID,
                    stageID,
                    auditStatus: groupCaseInfo.status,
                });
                if (result?.rejectReachedThreshold) {
                    setWithRejectTaskModalRegion({
                        open: true,
                        item: result,
                        onFinish: handleFinish,
                    });
                } else {
                    await handleFinish();
                }
                return;
            }
            await handleFinish();
        },
        [
            caseID,
            groupCaseInfo.status,
            isAuditForwardReject,
            stageID,
            taskInfo?.ID,
        ]
    );

    const handleClick = useCallback(
        async () => {
            try {
                startLoading();
                await handleSubmit();
                await handleSamplingTask(handleCaseRandomTask);
            } catch (error) {
            //
            } finally {
                stopLoading();
            }
        },
        [
            handleCaseRandomTask,
            handleSamplingTask,
            handleSubmit,
            startLoading,
            stopLoading,
        ]
    );

    const handlePassClick = useCallback(
        (pass: number) => {
            return async () => {
                try {
                    startLoading();
                    await apiCaseAuditForwardPass({
                        caseID,
                        taskID,
                        pass,
                        stageID,
                    });
                    const executeNextSteps = async () => {
                        await handleSamplingTask(handleCaseRandomTask);
                    };
                    // 后端认为不通过时，pass为1，需要走拒绝逻辑
                    if (pass) {
                        await handleRejectTask(executeNextSteps);
                    } else {
                        await executeNextSteps();
                    }
                } catch (error) {
                    //
                } finally {
                    stopLoading();
                }
            };
        },
        [
            caseID,
            handleCaseRandomTask,
            handleRejectTask,
            handleSamplingTask,
            stageID,
            startLoading,
            stopLoading,
            taskID,
        ]
    );

    return isAuditForwardReject ? (
        <Space>
            <Button
                loading={loading}
                type="primary"
                onClick={handlePassClick(0)}
            >
                通过&下一题
            </Button>
            <Button
                loading={loading}
                type="primary"
                onClick={handlePassClick(1)}
            >
                不通过&下一题
            </Button>
            <RejectModal />
        </Space>
    ) : (
        <Button loading={loading} type="primary" onClick={handleClick}>
            提交&下一题
        </Button>
    );
});
