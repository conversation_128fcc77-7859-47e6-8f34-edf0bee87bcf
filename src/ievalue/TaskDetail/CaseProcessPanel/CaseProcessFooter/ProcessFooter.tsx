/* eslint-disable complexity */
import styled from '@emotion/styled';
import {Space, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {message} from '@panda-design/components';
import {useCallback, useEffect, useMemo} from 'react';
import {
    apiCaseAcceptancePass,
    apiCaseAuditForwardPass,
    apiCaseAuditPass,
    apiCaseEvaluatePass,
} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {GSB_MANUAL_POLICY_ID} from '@/constants/ievalue/evaluate';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {
    useCaseStageType,
    useGroupCaseID,
    useGroupCaseList,
    useGroupStageInfo,
    useIsCaseDispatch,
    useTaskInfo,
    useTaskStageID,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {AnswerCardButton, CaseButton} from '../AnswerCardButton';
import {CaseDispatchJumpOverButton} from '../CaseDispatchJumpOverButton';
import {CaseDispatchNextButton} from '../CaseDispatchNextButton';
import {CaseDispatchOnlyNextButton} from '../CaseDispatchOnlyNextButton';
import {DiffHistoryButton} from '../DiffHistoryButton';
import {SamplingAuditButton} from '../SamplingAuditButton';
import {CaseDispatchSubmitButton} from '../CaseDispatchSubmitButton';
import CaseDispatchAnswerCardButton from '../CaseDispatchAnswerCardButton';

const FooterContent = styled.div`
    flex: 0 0 50px;
    padding: 10px 20px 0 20px;
`;

interface Props {
    caseEvaluateRecords: any[];
}

const ProcessFooter = ({caseEvaluateRecords}: Props) => {
    const {groupCaseList, groupCaseTotal, groupCaseListRefresh} =
        useGroupCaseList(1, 100000);
    const [, , groupInfo] = useGroupStageInfo();
    const stageType = useCaseStageType();
    const [taskInfo] = useTaskInfo();
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    const stageID = useTaskStageID();
    const isCaseDispatch = useIsCaseDispatch();

    const [caseIndex, preCaseItem, nextCaseItem, isLastCase] = useMemo(
        () => {
            const caseIndex = groupCaseList.findIndex(
                item => item.caseID === caseID
            );
            if (groupCaseList.length >= 2) {
                const preCaseId =
                caseIndex === 0 ? groupCaseTotal - 1 : caseIndex - 1;
                const nextCaseId =
                caseIndex === groupCaseTotal - 1 ? 0 : caseIndex + 1;
                const isLastCase = caseIndex === groupCaseTotal - 1;
                return [
                    caseIndex,
                    groupCaseList[preCaseId],
                    groupCaseList[nextCaseId],
                    isLastCase,
                ];
            }
            return [caseIndex, null, null, false];
        },
        [groupCaseList, groupCaseTotal, caseID]
    );
    useEffect(
        () => {
            groupCaseListRefresh?.();
        },
        // caseID 可能改变，需要重新获取
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [caseID]
    );

    const handleAuditFinish = useCallback(
        async () => {
            try {
                if (stageType === TaskStageEnum.AUDITING) {
                    await apiCaseAuditPass({caseID, taskID});
                } else {
                    const result = await apiCaseAuditForwardPass({caseID, taskID, stageID});
                    if (result?.reachedThreshold) {
                        message.info('抽样比例已达成，可以随时结束当前阶段');
                    }
                }
                if (groupCaseList.length < 2) {
                    await groupCaseListRefresh?.();
                    message.success('提交成功！');
                }
                return true;
            } catch (error) {
                return false;
            }
        },
        [caseID, groupCaseList.length, groupCaseListRefresh, stageID, stageType, taskID]
    );

    const handleEvaluatePass = useCallback(
        async () => {
            try {
                await apiCaseEvaluatePass({caseID, taskID});
                if (groupCaseList.length < 2) {
                    await groupCaseListRefresh?.();
                    message.success('提交成功！');
                }
                return true;
            } catch (error) {
                return false;
            }
        },
        [caseID, groupCaseList.length, groupCaseListRefresh, taskID]
    );

    const handleAcceptancePass = useCallback(
        async () => {
            try {
                await apiCaseAcceptancePass({caseID, taskID});
                if (groupCaseList.length < 2) {
                    await groupCaseListRefresh?.();
                    message.success('提交成功！');
                }
                return true;
            } catch (error) {
                return false;
            }
        },
        [caseID, groupCaseList.length, groupCaseListRefresh, taskID]
    );

    const [canAuditPass, canEvaluatePass, canAcceptancePass] = useMemo(
        () => {
            const canAuditPass =
            [TaskStageEnum.AUDITING, TaskStageEnum.AUDITING_FORWARD].includes(
                stageType
            )
            && ![CaseStatusEnum.RESOLVE_DISPUTE, CaseStatusEnum.REJECTED].includes(
                groupInfo.status
            );
            const canEvaluatePass =
            stageType === TaskStageEnum.EVALUATING
            || groupInfo.status === CaseStatusEnum.REJECTED;
            const canAcceptancePass = stageType === TaskStageEnum.ACCEPTING;
            return [canAuditPass, canEvaluatePass, canAcceptancePass];
        },
        [groupInfo.status, stageType]
    );

    const handleSubmit = useMemo(
        () => {
            if (canAuditPass) {
                return handleAuditFinish;
            } else if (canEvaluatePass) {
                return handleEvaluatePass;
            }
            return handleAcceptancePass;
        },
        [
            canAuditPass,
            canEvaluatePass,
            handleAcceptancePass,
            handleAuditFinish,
            handleEvaluatePass,
        ]
    );

    return (
        <FooterContent>
            <FlexLayout justify="space-between">
                <div></div>
                {isCaseDispatch ? (
                    <Space>
                        <CaseDispatchNextButton />
                        <CaseDispatchOnlyNextButton />
                        <CaseDispatchJumpOverButton />
                        <CaseDispatchSubmitButton />
                    </Space>
                ) : (
                    <Space align="center">
                        {groupCaseList.length >= 2
                        && !!preCaseItem
                        && !!nextCaseItem
                            ? (
                                <>
                                    <CaseButton
                                        caseItem={preCaseItem}
                                        title={'上一题'}
                                    />
                                    <CaseButton
                                        caseItem={nextCaseItem}
                                        title={'下一题'}
                                        type="primary"
                                        isLastCase={isLastCase}
                                    />
                                    {groupInfo.status !== CaseStatusEnum.FINISH
                                    && (canEvaluatePass
                                        || canAuditPass
                                        || canAcceptancePass) && (
                                        <CaseButton
                                            caseItem={nextCaseItem}
                                            title={'提交&下一题'}
                                            type="primary"
                                            onClick={handleSubmit}
                                            checkISCompleted={
                                                !canAuditPass
                                                && !canAcceptancePass
                                                && taskInfo?.manualPolicy
                                                    !== GSB_MANUAL_POLICY_ID
                                            }
                                            caseEvaluateRecords={
                                                caseEvaluateRecords
                                            }
                                            isLastCase={isLastCase}
                                            stage={taskInfo.stage}
                                        />
                                    )}
                                </>
                            ) : (
                                <>
                                    {(canEvaluatePass
                                    || canAuditPass
                                    || canAcceptancePass)
                                    && (
                                        <Button
                                            type="primary"
                                            onClick={handleSubmit}
                                            disabled={
                                                taskInfo.stage === 'TERMINATED'
                                            }
                                        >
                                            提交
                                        </Button>
                                    )}
                                </>
                            )}
                        <SamplingAuditButton
                            handleSubmit={handleSubmit}
                            stageType={stageType}
                        />
                        <Typography.Text>{`${
                            caseIndex + 1
                        }/${groupCaseTotal}`}
                        </Typography.Text>
                    </Space>
                )}
                <Space align="center">
                    {isCaseDispatch ? <CaseDispatchAnswerCardButton /> : <AnswerCardButton />}
                    <DiffHistoryButton />
                </Space>
            </FlexLayout>
        </FooterContent>
    );
};

export default ProcessFooter;
