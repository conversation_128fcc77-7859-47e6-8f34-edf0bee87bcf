import {Loading} from '@panda-design/components';
import {Tabs} from 'antd';
import {head} from 'lodash';
import {useState, useMemo} from 'react';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {useTaskInfo, useTaskPieChart} from '@/hooks/ievalue/task';
import {PandaEmpty} from '@/design/PandaEmpty';
import PieChart from './PieChart';

interface TaskPieChartContentProps {
    stageName?: string;
}

interface TaskPieChartContainerProps extends TaskPieChartContentProps {
    modelID: string;
}

const TaskPieChartContainer = ({
    stageName,
    modelID,
}: TaskPieChartContainerProps) => {
    const {pending, pieChartData} = useTaskPieChart({
        modelID: Number(modelID),
        stageName,
    });

    if (pending) {
        return <Loading />;
    }

    if (!pieChartData?.length) {
        return (
            <PandaEmpty type="data" description="暂无数据" style={{width: '100%'}} />
        );
    }

    return (
        <FlexLayout wrap="wrap" gap={8} style={{width: '100%'}}>
            {pieChartData?.map(pieChartItem => (
                <PieChart key={pieChartItem.metric} data={pieChartItem} />
            ))}
        </FlexLayout>
    );
};

const TaskPieChartContent = ({stageName}: TaskPieChartContentProps) => {
    const [taskInfo] = useTaskInfo();
    const defaultActiveKey = `${head(taskInfo?.modelParams)?.modelID}`;
    const [modelID, setModelID] = useState(defaultActiveKey);

    const items = useMemo(
        () =>
            taskInfo?.modelParams?.map(item => ({
                label: item.modelName,
                key: `${item.modelID}`,
            })),
        [taskInfo?.modelParams]
    );

    return (
        <>
            <Tabs
                defaultActiveKey={defaultActiveKey}
                items={items}
                onChange={setModelID}
            />
            <TaskPieChartContainer stageName={stageName} modelID={modelID} />
        </>
    );
};

export default TaskPieChartContent;
