import {Flex} from 'antd';
import {TaskInfoItem} from '@/api/ievalue/task';

interface Props {
    taskInfo: TaskInfoItem;
}

const RejectRateInfo = ({taskInfo}: Props) => {
    if (taskInfo?.withReject) {
        if (taskInfo?.rejectRateParams?.length) {
            return (
                <Flex vertical gap={4}>
                    {taskInfo.rejectRateParams.map((item, index) => (
                        <div key={index}>
                            {item.stageName}: {item.rejectRatio}%
                        </div>
                    ))}
                </Flex>
            );
        }
    }

    return '-';
};

export default RejectRateInfo;
