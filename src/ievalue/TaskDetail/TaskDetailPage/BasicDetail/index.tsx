/* eslint-disable max-lines */
import {ClockCircleOutlined, CloseOutlined, SmileOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {
    Button,
    Descriptions,
    Dropdown,
    MenuProps,
    message,
    Popover,
    Space,
    Tag,
    Timeline,
    Typography,
} from 'antd';
import {useRequestCallback} from 'huse';
import {head} from 'lodash';
import {useCallback, useEffect, useMemo} from 'react';
import {Link, useParams} from 'react-router-dom';
import dayjs from 'dayjs';
import {apiEvaluateAutoCheck} from '@/api/ievalue/case';
import {
    TaskInfoItem,
    apiDisassociateCard,
    apiTaskPredictCancel,
    apiTaskPredictRetry,
} from '@/api/ievalue/task';
import {DownloadButton} from '@/components/ievalue/DownloadButton';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {ModelTypeEnum} from '@/constants/ievalue/model';
import {
    CooperateTypeMap,
    TargetEnum,
    TaskEvaluateModeMap,
    TaskPredictRoundMap,
    TaskStageEnum,
    TaskStatusEnum,
    TemplateStageTypesEnum,
} from '@/constants/ievalue/task';
import {MenuInfo} from '@/types/ievalue/common';
import {useIsYiYanSpace, useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    useEvaluateDatasetFileList,
    useReordCardList,
    useTaskInfo,
    useTaskModelList,
    useTaskModelLogInfo,
    useTaskPromptVersionList,
    useTaskStageList,
    useTaskStrategyInfo,
} from '@/hooks/ievalue/task';
import {getTaskStatus} from '@/utils/ievalue/task';
import {PromptUrl} from '@/links/ievalue/prompt';
import {ReportUrl} from '@/links/ievalue/report';
import {SpaceUrl} from '@/links/ievalue/space';
import {CollapsePanel} from '@/components/ievalue/CollapsePanel';
import {PromptAuthLink} from '@/components/ievalue/PromptAuthLink';
import {VisibleAutoEvaluateCancelButton} from '../components/VisibleAutoEvaluateCancelButton';
import {VisibleAutoEvaluateRestartAndRetryDropdown} from '../components/VisibleAutoEvaluateRestartAndRetryDropdown';
import ChatGLMBatchModelTaskInfo from '../components/ChatGLMBatchModelTaskInfo';
import {DownloadEvaluateFile} from '../components/DownloadEvaluateFile';
import {TaskMoreOptionButton} from '../TaskMoreOptionButton';
import PolicyInfoTipTag from '../components/PolicyInfoTipTag';
import {TerminatedTaskButton} from '../components/TerminatedTaskButton';
import ShareTaskButton from '../components/ShareTaskButton';
import AddTaskTag from '../components/AddTaskTag';
import TaskModelTags from '../components/TaskModelTags';
import {AllCompareReportList} from './AllCompareReportList';
import DuZhiLiaoStageInfoMap from './DuZhiLiaoStageInfoMap';
import LineUpMsg from './LineUpMsg';
import FailMsg from './FailMsg';
import {LogDownload} from './LogDownload';
import ShowJsonParamButton from './ShowJsonParamButton';
import RejectRateInfo from './RejectRateInfo';
import SamplingRatioInfo from './SamplingRatioInfo';

enum RetryRangeEnum {
    ALL = 'ALL',
    FAIL = 'FAIL',
}

const getRetryDropdownItems = (taskInfo: TaskInfoItem): MenuProps['items'] => {
    return [
        {
            key: RetryRangeEnum.FAIL,
            label: '重试失败',
        },
        {
            key: RetryRangeEnum.ALL,
            label: '重试全部',
            disabled: !!taskInfo.dispatchType
                && [TaskStatusEnum.CANCEL, TaskStatusEnum.RETRY].includes(
                    taskInfo.stageStatus as TaskStatusEnum
                ),
        },
    ];
};

export const StyledLink = styled(Link)`
    color: inherit;
    display: inline-flex;
    gap: 4px;
    align-items: center;

    &:hover {
        color: var(--color-brand-6);
    }
`;

const StyledTag = styled(Tag)`
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

// eslint-disable-next-line complexity
export const BasicDetail = () => {
    const {taskID} = useParams();
    const [taskInfo, taskInfoRefresh] = useTaskInfo();
    const [taskModelLogInfo] = useTaskModelLogInfo();
    const [taskModelList] = useTaskModelList();
    const [promptList] = useTaskPromptVersionList();
    const [stageInfo] = useTaskStageList();
    const [datasetFileList] = useEvaluateDatasetFileList();
    const strategyInfo = useTaskStrategyInfo();
    const [reordCardList, {refresh: recordCardRefresh}] = useReordCardList();
    const [load] = useRequestCallback(apiEvaluateAutoCheck, {
        taskID: Number(taskID),
    });
    const isYiYangSpace = useIsYiYanSpace();
    const spaceCode = useSpaceCodeSafe();
    const datasetFile = useMemo(
        () => {
            return head(datasetFileList);
        },
        [datasetFileList]
    );
    useEffect(
        () => {
            if (taskInfo.stage === 'AUTO_EVALUATE') {
                load();
            }
        },
        [load, taskInfo.stage]
    );

    const chatGLMBatchModelList = useMemo(
        () => {
            return (
                taskModelList?.filter(
                    item => item.modelType === ModelTypeEnum.ChatGLMBatch
                ) || []
            );
        },
        [taskModelList]
    );
    const onRemove = useCallback(
        async (ID: string) => {
            try {
                await apiDisassociateCard({ID});
                message.success('操作成功');
                recordCardRefresh();
            } catch (e) {
                /* empty */
            }
        },
        [recordCardRefresh]
    );

    const handleCancelPredict = useCallback(
        async () => {
            try {
                await apiTaskPredictCancel({taskID: taskInfo.taskID});
            } catch (e) {
                /* empty */
            } finally {
                taskInfoRefresh();
            }
        },
        [taskInfo.taskID, taskInfoRefresh]
    );

    const handleRetryPredict = useCallback(
        async (retryRange: RetryRangeEnum) => {
            try {
                await apiTaskPredictRetry({
                    taskID: taskInfo.taskID,
                    retryRange,
                });
            } catch (e) {
                /* empty */
            } finally {
                taskInfoRefresh();
            }
        },
        [taskInfo.taskID, taskInfoRefresh]
    );

    const handleRetryPredictClick: MenuProps['onClick'] = useCallback(
        ({key, domEvent}: MenuInfo) => {
            domEvent?.stopPropagation();
            handleRetryPredict(key as RetryRangeEnum);
        },
        [handleRetryPredict]
    );

    const OperateButton = useMemo(
        // eslint-disable-next-line complexity
        () => {
            switch (taskInfo.stage) {
                case TaskStageEnum.FINISHED:
                case TaskStageEnum.TERMINATED:
                    return (
                        stageInfo.map(e => e.stageType).join(',')
                        !== TemplateStageTypesEnum.NEW_PREDICTING && (
                            <>
                                <StyledLink
                                    to={ReportUrl.ReportTaskDetail.toUrl({
                                        spaceCode,
                                        taskID: taskInfo.taskID,
                                    })}
                                    target="_blank"
                                >
                                    <Button type="primary">查看报告</Button>
                                </StyledLink>
                                {!isYiYangSpace && (
                                    <Popover
                                        content={
                                            <div style={{width: '600px'}}>
                                                <AllCompareReportList />
                                            </div>
                                        }
                                    >
                                        <Button>查看对比报告</Button>
                                    </Popover>
                                )}
                            </>
                        )
                    );
                case TaskStageEnum.PREDICTING:
                    if (
                        [
                            TaskStatusEnum.WAITING,
                            TaskStatusEnum.RUNNING,
                        ].includes(taskInfo.stageStatus as TaskStatusEnum)
                    ) {
                        return (
                            <Button
                                type="default"
                                onClick={handleCancelPredict}
                            >
                                取消推理
                            </Button>
                        );
                    }
                    if (
                        [TaskStatusEnum.CANCEL, TaskStatusEnum.FAIL, TaskStatusEnum.RETRY].includes(
                            taskInfo.stageStatus as TaskStatusEnum
                        )
                    ) {
                        return (
                            <Dropdown
                                menu={{
                                    items: getRetryDropdownItems(taskInfo),
                                    onClick: handleRetryPredictClick,
                                }}
                            >
                                <Button>重试</Button>
                            </Dropdown>
                        );
                    }
                    return <></>;
                case TaskStageEnum.EVALUATING:
                case TaskStageEnum.AUTO_EVALUATE:
            }
        },
        [
            handleCancelPredict,
            handleRetryPredictClick,
            isYiYangSpace,
            spaceCode,
            stageInfo,
            taskInfo,
        ]
    );
    const cardContent = (
        <div>
            {reordCardList.map((item: any) => (
                <p key={item.id}>
                    <Tag>
                        {item.space}-{item.sequence}
                        <CloseOutlined
                            style={{margin: '0 0 0 10px'}}
                            onClick={() => onRemove(item.ID)}
                        />
                    </Tag>
                </p>
            ))}
        </div>
    );
    const StageInfoMapping: any = {
        [TaskStageEnum.NEW]: (
            <Descriptions size="middle">
                <Descriptions.Item label="评估对象类型">
                    {taskInfo.target}
                </Descriptions.Item>
                {taskInfo.target === 'Prompt' && (
                    <Descriptions.Item label="Prompt">
                        <Space split="|" wrap>
                            {promptList?.map(item => (
                                <PromptAuthLink
                                    key={item.promptID}
                                    to={PromptUrl.detail.toUrl({
                                        spaceCode: item.spaceCode,
                                        promptID: item.promptID,
                                        versionID: item.promptVersionID,
                                    })}
                                    authParams={{
                                        promptID: item.promptID,
                                        role: 'READER',
                                    }}
                                    target="_blank"
                                >
                                    {item.promptName + '-' + item.versionName}
                                </PromptAuthLink>
                            ))}
                        </Space>
                    </Descriptions.Item>
                )}
                {taskInfo.target === 'PromptFlow' && (
                    <Descriptions.Item label="PromptFlow">
                        <Space split="|" wrap>
                            {promptList?.map(item => (
                                <PromptAuthLink
                                    key={item.promptID}
                                    to={SpaceUrl.promptflowDetailHistory.toUrl({
                                        spaceCode,
                                        promptID: item.promptID,
                                        flowID: item.flowID,
                                        recordID: item.recordID,
                                    })}
                                    authParams={{
                                        promptID: item.promptID,
                                        role: 'READER',
                                    }}
                                    target="_blank"
                                >
                                    {item.promptName}
                                </PromptAuthLink>
                            ))}
                        </Space>
                    </Descriptions.Item>
                )}
                {taskInfo.taskType !== 'fastEvaluation'
                    && taskInfo.target !== 'Prompt'
                    && taskInfo.target !== 'PromptFlow' && (
                    <Descriptions.Item label="模型">
                        <TaskModelTags />
                    </Descriptions.Item>
                )}
                <Descriptions.Item label="对话轮次">
                    {TaskPredictRoundMap[taskInfo.predictRound]}
                </Descriptions.Item>
                <Descriptions.Item label="Case流转">
                    {taskInfo.dispatchType ? '按Case流转' : '按Group流转'}
                </Descriptions.Item>
                {taskInfo?.ordermadeParam && (
                    <Descriptions.Item label="自定义参数">
                        <ShowJsonParamButton data={taskInfo?.ordermadeParam} />
                    </Descriptions.Item>
                )}
                {taskInfo.target !== 'Prompt'
                    && taskInfo.target !== 'PromptFlow' && (
                    <Descriptions.Item label="关联卡片">
                        {reordCardList.slice(0, 2).map((item: any) => (
                            <Space size={[0, 8]} wrap key={item.id}>
                                <Tag>
                                    {item.space}-{item.sequence}
                                    <CloseOutlined
                                        style={{margin: '0 0 0 10px'}}
                                        onClick={() => onRemove(item.ID)}
                                    />
                                </Tag>
                            </Space>
                        ))}
                        {reordCardList.length > 2 && (
                            <Popover content={cardContent}>
                                <span>...</span>
                            </Popover>
                        )}
                    </Descriptions.Item>
                )}
            </Descriptions>
        ),
        [TaskStageEnum.PREDICTING]: (
            <Descriptions size="middle">
                <Descriptions.Item label="是否需要推理">
                    {taskInfo.predictType ? '是' : '否'}
                </Descriptions.Item>
                {chatGLMBatchModelList.length > 0 && (
                    <Descriptions.Item label="批量预测排队信息" span={2}>
                        <FlexLayout wrap="wrap" gap={4}>
                            {chatGLMBatchModelList.map(item => (
                                <ChatGLMBatchModelTaskInfo
                                    key={item.modelID}
                                    item={item}
                                />
                            ))}
                        </FlexLayout>
                    </Descriptions.Item>
                )}
                <Descriptions.Item label="推理方式">自动</Descriptions.Item>
                <Descriptions.Item label="推理日志">
                    <LogDownload
                        taskModelLogInfo={taskModelLogInfo}
                        taskInfo={taskInfo}
                    />
                </Descriptions.Item>
            </Descriptions>
        ),
        [TaskStageEnum.EVALUATING]: (
            <Descriptions size="middle">
                <Descriptions.Item label="评估集">
                    {datasetFile ? (
                        <DownloadButton
                            size="small"
                            fileUrl={datasetFile.url}
                            fileName={datasetFile.name}
                        />
                    ) : (
                        <span>暂不可下载</span>
                    )}
                </Descriptions.Item>
                <Descriptions.Item label="评估界面">
                    {taskInfo.showMethod === 'CARD' ? '答题' : '列表'}
                </Descriptions.Item>
                <Descriptions.Item label="评估方式">
                    {TaskEvaluateModeMap[taskInfo.evaluateMode]}
                </Descriptions.Item>
                <Descriptions.Item label="协作方式">
                    {CooperateTypeMap[taskInfo.cooperateType]}
                </Descriptions.Item>
                <Descriptions.Item label="评估策略">
                    <PolicyInfoTipTag strategyItem={strategyInfo} />
                </Descriptions.Item>
                <Descriptions.Item label="盲评">
                    {taskInfo.blind ? '是' : '否'}
                </Descriptions.Item>
                <Descriptions.Item label="优先级">
                    {taskInfo.priority}
                </Descriptions.Item>
                <Descriptions.Item label="抽样">
                    {taskInfo.isSampling ? '是' : '否'}
                </Descriptions.Item>
                {taskInfo.isSampling && (
                    <>
                        <Descriptions.Item label="抽样比例">
                            <SamplingRatioInfo taskInfo={taskInfo} />
                        </Descriptions.Item>
                        <Descriptions.Item label="抽样自动流转">
                            {taskInfo.autoSkip ? '是' : '否'}
                        </Descriptions.Item>
                    </>
                )}
                <Descriptions.Item label="批量打回">
                    {taskInfo.withReject ? '是' : '否'}
                </Descriptions.Item>
                {taskInfo.withReject && (
                    <Descriptions.Item label="准确率阈值">
                        <RejectRateInfo taskInfo={taskInfo} />
                    </Descriptions.Item>
                )}
            </Descriptions>
        ),
        [TaskStageEnum.AUDITING]: <>--</>,
        [TaskStageEnum.AUDITING_FORWARD]: <>--</>,
        [TaskStageEnum.ACCEPTING]: <>--</>,
        [TaskStageEnum.AUTO_EVALUATE]: (
            <Descriptions size="middle">
                <Descriptions.Item label="评估策略">
                    <PolicyInfoTipTag strategyItem={strategyInfo} />
                </Descriptions.Item>
                <Descriptions.Item label="评估维度">
                    <FlexLayout gap={4} wrap="wrap" style={{overflow: 'hidden'}}>
                        {strategyInfo?.metric?.map(item => (
                            <StyledTag key={item.desc}>
                                {item.desc}
                                {item?.models
                                    ? `（${head(item?.models)}）`
                                    : ''}
                            </StyledTag>
                        ))}
                    </FlexLayout>
                </Descriptions.Item>
                {taskInfo?.evaluateParam && (
                    <Descriptions.Item label="评估参数">
                        <ShowJsonParamButton data={taskInfo.evaluateParam} />
                    </Descriptions.Item>
                )}
            </Descriptions>
        ),
        [TaskStageEnum.FINISHED]: <>--</>,
    };
    const statusColorMap: Record<TaskStatusEnum, string> = {
        [TaskStatusEnum.SUCCESS]: 'green',
        [TaskStatusEnum.FAIL]: 'red',
        [TaskStatusEnum.RETRY]: 'red',
        [TaskStatusEnum.WAITING]: 'gray',
        [TaskStatusEnum.RUNNING]: 'blue',
        [TaskStatusEnum.CANCEL]: 'gray',
        [TaskStatusEnum.TERMINATED]: 'gray',
    };
    const timelineItems = useMemo(
        () => {
            return stageInfo.map((item, index) => {
                const isLast = index === stageInfo.length - 1;
                let dot = null;
                if (isLast) {
                    dot = <SmileOutlined />;
                } else if (item.status === TaskStatusEnum.RUNNING) {
                    dot = <ClockCircleOutlined style={{fontSize: '16px'}} />;
                }

                return {
                    label: (
                        <>
                            {item.stageName}
                            <br />
                            <span style={{color: '#888', fontSize: '12px'}}>
                                {item.startTime ? dayjs(item.startTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
                            </span>
                        </>
                    ),
                    children:
                        taskInfo.target === TargetEnum.DUZHILIAO
                            ? DuZhiLiaoStageInfoMap[item.stageType]
                            : StageInfoMapping[item.stageType],
                    color: statusColorMap[item.status],
                    dot: dot,
                };
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [stageInfo, taskInfo.target, DuZhiLiaoStageInfoMap, StageInfoMapping, statusColorMap]
    );
    return (
        <>
            <FlexLayout justify="space-between">
                <Space>
                    <Typography.Title level={4}>
                        {taskInfo.name}
                    </Typography.Title>
                    {taskInfo?.deleted === 1 ? (
                        <Tag color="#f50">已删除</Tag>
                    ) : (
                        <Tag color="blue">
                            {getTaskStatus(
                                taskInfo.stage,
                                taskInfo.stageStatus
                            )}
                        </Tag>
                    )}
                    <AddTaskTag />
                </Space>
                {taskInfo?.deleted !== 1 && (
                    <Space>
                        <TerminatedTaskButton />
                        {taskInfo.taskID > 0 && OperateButton}
                        <DownloadEvaluateFile />
                        <VisibleAutoEvaluateCancelButton />
                        <VisibleAutoEvaluateRestartAndRetryDropdown />
                        <ShareTaskButton />
                        <TaskMoreOptionButton
                            taskInfo={taskInfo}
                            refresh={taskInfoRefresh}
                        />
                    </Space>
                )}
            </FlexLayout>
            <FailMsg taskInfo={taskInfo} />
            <LineUpMsg taskInfo={taskInfo} />
            <CollapsePanel title="基本信息">
                <Timeline
                    mode="left"
                    items={timelineItems}
                />
            </CollapsePanel>
        </>
    );
};
