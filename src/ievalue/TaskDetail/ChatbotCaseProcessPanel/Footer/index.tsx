/* eslint-disable complexity */
import styled from '@emotion/styled';
import {Space, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {message} from '@panda-design/components';
import {useActionPending} from 'huse';
import {memo, useCallback, useEffect, useMemo} from 'react';
import {Boundary} from 'react-suspense-boundary';
import {
    apiCaseAuditForwardPass,
    apiCaseAuditPass,
    apiCaseEvaluatePass,
    apiChatClose,
} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGetPromptExcute,
    useGroupCaseID,
    useGroupCaseList,
    useGroupStageInfo,
    useIsCaseDispatch,
    useTaskStageID,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {AnswerCardButton} from '../../CaseProcessPanel/AnswerCardButton';
import {CaseDispatchJumpOverButton} from '../../CaseProcessPanel/CaseDispatchJumpOverButton';
import {CaseDispatchNextButton} from '../../CaseProcessPanel/CaseDispatchNextButton';
import {CaseDispatchOnlyNextButton} from '../../CaseProcessPanel/CaseDispatchOnlyNextButton';
import {DiffHistoryButton} from '../../CaseProcessPanel/DiffHistoryButton';
import {SamplingAuditButton} from '../../CaseProcessPanel/SamplingAuditButton';
import {SatisfactionSort} from '../../CaseProcessPanel/SatisfactionSort';
import {CaseDispatchSubmitButton} from '../../CaseProcessPanel/CaseDispatchSubmitButton';
import CaseDispatchAnswerCardButton from '../../CaseProcessPanel/CaseDispatchAnswerCardButton';
import {CaseButton} from './AnswerCardButton';

const FooterContent = styled.div`
    flex: 0 0 30px;
    width: 100%;
    padding-bottom: 20px;
`;

export const CaseProcessFooter = memo(() => {
    const {caseEvaluateRecords} = useCaseEvaluateList();
    const {groupCaseList, groupCaseTotal, groupCaseListRefresh} =
        useGroupCaseList(1, 100000);
    const [, , groupInfo] = useGroupStageInfo();
    const stageType = useCaseStageType();
    const isCaseDispatch = useIsCaseDispatch();
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    const stageID = useTaskStageID();
    const [caseIndex, preCaseItem, nextCaseItem, isLastCase] = useMemo(
        () => {
            const caseIndex = groupCaseList.findIndex(
                item => item.caseID === caseID
            );
            if (groupCaseList.length >= 2) {
                const preCaseId =
                caseIndex === 0 ? groupCaseTotal - 1 : caseIndex - 1;
                const nextCaseId =
                caseIndex === groupCaseTotal - 1 ? 0 : caseIndex + 1;
                const isLastCase = caseIndex === groupCaseTotal - 1;
                return [
                    caseIndex,
                    groupCaseList[preCaseId],
                    groupCaseList[nextCaseId],
                    isLastCase,
                ];
            }
            return [caseIndex, null, null, false];
        },
        [groupCaseList, groupCaseTotal, caseID]
    );
    useEffect(
        () => {
            groupCaseListRefresh?.();
        },
        // caseID 可能改变，需要重新获取
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [caseID]
    );

    const handleAuditFinish = useCallback(
        async () => {
            try {
                if (stageType === TaskStageEnum.AUDITING) {
                    await apiCaseAuditPass({caseID, taskID});
                } else {
                    const result = await apiCaseAuditForwardPass({
                        caseID,
                        taskID,
                        stageID,
                    });
                    if (result?.reachedThreshold) {
                        message.info('抽样比例已达成，可以随时结束当前阶段');
                    }
                }
                return true;
            } catch (e) {
            // eslint-disable-next-line no-console
                console.log(e);
                return false;
            }
        },
        [caseID, stageID, stageType, taskID]
    );

    const handleEvaluatePass = useCallback(
        async () => {
            try {
                await apiCaseEvaluatePass({caseID, taskID});
                if (groupCaseList.length < 2) {
                    await groupCaseListRefresh?.();
                    message.success('提交成功！');
                }
                return true;
            } catch (e) {
                return false;
            }
        },
        [caseID, groupCaseList.length, groupCaseListRefresh, taskID]
    );

    const [canAuditPass, canEvaluatePass, canAcceptancePass] = useMemo(
        () => {
            const canAuditPass =
            [TaskStageEnum.AUDITING, TaskStageEnum.AUDITING_FORWARD].includes(
                stageType
            )
            && ![CaseStatusEnum.RESOLVE_DISPUTE, CaseStatusEnum.REJECTED].includes(
                groupInfo.status
            );
            const canEvaluatePass =
            stageType === TaskStageEnum.EVALUATING
            || groupInfo.status === CaseStatusEnum.REJECTED;
            const canAcceptancePass = stageType === TaskStageEnum.ACCEPTING;
            return [canAuditPass, canEvaluatePass, canAcceptancePass];
        },
        [groupInfo.status, stageType]
    );
    const [actionChatClose] = useActionPending(apiChatClose);
    const [_, {refresh: predictRecordListRefresh}] = useGetPromptExcute();

    const submitNext = useCallback(
        async () => {
            await actionChatClose({caseID});
            predictRecordListRefresh();
            return canAuditPass ? handleAuditFinish() : handleEvaluatePass();
        },
        [
            actionChatClose,
            canAuditPass,
            caseID,
            handleAuditFinish,
            handleEvaluatePass,
            predictRecordListRefresh,
        ]
    );
    return (
        <FooterContent>
            <FlexLayout justify="space-between">
                <Boundary>
                    <SatisfactionSort />
                </Boundary>
                {isCaseDispatch ? (
                    <Space>
                        <CaseDispatchNextButton />
                        <CaseDispatchOnlyNextButton />
                        <CaseDispatchJumpOverButton />
                        <CaseDispatchSubmitButton />
                    </Space>
                ) : (
                    <Space align="center">
                        {groupCaseList.length >= 2
                        && !!preCaseItem
                        && !!nextCaseItem
                            ? (
                                <>
                                    <CaseButton
                                        caseItem={preCaseItem}
                                        title={'上一题'}
                                    />
                                    <CaseButton
                                        caseItem={nextCaseItem}
                                        title={'下一题'}
                                        type="primary"
                                        isLastCase={isLastCase}
                                    />
                                    {groupInfo.status !== CaseStatusEnum.FINISH
                                    && (canEvaluatePass || canAuditPass) && (
                                        <CaseButton
                                            caseItem={nextCaseItem}
                                            title={'提交&下一题'}
                                            type="primary"
                                            onClick={submitNext}
                                            checkISCompleted={
                                                !canAuditPass
                                                && !canAcceptancePass
                                            }
                                            caseEvaluateRecords={
                                                caseEvaluateRecords
                                            }
                                            isLastCase={isLastCase}
                                        />
                                    )}
                                </>
                            ) : (
                                <>
                                    {(canEvaluatePass || canAuditPass) && (
                                        <Button
                                            type="primary"
                                            onClick={
                                                canAuditPass
                                                    ? handleAuditFinish
                                                    : handleEvaluatePass
                                            }
                                        >
                                            提交
                                        </Button>
                                    )}
                                </>
                            )}
                        <SamplingAuditButton
                            handleSubmit={submitNext}
                            stageType={stageType}
                        />
                        <Typography.Text style={{width: 40}}>
                            {`${caseIndex + 1}/${groupCaseTotal}`}
                        </Typography.Text>
                    </Space>
                )}
                <Space align="center">
                    {isCaseDispatch ? <CaseDispatchAnswerCardButton /> : <AnswerCardButton />}
                    <DiffHistoryButton />
                </Space>
            </FlexLayout>
        </FooterContent>
    );
});
