import {Input} from 'antd';
import {But<PERSON>, message} from '@panda-design/components';
import {useActionPending} from 'huse';
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState, KeyboardEvent} from 'react';
import {
    Query,
    SendMsgResponse,
    apiChatCreate,
    apiChatSendQuery,
    apiGroupCaseQueryAdd,
} from '@/api/ievalue/case';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {TaskModelItem} from '@/api/ievalue/task';
import {IconSend} from '@/icons/ievalue';
import {ChatStatusEnum, ChatStatusMap} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {
    useCaseStageType,
    useChatState,
    useFindModel,
    useGroupCaseID,
    useGroupChatContext,
    useWatchStatus,
} from '@/hooks/ievalue/task';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import styles from './index.module.less';
interface ChangeEvent {
    target: {
        value: string;
    };
}
interface SendToolBarProps {
    isEdit?: boolean;
    sendingQueryItem?: Query;
    onAddQuerySucceed?: (item: Query) => void;
    onSendMsgSucceed?: (response: SendMsgResponse) => void;
    setToolbarHeight: (height: number) => void;
    onRefreshList?: (data: PromptExcuteResultListItem) => void;
}

const ToolBar = (
    {
        data,
        onSendMsgSucceed,
        isEdit,
        sendingQueryItem,
        setToolbarHeight,
        onRefreshList,
    }: SendToolBarProps & {data: PromptExcuteResultListItem},
    ref: any
) => {
    const {ID: predictRecordID} = data || {};
    const inputRef = useRef<any>();
    const [value, setValue] = useState('');
    const containerRef = useRef<any>();
    const caseID = useGroupCaseID();
    const [actionChatSendQuery, pendingCount] = useActionPending(apiChatSendQuery);
    const {statusItem, getStatusItem} = useChatState(data.modelID || 0);
    const stageType = useCaseStageType();
    const [actionChatCreate] = useActionPending(apiChatCreate);
    const {startPolling: startStatusPolling} = useWatchStatus(data.modelID);
    const model = useFindModel((model: TaskModelItem) => model.modelID === data.modelID);
    const sendMsg = useCallback(
        async (input: string, onSendSuccessCallback?: (response: SendMsgResponse) => void) => {
            try {
                setValue('');
                const statusItem = getStatusItem();
                if (
                    statusItem?.status === ChatStatusEnum.ChatClosed
                    || statusItem?.status === ChatStatusEnum.ChatStop
                    || statusItem?.status === ChatStatusEnum.ChatFailed
                    || statusItem?.status === ChatStatusEnum.ChatCreating
                    || statusItem?.status === ChatStatusEnum.ChatRunning
                ) {
                    // 封存
                    message.warning(
                        `${model?.model}:该会话${ChatStatusMap[statusItem?.status]}，无法发送消息`
                    );
                    return;
                }
                if (statusItem?.status === ChatStatusEnum.ChatNotExist) {
                    // 创建会话
                    const res = await actionChatCreate({caseID, predictRecordID});
                    onRefreshList?.(res?.[0]);
                    // 创建会话后端是异步，需要轮巡等待会话创建成功后刷新列表
                    startStatusPolling({
                        status: ChatStatusEnum.ChatCreating,
                        callback: async () => {
                            const resut = await actionChatSendQuery({
                                caseID,
                                predictRecordID,
                                input,
                            });
                            onSendMsgSucceed?.(resut);
                            onSendSuccessCallback?.(resut);
                        },
                    });
                } else {
                    const resut = await actionChatSendQuery({
                        caseID,
                        predictRecordID,
                        input,
                    });
                    onSendMsgSucceed?.(resut);
                    onSendSuccessCallback?.(resut);
                }
            } catch (error) {
                /**  */
                // eslint-disable-next-line no-console
                console.log(error);
            }
        },
        [
            actionChatCreate,
            actionChatSendQuery,
            caseID,
            getStatusItem,
            model?.model,
            onRefreshList,
            onSendMsgSucceed,
            predictRecordID,
            startStatusPolling,
        ]
    );

    const onSendMsg = useCallback(
        () => {
            sendMsg(value);
        },
        [value, sendMsg]
    );

    const updateContainerHeight = useCallback(
        () => {
            setToolbarHeight(containerRef.current?.clientHeight || 40);
        },
        [setToolbarHeight]
    );

    useImperativeHandle(
        ref,
        () => ({
            sendMsg,
        }),
        [sendMsg]
    );

    const onChange = useCallback(
        (e: ChangeEvent) => {
            setValue(e.target.value);
        },
        []
    );
    const onKeyDown = useCallback(
        (e: KeyboardEvent<HTMLTextAreaElement>) => {
            if (e.keyCode === 13 && e.metaKey) {
                setValue(value + '\n');
                e.preventDefault();
            } else if (e.keyCode === 13) {
                if (value.trim() === '') {
                    return;
                }
                onSendMsg();
            }
        },
        [onSendMsg, value]
    );

    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();

    const NotEvaluatIng = useMemo(
        () => {
        // 已完成或者不是评估状态的，不可操作
            return groupCaseInfo.status === 'FINISH' || ![TaskStageEnum.EVALUATING].includes(stageType);
        },
        [groupCaseInfo.status, stageType]
    );

    const disabled = useMemo(
        () => {
            return (
                !!sendingQueryItem
            || !!pendingCount
            || (statusItem?.status !== ChatStatusEnum.ChatReady
                && statusItem?.status !== ChatStatusEnum.ChatNotExist)
            || NotEvaluatIng
            );
        },
        [NotEvaluatIng, pendingCount, sendingQueryItem, statusItem?.status]
    );
    return (
        <div className={styles.sendBox} style={{display: isEdit ? 'block' : 'none'}}>
            <div className={styles.sendContiner} ref={containerRef}>
                <Input.TextArea
                    key={predictRecordID}
                    ref={inputRef}
                    autoSize={{minRows: 1, maxRows: 4}}
                    autoFocus={false}
                    disabled={disabled}
                    bordered={false}
                    placeholder="请输入您的问题，cmd+回车换行"
                    value={value}
                    onChange={onChange}
                    onKeyDown={onKeyDown}
                    onResize={updateContainerHeight}
                />
                <Button
                    loading={!!pendingCount}
                    disabled={disabled || value.trim() === ''}
                    type="text"
                    icon={<IconSend />}
                    onClick={onSendMsg}
                />
            </div>
        </div>
    );
};

export const SendToolBar = forwardRef(ToolBar);

export const QuerySendToolBar = memo(
    ({onAddQuerySucceed, sendingQueryItem, setToolbarHeight}: SendToolBarProps) => {
        const inputRef = useRef<any>();
        const containerRef = useRef<any>();
        const caseID = useGroupCaseID();
        const [value, setValue] = useState('');
        const stageType = useCaseStageType();
        const [actionGroupCaseQueryAdd, pendingCount] = useActionPending(apiGroupCaseQueryAdd);
        const onAddQuery = useCallback(
            async () => {
                setValue('');
                const data = await actionGroupCaseQueryAdd({caseID, input: value});
                onAddQuerySucceed?.(data as Query);
            },
            [actionGroupCaseQueryAdd, caseID, value, onAddQuerySucceed]
        );
        const onChange = useCallback(
            (e: ChangeEvent) => {
                setValue(e.target.value);
            },
            []
        );
        const onQueryKeyDown = useCallback(
            (e: KeyboardEvent<HTMLTextAreaElement>) => {
                if (e.keyCode === 13 && e.metaKey) {
                    setValue(value + '\n');
                    e.preventDefault();
                } else if (e.keyCode === 13) {
                    if (value.trim() === '') {
                        return;
                    }
                    onAddQuery();
                }
            },
            [onAddQuery, value]
        );
        const updateContainerHeight = useCallback(
            () => {
                setToolbarHeight(containerRef.current?.clientHeight || 40);
            },
            [setToolbarHeight]
        );
        const groupCaseInfo = useEvaluateCommonGroupCaseInfo();

        const NotEvaluatIng = useMemo(
            () => {
            // 已完成或者不是评估状态的，不可操作
                return (
                    groupCaseInfo.status === 'FINISH' || ![TaskStageEnum.EVALUATING].includes(stageType)
                );
            },
            [groupCaseInfo.status, stageType]
        );
        const {statusData} = useGroupChatContext();
        const disabled = useMemo(
            () => {
                const data =
                statusData?.filter(
                    e =>
                        e.status !== ChatStatusEnum.ChatReady
                        && e.status !== ChatStatusEnum.ChatNotExist
                ) || [];
                return !!sendingQueryItem || !!pendingCount || data.length > 0 || NotEvaluatIng;
            },
            [NotEvaluatIng, pendingCount, sendingQueryItem, statusData]
        );
        return (
            <div className={styles.sendBox}>
                <div className={styles.sendContiner} ref={containerRef}>
                    <Input.TextArea
                        key={'query'}
                        ref={inputRef}
                        autoSize={{minRows: 1, maxRows: 4}}
                        autoFocus={false}
                        disabled={disabled}
                        bordered={false}
                        placeholder="请输入Query，cmd+回车换行"
                        value={value}
                        onChange={onChange}
                        onKeyDown={onQueryKeyDown}
                        onResize={updateContainerHeight}
                    />
                    <Button
                        loading={!!pendingCount}
                        disabled={disabled || value.trim() === ''}
                        type="text"
                        icon={<IconSend />}
                        onClick={onAddQuery}
                    />
                </div>
            </div>
        );
    }
);
