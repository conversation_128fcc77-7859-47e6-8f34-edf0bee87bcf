import {CopyOutlined} from '@ant-design/icons';
import {createIcon} from '@panda-design/components';
import {Space, Typography} from 'antd';
import {useCallback} from 'react';
import {Query} from '@/api/ievalue/case';
import Details from '@/assets/ievalue/Details';
import ChatIcon from '@/assets/ievalue/Chat';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {copyToClipboard} from '@/utils/ievalue';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
export const IconDetails = createIcon(Details);

export const QueryHeader = () => {
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const onCopy = useCallback(
        () => {
            let text = '';
            groupCaseInfo?.queries?.forEach((e: Query) => {
                if (e.input) {
                    text = `${text}Q：${e.input}\n`;
                }
                if (e.referenceOutput) {
                    text = `${text}A：${e.referenceOutput}\n`;
                }
            });
            copyToClipboard(text);
        },
        [groupCaseInfo?.queries]
    );
    return (
        <FlexLayout align="center" justify="space-between">
            <Space>
                <span style={{color: '#BC6B31', fontSize: '12px'}}>
                    <ChatIcon
                        style={{color: '#BC6B31', verticalAlign: 'middle', margin: '0 5px 0 0'}}
                    />
                    Query&参考答案
                </span>
            </Space>
            <Typography.Text
                copyable={{
                    text: '',
                    onCopy,
                    tooltips: '复制对话',
                    icon: <CopyOutlined style={{color: '#000', width: '12px', height: '12px'}} />,
                }}
            />
        </FlexLayout>
    );
};
