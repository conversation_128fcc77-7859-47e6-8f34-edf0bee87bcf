import {Markdown} from '@/design/Markdown';
import {CollapsePanel} from '@/components/ievalue/CollapsePanel';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
export const SystemInfo = () => {
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    return (
        <CollapsePanel
            title={
                <span style={{color: '#BC6B31', fontSize: '12px'}}>
                    系统人设
                </span>
            }
        >
            <Markdown content={groupCaseInfo?.system ?? ''} codeHighlight={false} />
        </CollapsePanel>
    );
};
