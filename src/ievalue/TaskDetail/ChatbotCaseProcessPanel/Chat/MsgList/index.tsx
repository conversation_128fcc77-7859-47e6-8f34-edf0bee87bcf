/* eslint-disable max-lines */
/* eslint-disable max-len */
import styled from '@emotion/styled';
import {List} from 'antd';
import {mean, round} from 'lodash';
import {
    forwardRef,
    memo,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
} from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import {css, cx} from '@emotion/css';
import {Boundary} from 'react-suspense-boundary';
import {
    apiEvaluateCaseUpsert,
    CaseEvaluateRecordItem,
    ChatRecordItem,
    GroupCaseInfoItem,
    Query,
} from '@/api/ievalue/case';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import useSmoothScroll from '@/hooks/ievalue/useSmoothScroll';
import {
    useCacheChatRecordList,
    useCaseEvaluateList,
    useCaseStageType,
    useChatRecordList,
    useGroupPanekContext,
    useGroupStageInfo,
    useTaskInfo,
    useTaskStrategyInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {useChatIsSingleScrol} from '@/providers/TaskDetailProviders/ChatExtendInfoProvider';
import {useEvaluateCommonGroupCaseInfo, useEvaluateCommonGroupCaseInfoRefresh} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {ScoreForm} from '../Score';
import {BotItem, CustomerItem} from './Item';
import {QueryBotItem, QueryCustomerItem} from './QueryItem';
import MoreInfo from './Item/MoreInfo';
const listContainer = css`
    overflow: auto;
    box-sizing: border-box;
    flex: 1;
    ::-webkit-scrollbar {
        display: none;
    }
`;
const singleListContainer = css`
    flex: 1;
`;
const ListLayout = styled(List)`
    .ant-5-list-item {
        padding: 0;
        border-block-end: 0;
    }
`;

const gropMsg = css`
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0px;
`;

interface IMsgPops {
    data?: PromptExcuteResultListItem;
    groupCaseInfo?: GroupCaseInfoItem;
    height?: string;
    onGroupSendQuery?: (item: Query) => void;
    sendingQueryItem?: Query;
    isEdit?: boolean;
    isFullScreen?: boolean;
}

const MsgItem = ({
    item,
    index,
    data,
    openScore,
    setOpenScore,
    refresh,
    onSave,
    getPopupContainer,
    isFullScreen,
}: {
    item: Query | ChatRecordItem;
    index?: number;
    onSave: (item: ChatRecordItem) => void;
    data?: PromptExcuteResultListItem;
    openScore: boolean;
    setOpenScore: (openScore: boolean) => void;
    refresh: () => void;
    getPopupContainer?: () => HTMLElement;
    isFullScreen?: boolean;
}) => {
    return (
        <List.Item key={item.ID}>
            <div className={gropMsg}>
                <MoreInfo item={item as ChatRecordItem} />
                {item?.ID && (
                    <CustomerItem item={item as ChatRecordItem} index={index} />
                )}
                {item?.ID && (
                    <BotItem
                        item={item as ChatRecordItem}
                        index={index}
                        data={data}
                        onSave={onSave}
                        openScore={openScore}
                        setOpenScore={setOpenScore}
                        refresh={refresh}
                        getPopupContainer={getPopupContainer}
                        isFullScreen={isFullScreen}
                    />
                )}
            </div>
        </List.Item>
    );
};

export const MsgList = memo(
    forwardRef(
        // eslint-disable-next-line max-statements
        (
            {
                height,
                data,
                openScore,
                setOpenScore,
                isFullScreen,
            }: IMsgPops & {
                openScore: boolean;
                setOpenScore: (openScore: boolean) => void;
            },
            ref?: any
        ) => {
            const {open} = useGroupPanekContext();
            const {ID: predictRecordID, chatTaskID, modelID} = data || {};
            const strategyInfo = useTaskStrategyInfo();
            const [taskInfo] = useTaskInfo();
            const msgListRef = useRef<any>(
                document.getElementById('scrollableDiv')
            );
            const {list, setList} = useCacheChatRecordList();
            const timerRef = useRef<any>();
            const {data: listData, refresh} = useChatRecordList({
                predictRecordID,
                chatTaskID,
            });
            const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
            const {caseEvaluateRecords, caseEvaluateRefresh} =
                useCaseEvaluateList();
            const [, _, groupInfo] = useGroupStageInfo();
            const stageType = useCaseStageType();

            const taskID = useTaskTaskID();

            const recordItem = useMemo(
                () => {
                    if (predictRecordID) {
                        return (
                            caseEvaluateRecords.find(
                                e => e.origin.predictRecordID === predictRecordID
                            ) || ({} as CaseEvaluateRecordItem)
                        );
                    }
                    return (
                        caseEvaluateRecords.find(
                            e => e.origin.modelID === modelID
                        ) || ({} as CaseEvaluateRecordItem)
                    );
                },
                [caseEvaluateRecords, modelID, predictRecordID]
            );

            const blockHandle = useMemo(
                () => {
                    return (
                        groupInfo.status === CaseStatusEnum.FINISH
                    || stageType === TaskStageEnum.FEEDBACK
                    || (stageType === TaskStageEnum.EVALUATING
                        && groupInfo.status === CaseStatusEnum.REJECTED)
                    || ((groupCaseInfo.status === CaseStatusEnum.FINAL_DISPUTE
                        || recordItem.origin.status
                            === CaseStatusEnum.FINAL_DISPUTE)
                        && stageType !== TaskStageEnum.ACCEPTING)
                    || ([
                        TaskStageEnum.AUDITING,
                        TaskStageEnum.AUDITING_FORWARD,
                    ].includes(stageType)
                        && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE
                        && recordItem.origin.status === CaseStatusEnum.SUBMIT)
                    );
                },
                [
                    groupCaseInfo.status,
                    groupInfo.status,
                    stageType,
                    recordItem.origin.status,
                ]
            );

            const needUpdateRecord = useMemo(
                () => {
                    return (
                        ([TaskStageEnum.AUDITING].includes(stageType)
                        && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE)
                    || stageType === TaskStageEnum.ACCEPTING
                    );
                },
                [groupInfo.status, stageType]
            );

            const handleAutoUpdateSort = useCallback(
                async (updateMetric: string, updateScore: number) => {
                    // 非当前操作
                    const isNotOperationRecords = caseEvaluateRecords?.filter(
                        i =>
                            i.origin.predictRecordID
                            !== recordItem.origin.predictRecordID
                    );
                    if (
                        stageType === TaskStageEnum.EVALUATING
                        || needUpdateRecord
                    ) {
                        const {needSort, sortMetric} = strategyInfo;
                        if (
                            !!needSort
                            && updateMetric === sortMetric
                            && caseEvaluateRecords
                            && isNotOperationRecords.every(
                                // todo 去除操作当条
                                item =>
                                    item?.origin?.score
                                    && item?.origin?.score?.some(
                                        scoreItem =>
                                            scoreItem?.metric === sortMetric
                                            && scoreItem?.score !== null
                                    )
                            )
                        ) {
                            const sortList = caseEvaluateRecords
                                .map(record => ({
                                    id: record.origin.predictRecordID,
                                    score:
                                        record.origin.predictRecordID
                                        === recordItem.origin.predictRecordID
                                            ? updateScore
                                            : record.origin.score.find(
                                                (item: any) =>
                                                    item?.metric
                                                      === sortMetric
                                            ).score,
                                }))
                                .sort((a, b) => b.score - a.score);

                            const newRanking = sortList.map(item => item.id);
                            const current = sortList.reduce(
                                (result: string[], curr, index, arr) => {
                                    if (index > 0) {
                                        if (curr.score > arr[index - 1].score) {
                                            result.push('<');
                                        } else if (
                                            curr.score < arr[index - 1].score
                                        ) {
                                            result.push('>');
                                        } else {
                                            result.push('=');
                                        }
                                    }
                                    return result;
                                },
                                []
                            );
                            await apiEvaluateCaseUpsert({
                                taskID,
                                stageID: groupCaseInfo.stageID,
                                caseID: groupCaseInfo.caseID,
                                groupID: groupCaseInfo.groupID,
                                ranking: newRanking,
                                operators: current,
                            });
                        }
                    }
                },
                [
                    caseEvaluateRecords,
                    groupCaseInfo.caseID,
                    groupCaseInfo.groupID,
                    groupCaseInfo.stageID,
                    needUpdateRecord,
                    recordItem.origin.predictRecordID,
                    stageType,
                    strategyInfo,
                    taskID,
                ]
            );

            const handleChange = useCallback(
                async (value: number, metric: string) => {
                    if (blockHandle) {
                        return;
                    }
                    try {
                        await handleAutoUpdateSort?.(metric, value);
                    } catch (error) {
                        /* empty */
                    }
                },
                [blockHandle, handleAutoUpdateSort]
            );

            useEffect(
                () => {
                    setList({
                        predictRecordID,
                        chatTaskID,
                        list: listData,
                    });
                },
                // eslint-disable-next-line react-hooks/exhaustive-deps
                [chatTaskID, listData, predictRecordID]
            );

            useEffect(
                () => {
                    const handleScoreAge = async () => {
                        await Promise.all(
                            strategyInfo?.metric?.map(item => {
                                const metricScoreList = listData?.map(
                                    chatItem =>
                                        chatItem?.score?.find(
                                            (i: any) => i.metric === item.metric
                                        )?.score || 0
                                );
                                const metricAgeScore = round(
                                    mean(metricScoreList),
                                    4
                                );
                                return handleChange(
                                    metricAgeScore,
                                    item.metric
                                );
                            })
                        );
                        await caseEvaluateRefresh?.();
                    };
                    if (listData && !!taskInfo.isSessionAutoScore) {
                        handleScoreAge();
                    }
                },
                // eslint-disable-next-line react-hooks/exhaustive-deps
                [listData, strategyInfo?.metric, taskInfo.isSessionAutoScore]
            );

            // const list = useMemo(
            //     () => {
            //         const cacheList = getList({
            //             predictRecordID,
            //             chatTaskID,
            //         });

            //         return listData.length > 0 ? listData : cacheList || ([] as ChatRecordItem[]);
            //     },
            //     [chatTaskID, getList, listData, predictRecordID]
            // );

            const next = useCallback(
                () => {
                // eslint-disable-next-line no-console
                    console.log('next');
                },
                []
            );

            const {scrollTo, reachedBottom} = useSmoothScroll({
                ref: msgListRef,
                speed: 300,
                direction: 'y',
            });
            const scrollToBottom = useCallback(
                () => {
                    if (timerRef.current) {
                        clearInterval(timerRef.current);
                    }
                    let count = 10;
                    if (msgListRef.current) {
                        timerRef.current = setInterval(() => {
                            if (reachedBottom || count <= 0) {
                                clearInterval(timerRef.current);
                            }
                            count--;
                            scrollTo(0, msgListRef.current?.scrollHeight);
                        }, 100);
                    }
                },
                [reachedBottom, scrollTo]
            );

            useImperativeHandle(
                ref,
                () => ({
                    refresh,
                    scrollToBottom,
                    getList: () => {
                        return list;
                    },
                }),
                [list, refresh, scrollToBottom]
            );

            /*
    const Loading = () => {
        return (
            <Space className={styles.noMore}>
                <Spin
                    indicator={<OperateLoading className={styles.loading} />}
                    spinning
                    delay={0}
                />
                正在加载中...
            </Space>
        );
    };

    const LoadEnd = () => {
        return (
            <div className={styles.noMore}>
                <Typography.Paragraph style={{color: '#8f8f8f'}}>
                    ~刷完啦,已经是最新消息~
                </Typography.Paragraph>
            </div>
        );
    };
*/

            const onSave = useCallback(
                (item: ChatRecordItem) => {
                    setList({
                        predictRecordID,
                        chatTaskID,
                        list: list.map((e: ChatRecordItem) =>
                            (e.ID === item.ID ? item : e)
                        ),
                    });
                },
                [chatTaskID, list, predictRecordID, setList]
            );

            const getPopupContainer = useCallback(
                () => {
                    return document.getElementById(
                        `${isFullScreen}_scorePannel${data?.modelID}`
                    );
                },
                [data?.modelID, isFullScreen]
            );

            const renderItem = useCallback(
                (item: ChatRecordItem, index: number) => {
                    return (
                        <MsgItem
                            item={item}
                            index={index}
                            onSave={onSave}
                            isFullScreen={isFullScreen}
                            data={data}
                            openScore={openScore}
                            setOpenScore={setOpenScore}
                            refresh={refresh}
                            getPopupContainer={getPopupContainer}
                        />
                    );
                },
                [data, getPopupContainer, isFullScreen, onSave, openScore, refresh, setOpenScore]
            );
            const isSingleScrol = useChatIsSingleScrol();
            const showScoreComp = useMemo(
                () => {
                    return open && !isFullScreen && !isSingleScrol;
                },
                [isFullScreen, isSingleScrol, open]
            );
            return (
                <div
                    ref={msgListRef}
                    className={isSingleScrol ? singleListContainer : cx(singleListContainer, listContainer)}
                    style={isSingleScrol ? undefined : {height: height || 'calc(100% - 40px - 40px)'}}
                    id={`scrollableDiv${data?.modelID}`}
                >
                    {showScoreComp && (
                        <Boundary>
                            <div id={`parent${data?.modelID}`}>
                                <ScoreForm modelID={data.modelID} style={{padding: 14}} getPopupContainer={() => document.getElementById(`parent${data?.modelID}`)} />
                            </div>
                        </Boundary>
                    )}
                    <InfiniteScroll
                        dataLength={list?.length || 0}
                        next={next}
                        hasMore={false}
                        inverse={false}
                        loader={null}
                        endMessage={null}
                        scrollableTarget={`scrollableDiv${data?.modelID}`}
                        style={{
                            overflow: 'none',
                            display: 'flex',
                            flexDirection: 'column-reverse',
                        }}
                        pullDownToRefreshThreshold={50}
                    >
                        <ListLayout
                            id={`${isFullScreen}_scorePannel${data?.modelID}`}
                        >
                            <List
                                key="msgList"
                                dataSource={list || []}
                                split={false}
                                renderItem={renderItem}
                                style={{padding: 0}}
                            />
                        </ListLayout>
                    </InfiniteScroll>
                </div>
            );
        }
    )
);
const QueryMsgItem = ({
    item,
    sendingQueryItem,
    onGroupSendQuery,
}: {
    item: Query;
    onGroupSendQuery?: (item: Query) => void;
    sendingQueryItem?: Query;
}) => {
    return (
        <div className={gropMsg}>
            {item?.input && (
                <QueryCustomerItem
                    sendingQueryItem={sendingQueryItem}
                    data={item}
                    sendQuery={onGroupSendQuery}
                />
            )}
            {item?.referenceOutput && <QueryBotItem data={item} />}
        </div>
    );
};

const ListComponent = ({
    groupCaseInfo,
    sendingQueryItem,
    onGroupSendQuery,
}: {
    groupCaseInfo: GroupCaseInfoItem;
    onGroupSendQuery?: (item: Query) => void;
    sendingQueryItem?: Query;
}) => {
    const next = useCallback(
        () => {},
        []
    );
    const renderItem = useCallback(
        (item: Query) => {
            return (
                <QueryMsgItem
                    item={item}
                    sendingQueryItem={sendingQueryItem}
                    onGroupSendQuery={onGroupSendQuery}
                />
            );
        },
        [onGroupSendQuery, sendingQueryItem]
    );
    return (
        <InfiniteScroll
            dataLength={groupCaseInfo?.queries?.length || 0}
            next={next}
            hasMore={false}
            inverse
            loader={null}
            endMessage={null}
            scrollableTarget="scrollableQueryDiv"
            style={{
                overflow: 'none',
                display: 'flex',
                flexDirection: 'column-reverse',
            }}
        >
            <List
                key="msgList"
                dataSource={groupCaseInfo?.queries || []}
                split={false}
                renderItem={renderItem}
                style={{padding: 0}}
            />
        </InfiniteScroll>
    );
};

export const QueryMsgList = memo(
    forwardRef(
        (
            {onGroupSendQuery, sendingQueryItem, height}: IMsgPops,
            ref: any
        ) => {
            const msgListRef = useRef<any>(
                document.getElementById('scrollableQueryDiv')
            );
            const timerRef = useRef<any>();
            const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
            const groupCaseInfoRefresh = useEvaluateCommonGroupCaseInfoRefresh();
            const {scrollTo, reachedBottom} = useSmoothScroll({
                ref: msgListRef,
                speed: 300,
                direction: 'y',
            });
            const scrollToBottom = useCallback(
                () => {
                    if (timerRef.current) {
                        clearInterval(timerRef.current);
                    }
                    let count = 5;
                    if (msgListRef.current) {
                        timerRef.current = setInterval(() => {
                            if (reachedBottom || count <= 0) {
                                clearInterval(timerRef.current);
                            }
                            count--;
                            scrollTo(0, msgListRef.current?.scrollHeight);
                        }, 50);
                    }
                },
                [reachedBottom, scrollTo]
            );

            useEffect(
                () => {
                    scrollToBottom();
                },
                // eslint-disable-next-line react-hooks/exhaustive-deps
                [msgListRef.current?.scrollHeight, height]
            );
            useImperativeHandle(
                ref,
                () => ({
                    groupCaseInfoRefresh,
                    scrollToBottom,
                }),
                [groupCaseInfoRefresh, scrollToBottom]
            );

            return (
                <div
                    ref={msgListRef}
                    className={listContainer}
                    id="scrollableQueryDiv"
                    style={{height: height || 'calc(100% - 40px - 40px)'}}
                >
                    <ListComponent
                        groupCaseInfo={groupCaseInfo}
                        onGroupSendQuery={onGroupSendQuery}
                        sendingQueryItem={sendingQueryItem}
                    />
                </div>
            );
        }
    )
);
