import {CopyOutlined} from '@ant-design/icons';
import {Space, Tooltip, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {memo, useCallback, useMemo} from 'react';
import {Query} from '@/api/ievalue/case';
import {default as ChatRobotIcon} from '@/assets/ievalue/ChatRobot';
import {IconSend} from '@/icons/ievalue';
import UserIcon from '@/assets/ievalue/User';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {Markdown} from '@/design/Markdown';
import {ChatStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useCaseStageType, useGroupChatContext} from '@/hooks/ievalue/task';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import styles from './index.module.less';

interface IProps {
    text: string;
}
const CopyButton = memo(({text}: IProps) => {
    return (
        <Typography.Text
            copyable={{
                text: text ?? '',
                icon: <CopyOutlined style={{color: '#000', width: '12px', height: '12px'}} />,
            }}
        />
    );
});
interface ISendButtonProps {
    onClick: () => void;
    loading: boolean;
    disabled: boolean;
}
const SendButton = memo(({onClick, loading, disabled}: ISendButtonProps) => {
    return (
        <Tooltip title="一键发送该对话">
            <Button
                style={{width: 14, height: 22, padding: 0, margin: 0}}
                disabled={disabled}
                loading={loading}
                size="small"
                type="text"
                icon={<IconSend width={12} height={12} onClick={onClick} />}
            />
        </Tooltip>
    );
});

export const QueryBotItem = memo(({data}: {data: Query}) => {
    return (
        <div className={styles.botContainer}>
            <ChatRobotIcon
                className={styles.robot}
                style={{
                    margin: '3px 10px 0 0',
                    width: 24,
                    height: 24,
                }}
            />
            <div className={styles.botContent}>
                <FlexLayout justify="space-between">
                    <Space>
                        <Markdown content={data?.referenceOutput || ''} codeHighlight />
                    </Space>
                    <Space>
                        <CopyButton text={data?.referenceOutput || ''} />
                    </Space>
                </FlexLayout>
                <Space />
            </div>
        </div>
    );
});
export const QueryCustomerItem = memo(
    ({
        data,
        sendQuery,
        sendingQueryItem,
    }: {
        data: Query;
        loading?: boolean;
        sendQuery?: (item: Query) => void;
        sendingQueryItem?: Query;
    }) => {
        const onClick = useCallback(
            () => {
                sendQuery?.(data);
            },
            [data, sendQuery]
        );
        const {statusData} = useGroupChatContext();
        // 封存时不能发送
        const chatClosed = useMemo(
            () => !statusData?.find(item => item.status !== ChatStatusEnum.ChatClosed),
            [statusData]
        );
        // 只有评估中才能发送
        const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
        const stageType = useCaseStageType();

        const isEvaluating = useMemo(
            () => {
            // 已完成或者不是评估状态的，不可操作
                return (
                    groupCaseInfo.status !== 'FINISH' && [TaskStageEnum.EVALUATING].includes(stageType)
                );
            },
            [groupCaseInfo.status, stageType]
        );
        // console.log(!!sendingQueryItem, chatClosed, !isEvaluating);
        const content = useMemo(
            () => {
                let content = data?.input;
                if (data?.image) {
                    content += '\n' + data?.image;
                }
                return content || '';
            },
            [data]
        );
        return (
            <div className={styles.customerContainer}>
                <UserIcon
                    style={{
                        margin: '3px 10px 0 0',
                        width: 24,
                        height: 24,
                    }}
                />
                <div className={styles.customerContent}>
                    <FlexLayout justify="space-between">
                        <Space>
                            <Markdown content={content} codeHighlight />
                        </Space>
                        <Space>
                            <CopyButton text={data?.input || ''} />
                            <span>{' | '}</span>
                            <SendButton
                                disabled={!!sendingQueryItem || chatClosed || !isEvaluating}
                                loading={sendingQueryItem?.ID === data.ID}
                                onClick={onClick}
                            />
                        </Space>
                    </FlexLayout>
                    <Space />
                </div>
            </div>
        );
    }
);
