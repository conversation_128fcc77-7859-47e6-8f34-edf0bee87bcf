/* eslint-disable complexity */
/* eslint-disable max-lines */
import {Divider, Input} from 'antd';
import {debounce, head} from 'lodash';
import {
    CSSProperties,
    forwardRef,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useState,
} from 'react';
import {
    CaseEvaluateRecordItem,
    MetricItem,
    ScoreItem,
    apiCaseEvaluateUpsert,
    apiEvaluateCaseUpsert,
    apiEvaluateRecordUpdate,
} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {CaseHistoryTypeEnum, CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGroupStageInfo,
    useStrategyTaskList,
    useTaskInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import NotePanel from '@/components/TaskGroup/NotePanelComponent/NotePanel';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {ScoreHistoryButton} from '../../../TaskGroupDetail/components/ScoreHistoryButton';
import AddTagPanel from '../../../TaskGroupDetail/components/TagPanelComponent/AddTagPanel';
import AutoComputePanel from '../../../TaskGroupDetail/components/AutoComputePanelComponent/AutoComputePanel';
import FeedbackTagPanel from '../../../TaskGroupDetail/components/TagPanelComponent/FeedbackTagPanel';
import {DiffAlertTitle} from './DiffAlertTitle';
import styles from './index.module.less';
import {ScoreGroup} from './ScoreGroup';
import {SessionAutoScoreGroup} from './SessionAutoScoreGroup';
const {TextArea} = Input;
const debouncedEvaluateRecordUpdate = debounce(apiEvaluateRecordUpdate, 500);
const debouncedCaseEvaluateUpsert = debounce(apiCaseEvaluateUpsert, 500);

const getScoreNameByStrategy = (
    strategyMetricList: MetricItem[],
    metric: string,
    score: number | null
) => {
    return (
        strategyMetricList
            .find(metricItem => metricItem.metric === metric)
            ?.choices?.find(
                choicesItem => Number(choicesItem.score) === score
            )?.name ?? null
    );
};

export const ScoreForm = memo(
    forwardRef(
        (
            {
                modelID,
                predictRecordID,
                style,
                rows,
                getPopupContainer,
                showFeedbackTagPanel = true,
            }: {
                modelID?: number;
                style?: CSSProperties;
                rows?: number;
                predictRecordID?: number;
                getPopupContainer?: () => HTMLElement;
                showFeedbackTagPanel?: boolean;
            },
            ref: any
        ) => {
            const [taskInfo] = useTaskInfo();
            const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
            const {caseEvaluateRecords, caseEvaluateRefresh} =
                useCaseEvaluateList();
            const [, _, groupInfo] = useGroupStageInfo();
            const stageType = useCaseStageType();
            const taskID = useTaskTaskID();
            const recordItem = useMemo(
                () => {
                    if (predictRecordID) {
                        return (
                            caseEvaluateRecords.find(
                                e => e.origin.predictRecordID === predictRecordID
                            ) || ({} as CaseEvaluateRecordItem)
                        );
                    }
                    return (
                        caseEvaluateRecords.find(
                            e => e.origin.modelID === modelID
                        ) || ({} as CaseEvaluateRecordItem)
                    );
                },
                [caseEvaluateRecords, modelID, predictRecordID]
            );
            const [strategyList] = useStrategyTaskList();
            const strategyMetricList = useMemo(
                () => {
                    return head(strategyList)?.metric ?? [];
                },
                [strategyList]
            );

            const [memoryHistoryScoreList, setMemoryHistoryScoreList] =
                useState<ScoreItem[]>([]);

            useEffect(
                () => {
                    let currentScore: ScoreItem[] | undefined =
                    recordItem.origin.score;
                    if (
                        [
                            TaskStageEnum.AUDITING,
                            TaskStageEnum.AUDITING_FORWARD,
                        ].includes(stageType)
                    ) {
                        if (groupInfo.status !== CaseStatusEnum.RESOLVE_DISPUTE) {
                            currentScore =
                            recordItem?.diff?.score ?? recordItem.origin.score;
                        }
                    }
                    const result =
                    currentScore
                    ?? strategyMetricList.map(({desc, metric}) => ({
                        desc,
                        metric,
                        scoreName: '',
                        score: null,
                    }));
                    setMemoryHistoryScoreList(result);
                },
                [groupInfo.status, stageType, recordItem, strategyMetricList]
            );
            const blockHandle: any = useMemo(
                () => {
                    return (
                        groupInfo.status === CaseStatusEnum.FINISH
                    || (stageType === TaskStageEnum.EVALUATING
                        && groupInfo.status === CaseStatusEnum.REJECTED)
                    || ((groupCaseInfo?.status === CaseStatusEnum.FINAL_DISPUTE
                        || recordItem.origin.status
                            === CaseStatusEnum.FINAL_DISPUTE)
                        && stageType !== TaskStageEnum.ACCEPTING)
                    || ([
                        TaskStageEnum.AUDITING,
                        TaskStageEnum.AUDITING_FORWARD,
                    ].includes(stageType)
                        && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE
                        && recordItem.origin.status === CaseStatusEnum.SUBMIT)
                    );
                },
                [
                    groupCaseInfo?.status,
                    groupInfo.status,
                    stageType,
                    recordItem.origin.status,
                ]
            );

            const needUpdateRecord = useMemo(
                () => {
                    return (
                        ([TaskStageEnum.AUDITING].includes(stageType)
                        && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE)
                    || stageType === TaskStageEnum.ACCEPTING
                    );
                },
                [groupInfo.status, stageType]
            );
            const [note, setNote] = useState<string>();
            useEffect(
                () => {
                    setNote(recordItem.origin.note ?? undefined);
                },
                [recordItem?.origin?.note]
            );

            const handleChange = useCallback(
                ({target}: any, immediately: boolean) => {
                    if (blockHandle) {
                        return;
                    }
                    const updateScore = target?.value;
                    if (needUpdateRecord) {
                        debouncedEvaluateRecordUpdate({
                            ID: recordItem.origin.ID,
                            taskID: taskID,
                            predictRecordID: recordItem.origin.predictRecordID,
                            evaluateCaseID: recordItem.origin.evaluateCaseID,
                            note: updateScore,
                            diff: [
                                {
                                    type: CaseHistoryTypeEnum.RECORD,
                                    caseID: groupCaseInfo.caseID,
                                    groupID: groupCaseInfo.groupID,
                                    recordID: recordItem.origin.predictRecordID,
                                    field: 'note',
                                    origin: `${recordItem.origin.note}`,
                                    turnTo: `${updateScore}`,
                                },
                            ],
                        }).then(() => {
                            if (immediately) {
                                caseEvaluateRefresh?.();
                            }
                        });
                    } else {
                        debouncedCaseEvaluateUpsert({
                            taskID: taskID,
                            predictRecordID: recordItem.origin.predictRecordID,
                            stageID: groupCaseInfo.stageID,
                            groupID: groupCaseInfo.groupID,
                            caseID: groupCaseInfo.caseID,
                            note: updateScore,
                            diff: [
                                {
                                    type: CaseHistoryTypeEnum.RECORD,
                                    caseID: groupCaseInfo.caseID,
                                    groupID: groupCaseInfo.groupID,
                                    recordID: recordItem.origin.predictRecordID,
                                    field: 'note',
                                    origin: `${recordItem.origin.note}`,
                                    turnTo: `${updateScore}`,
                                },
                            ],
                        }).then(() => {
                            if (immediately) {
                                caseEvaluateRefresh?.();
                            }
                        });
                    }
                },
                [
                    blockHandle,
                    needUpdateRecord,
                    recordItem.origin.ID,
                    recordItem.origin.predictRecordID,
                    recordItem.origin.evaluateCaseID,
                    recordItem.origin.note,
                    taskID,
                    groupCaseInfo.caseID,
                    groupCaseInfo.groupID,
                    groupCaseInfo.stageID,
                    caseEvaluateRefresh,
                ]
            );
            // 打分变化后 设置好 memoryHistoryScoreList，以便后续变化时能取到当前打分
            const handleGroupScoreChange = useCallback(
                async ({target}: any) => {
                    if (blockHandle) {
                        return;
                    }
                    try {
                        const updateMetric: string = target?.name ?? '';
                        const updateScore = target?.value;
                        const originScoreName =
                            memoryHistoryScoreList.find(
                                item => item.metric === updateMetric
                            )?.scoreName ?? '';
                        const updateScoreName = getScoreNameByStrategy(
                            strategyMetricList,
                            updateMetric,
                            updateScore
                        );

                        // 非当前操作
                        const isNotOperationRecords: CaseEvaluateRecordItem[] =
                            [];
                        for (const i of caseEvaluateRecords) {
                            if (
                                i.origin.predictRecordID
                                !== recordItem.origin.predictRecordID
                            ) {
                                isNotOperationRecords.push(i);
                            }
                        }
                        if (
                            stageType === TaskStageEnum.EVALUATING
                            || needUpdateRecord
                        ) {
                            const {needSort, sortMetric} = head(strategyList);
                            if (
                                !!needSort
                                && updateMetric === sortMetric
                                && caseEvaluateRecords
                                && isNotOperationRecords.every(
                                    // todo 去除操作当条
                                    item =>
                                        item.origin.score
                                        && item?.origin?.score?.some(
                                            scoreItem =>
                                                scoreItem?.metric
                                                    === sortMetric
                                                && scoreItem?.score !== null
                                        )
                                )
                            ) {
                                const satisfactionSortList = caseEvaluateRecords
                                    .map(record => ({
                                        id: record.origin.predictRecordID,
                                        score:
                                            record.origin.predictRecordID
                                            === recordItem.origin.predictRecordID
                                                ? updateScore
                                                : record.origin.score.find(
                                                    (item: any) =>
                                                        item?.metric
                                                          === sortMetric
                                                ).score,
                                    }))
                                    .sort((a, b) => b.score - a.score);

                                const newRanking = satisfactionSortList.map(
                                    item => item.id
                                );
                                const current = satisfactionSortList.reduce(
                                    (result: string[], curr, index, arr) => {
                                        if (index > 0) {
                                            if (
                                                curr.score
                                                > arr[index - 1].score
                                            ) {
                                                result.push('<');
                                            } else if (
                                                curr.score
                                                < arr[index - 1].score
                                            ) {
                                                result.push('>');
                                            } else {
                                                result.push('=');
                                            }
                                        }
                                        return result;
                                    },
                                    []
                                );
                                apiEvaluateCaseUpsert({
                                    taskID,
                                    stageID: groupCaseInfo.stageID,
                                    caseID: groupCaseInfo.caseID,
                                    groupID: groupCaseInfo.groupID,
                                    ranking: newRanking,
                                    operators: current,
                                });
                            }
                        }

                        // console.log(updateMetric, 'updateScore', updateScore, memoryHistoryScoreList);
                        const score = memoryHistoryScoreList.map(item => {
                            if (item.metric === updateMetric) {
                                return {
                                    ...item,
                                    scoreName: updateScoreName,
                                    score: updateScore,
                                };
                            } else {
                                return item;
                            }
                        });
                        setMemoryHistoryScoreList(score);
                        const diff = [
                            {
                                type: CaseHistoryTypeEnum.RECORD,
                                caseID: groupCaseInfo.caseID,
                                groupID: groupCaseInfo.groupID,
                                recordID: recordItem.origin.predictRecordID,
                                field: updateMetric,
                                origin: originScoreName,
                                turnTo: updateScoreName,
                            },
                        ];
                        if (needUpdateRecord) {
                            await apiEvaluateRecordUpdate({
                                ID: recordItem.origin.ID,
                                taskID: taskID,
                                predictRecordID:
                                    recordItem.origin.predictRecordID,
                                evaluateCaseID:
                                    recordItem.origin.evaluateCaseID,
                                score,
                                diff,
                            });
                        } else {
                            await apiCaseEvaluateUpsert({
                                accepted: 0,
                                taskID: taskID,
                                predictRecordID:
                                    recordItem.origin.predictRecordID,
                                stageID: groupCaseInfo.stageID,
                                groupID: groupCaseInfo.groupID,
                                caseID: groupCaseInfo.caseID,
                                score,
                                diff,
                            });
                        }
                        const refDomid = `#id${recordItem.origin.predictRecordID}`;
                        const ele: HTMLElement =
                            document.querySelector(refDomid);
                        if (ele) {
                            ele.style.border = 'none';
                        }
                    } catch (error) {
                        // eslint-disable-next-line no-console
                        console.log(error, 'caseEvaluateUpsert error');
                    } finally {
                        caseEvaluateRefresh?.();
                    }
                },
                [
                    blockHandle,
                    memoryHistoryScoreList,
                    strategyMetricList,
                    stageType,
                    needUpdateRecord,
                    groupCaseInfo.caseID,
                    groupCaseInfo.groupID,
                    groupCaseInfo.stageID,
                    recordItem.origin.predictRecordID,
                    recordItem.origin.ID,
                    recordItem.origin.evaluateCaseID,
                    caseEvaluateRecords,
                    strategyList,
                    taskID,
                    caseEvaluateRefresh,
                ]
            );

            return (
                <div
                    className={styles.container}
                    id={`scorePannel${predictRecordID || modelID}`}
                    style={style}
                    ref={ref}
                >
                    {showFeedbackTagPanel && (
                        <FeedbackTagPanel
                            refresh={caseEvaluateRefresh}
                            recordItem={recordItem.origin}
                            blockHandle={blockHandle}
                            disabled={taskInfo.stage === 'TERMINATED'}
                            showModifyHistory={false}
                        />
                    )}
                    <FlexLayout
                        wrap="wrap"
                        gap={8}
                        align="center"
                        style={{padding: '4px 8px'}}
                    >
                        <DiffAlertTitle
                            recordItem={recordItem}
                            groupCaseInfo={groupCaseInfo}
                        />
                        {strategyMetricList?.map(metricItem => {
                            if (taskInfo.isSessionAutoScore) {
                                return (
                                    <SessionAutoScoreGroup
                                        key={metricItem.metric}
                                        predictRecordID={predictRecordID}
                                        modelID={modelID}
                                        metricItem={metricItem}
                                        groupInfo={groupInfo}
                                        recordItem={recordItem}
                                        blockHandle={blockHandle}
                                        handleChange={handleGroupScoreChange}
                                        getPopupContainer={getPopupContainer}
                                    />
                                );
                            }
                            return (
                                <ScoreGroup
                                    key={metricItem.metric}
                                    predictRecordID={predictRecordID}
                                    modelID={modelID}
                                    metricItem={metricItem}
                                    groupInfo={groupInfo}
                                    recordItem={recordItem}
                                    blockHandle={blockHandle}
                                    handleChange={handleGroupScoreChange}
                                    getPopupContainer={getPopupContainer}
                                />
                            );
                        })}
                        <ScoreHistoryButton
                            recordID={recordItem.origin.predictRecordID}
                        />
                    </FlexLayout>
                    <AutoComputePanel recordItem={recordItem} />
                    <AddTagPanel
                        caseID={groupCaseInfo.caseID}
                        stageID={groupCaseInfo.stageID}
                        refresh={caseEvaluateRefresh}
                        recordItem={recordItem.origin}
                    />
                    <Divider style={{margin: '7px 0'}} />
                    <NotePanel
                        blockHandle={blockHandle}
                        recordItem={recordItem}
                        refresh={caseEvaluateRefresh}
                    />
                    <TextArea
                        placeholder="请输入备注"
                        name="note"
                        value={note}
                        defaultValue={recordItem.origin.note ?? undefined}
                        rows={rows || 2}
                        bordered={false}
                        onChange={e => {
                            setNote(e.target.value);
                            handleChange(e, false);
                        }}
                        onBlur={e => {
                            handleChange(e, true);
                        }}
                        style={{resize: 'none'}}
                        disabled={taskInfo.stage === 'TERMINATED'}
                    />
                </div>
            );
        }
    )
);
