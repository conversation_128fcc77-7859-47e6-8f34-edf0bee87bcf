/* eslint-disable max-lines */
/* eslint-disable max-len */
import {Divider, Input} from 'antd';
import {debounce, head} from 'lodash';
import {CSSProperties, useCallback, useMemo} from 'react';
import {
    CaseEvaluateRecordItem,
    ChatOutputUpdateBody,
    ChatRecordItem,
    MetricItem,
    apiChatOutputUpdate,
} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGroupStageInfo,
    useStrategyTaskList,
    useTaskInfo,
    useTaskStageID,
} from '@/hooks/ievalue/task';
import TurnNotePanel from '@/components/TaskGroup/NotePanelComponent/TurnNotePanel';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import TurnFeedbackTag from '../../../../TaskGroupDetail/components/TagPanelComponent/FeedbackTagPanel/TurnFeedbackTag';
import TurnAddTagPanel from '../../../../TaskGroupDetail/components/TagPanelComponent/AddTagPanel/TurnAddTagPanel';
import TurnAutoComputePanel from '../../../../TaskGroupDetail/components/AutoComputePanelComponent/TurnAutoComputePanel';
import {ScoreGroup} from './ScoreGroup';
import styles from './index.module.less';
const {TextArea} = Input;

const getScoreNameByStrategy = (
    strategyMetricList: MetricItem[],
    metric: string,
    score: number | null
) => {
    return (
        strategyMetricList
            .find(metricItem => metricItem.metric === metric)
            ?.choices?.find(
                choicesItem => Number(choicesItem.score) === score
            )?.name ?? null
    );
};

const getScoreDescByStrategy = (
    strategyMetricList: MetricItem[],
    metric: string
) => {
    return (
        strategyMetricList.find(metricItem => metricItem.metric === metric)
            ?.desc ?? ''
    );
};

export const MsgItemScoreItem = ({
    modelID,
    chatID,
    predictRecordID,
    style,
    item,
    refresh,
    getPopupContainer,
}: {
    modelID?: number;
    chatID?: number;
    style?: CSSProperties;
    rows?: number;
    predictRecordID?: number;
    item?: ChatRecordItem;
    refresh?: () => void;
    getPopupContainer?: () => HTMLElement;
}) => {
    const [taskInfo] = useTaskInfo();
    const stageID = useTaskStageID();
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const {caseEvaluateRecords, caseEvaluateRefresh} = useCaseEvaluateList();
    const [, _, groupInfo] = useGroupStageInfo();
    const stageType = useCaseStageType();
    const recordItem = useMemo(
        () => {
            if (predictRecordID) {
                return (
                    caseEvaluateRecords.find(
                        e => e.origin.predictRecordID === predictRecordID
                    ) || ({} as CaseEvaluateRecordItem)
                );
            }
            return (
                caseEvaluateRecords.find(e => e.origin.modelID === modelID)
            || ({} as CaseEvaluateRecordItem)
            );
        },
        [caseEvaluateRecords, modelID, predictRecordID]
    );
    const [strategyList] = useStrategyTaskList();
    const strategyMetricList = useMemo(
        () => {
            return head(strategyList)?.metric ?? [];
        },
        [strategyList]
    );

    const blockHandle = useMemo(
        () => {
            return (
                groupInfo.status === CaseStatusEnum.FINISH
            || (stageType === TaskStageEnum.EVALUATING
                && groupInfo.status === CaseStatusEnum.REJECTED)
            || ((groupCaseInfo.status === CaseStatusEnum.FINAL_DISPUTE
                || recordItem.origin.status === CaseStatusEnum.FINAL_DISPUTE)
                && stageType !== TaskStageEnum.ACCEPTING)
            || ([TaskStageEnum.AUDITING, TaskStageEnum.AUDITING_FORWARD].includes(
                stageType
            )
                && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE
                && recordItem.origin.status === CaseStatusEnum.SUBMIT)
            );
        },
        [
            groupCaseInfo.status,
            groupInfo.status,
            stageType,
            recordItem.origin.status,
        ]
    );

    const handleChange = useCallback(
        async ({target}: any) => {
            if (blockHandle) {
                return;
            }
            try {
                const param: ChatOutputUpdateBody = {
                    taskID: taskInfo.taskID,
                    stageID,
                    predictRecordID: item.predictRecordID,
                    ID: Number(chatID),
                    note: target?.value,
                };
                await apiChatOutputUpdate(param);
                // eslint-disable-next-line no-console
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error, 'caseEvaluateUpsert error');
            } finally {
                refresh?.();
            }
        },
        [
            blockHandle,
            chatID,
            item,
            refresh,
            stageID,
            taskInfo.taskID,
        ]
    );

    // 打分变化后 设置好 memoryHistoryScoreList，以便后续变化时能取到当前打分
    const handleGroupScoreChange = useCallback(
        // eslint-disable-next-line complexity
        async ({target}: any, metricIndex: number) => {
            if (blockHandle) {
                return;
            }
            try {
                let scoreList = item.score;
                if (scoreList === null) {
                    scoreList = strategyMetricList?.map(_ => ({
                        desc: '',
                        score: undefined,
                        metric: null,
                        scoreName: null,
                    }));
                }
                const updateMetric: string = target?.name ?? '';
                const updateScore = target?.value;
                const updateScoreName = getScoreNameByStrategy(
                    strategyMetricList,
                    updateMetric,
                    updateScore
                );
                const updateScoreDesc = getScoreDescByStrategy(
                    strategyMetricList,
                    updateMetric
                );
                scoreList[metricIndex] = {
                    desc: updateScoreDesc,
                    score: updateScore,
                    metric: updateMetric,
                    scoreName: updateScoreName,
                };
                const param: ChatOutputUpdateBody = {
                    taskID: taskInfo.taskID,
                    stageID,
                    predictRecordID: item.predictRecordID,
                    ID: Number(chatID),
                    score: scoreList,
                };
                await apiChatOutputUpdate(param);
                refresh?.();
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error, 'caseEvaluateUpsert error');
            } finally {
                if (!taskInfo.isSessionAutoScore) {
                    caseEvaluateRefresh?.();
                }
            }
        },
        [
            blockHandle,
            caseEvaluateRefresh,
            chatID,
            item,
            refresh,
            stageID,
            strategyMetricList,
            taskInfo.isSessionAutoScore,
            taskInfo.taskID,
        ]
    );
    return (
        <FlexLayout
            className={styles.container}
            style={style}
            direction="column"
        >
            <TurnFeedbackTag recordItem={item} blockHandle={blockHandle} refresh={refresh} />
            <FlexLayout wrap="wrap" gap={8} style={{padding: '4px 8px'}}>
                {strategyMetricList?.map((metricItem, metricIndex) => {
                    return (
                        <ScoreGroup
                            key={metricItem.metric}
                            predictRecordID={predictRecordID}
                            modelID={modelID}
                            metricItem={metricItem}
                            metricIndex={metricIndex}
                            groupInfo={groupInfo}
                            recordItem={recordItem}
                            blockHandle={blockHandle}
                            handleChange={handleGroupScoreChange}
                            defaultValue={
                                item?.score?.[metricIndex]?.score ?? ''
                            }
                            getPopupContainer={getPopupContainer}
                        />
                    );
                })}
            </FlexLayout>
            <TurnAutoComputePanel recordItem={item} />
            <TurnAddTagPanel recordItem={item} refresh={refresh} />
            <Divider style={{margin: '7px 0'}} />
            <TurnNotePanel blockHandle={blockHandle} recordItem={item} />
            <div style={{flex: '2 0'}}>
                <TextArea
                    style={{height: '100%'}}
                    placeholder="请输入备注"
                    bordered={false}
                    rows={3}
                    name="note"
                    defaultValue={item?.note ?? undefined}
                    onChange={debounce(handleChange, 1000)}
                />
            </div>
        </FlexLayout>
    );
};
