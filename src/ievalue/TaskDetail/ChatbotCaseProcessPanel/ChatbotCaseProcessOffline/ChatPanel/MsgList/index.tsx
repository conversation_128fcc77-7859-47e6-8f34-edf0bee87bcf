/* eslint-disable max-lines */
/* eslint-disable max-len */
import styled from '@emotion/styled';
import {List} from 'antd';
import {mean, round} from 'lodash';
import {
    forwardRef,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
} from 'react';
import {Boundary} from 'react-suspense-boundary';
import {css} from '@emotion/css';
import {
    apiEvaluateCaseUpsert,
    CaseEvaluateRecordItem,
    ChatRecordItem,
    GroupCaseInfoItem,
    Query,
} from '@/api/ievalue/case';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useChatRecordMsgList,
    useGroupStageInfo,
    useTaskInfo,
    useTaskStrategyInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {useChatIsSingleScrol, useChatOneClickExpand} from '@/providers/TaskDetailProviders/ChatExtendInfoProvider';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import MoreInfo from '../../../Chat/MsgList/Item/MoreInfo';
import {ScoreForm} from '../../../Chat/Score';
import CreateChatRecordButton from '../../../../TaskGroupDetail/components/CreateChatRecordButton';
import {BotItem, CustomerItem} from './Item';
const listContainer = css`
    overflow: auto;
    box-sizing: border-box;
    flex: 1;
    ::-webkit-scrollbar {
        display: none;
    }
`;
const ListLayout = styled(List)`
    .ant-5-list-item {
        padding: 0;
        border-block-end: 0;
    }
`;
const gropMsg = css`
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0px;
`;
interface IMsgPops {
    data?: PromptExcuteResultListItem;
    groupCaseInfo?: GroupCaseInfoItem;
    onGroupSendQuery?: (item: Query) => void;
    sendingQueryItem?: Query;
    isEdit?: boolean;
    isFullScreen?: boolean;
}

const MsgItem = ({
    item,
    index,
    data,
    openScore,
    setOpenScore,
    refresh,
    getPopupContainer,
    isFullScreen,
}: {
    item: ChatRecordItem;
    index?: number;
    data?: PromptExcuteResultListItem;
    openScore: boolean;
    setOpenScore: (openScore: boolean) => void;
    refresh: () => void;
    getPopupContainer?: () => HTMLElement;
    isFullScreen?: boolean;
}) => {
    return (
        <List.Item key={item.ID}>
            <div className={gropMsg} style={{padding: 0}}>
                <MoreInfo item={item} />
                {item?.ID && (
                    <CustomerItem
                        item={item}
                        index={index}
                        refresh={refresh}
                    />
                )}
                {item?.ID && (
                    <BotItem
                        item={item}
                        index={index}
                        data={data}
                        openScore={openScore}
                        setOpenScore={setOpenScore}
                        refresh={refresh}
                        getPopupContainer={getPopupContainer}
                        isFullScreen={isFullScreen}
                    />
                )}
                <CreateChatRecordButton
                    preID={item.ID}
                    predictRecordID={item?.predictRecordID}
                    onFinish={refresh}
                    chatTaskID={item.chatTaskID}
                />
            </div>
        </List.Item>
    );
};

export const MsgList = forwardRef(
    (
        {
            data,
            openScore,
            setOpenScore,
            isFullScreen,
        }: IMsgPops & {
            openScore: boolean;
            setOpenScore: (openScore: boolean) => void;
        },
        ref?: any
    ) => {
        const oneClickExpand = useChatOneClickExpand();
        const {ID: predictRecordID, chatTaskID, modelID} = data || {};
        const strategyInfo = useTaskStrategyInfo();
        const [taskInfo] = useTaskInfo();
        const [listData, {refresh}] = useChatRecordMsgList({
            predictRecordID,
            chatTaskID,
        });
        const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
        const {caseEvaluateRecords, caseEvaluateRefresh} =
            useCaseEvaluateList();
        const [, _, groupInfo] = useGroupStageInfo();
        const stageType = useCaseStageType();

        const taskID = useTaskTaskID();

        const recordItem = useMemo(
            () => {
                if (predictRecordID) {
                    return (
                        caseEvaluateRecords.find(
                            e => e.origin.predictRecordID === predictRecordID
                        ) || ({} as CaseEvaluateRecordItem)
                    );
                }
                return (
                    caseEvaluateRecords.find(e => e.origin.modelID === modelID)
                || ({} as CaseEvaluateRecordItem)
                );
            },
            [caseEvaluateRecords, modelID, predictRecordID]
        );

        const blockHandle = useMemo(
            () => {
                return (
                    groupInfo.status === CaseStatusEnum.FINISH
                || stageType === TaskStageEnum.FEEDBACK
                || (stageType === TaskStageEnum.EVALUATING
                    && groupInfo.status === CaseStatusEnum.REJECTED)
                || ((groupCaseInfo.status === CaseStatusEnum.FINAL_DISPUTE
                    || recordItem.origin.status
                        === CaseStatusEnum.FINAL_DISPUTE)
                    && stageType !== TaskStageEnum.ACCEPTING)
                || ([
                    TaskStageEnum.AUDITING,
                    TaskStageEnum.AUDITING_FORWARD,
                ].includes(stageType)
                    && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE
                    && recordItem.origin.status === CaseStatusEnum.SUBMIT)
                );
            },
            [
                groupCaseInfo.status,
                groupInfo.status,
                stageType,
                recordItem.origin.status,
            ]
        );

        const needUpdateRecord = useMemo(
            () => {
                return (
                    ([TaskStageEnum.AUDITING].includes(stageType)
                    && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE)
                || stageType === TaskStageEnum.ACCEPTING
                );
            },
            [groupInfo.status, stageType]
        );

        const handleAutoUpdateSort = useCallback(
            async (updateMetric: string, updateScore: number) => {
                // 非当前操作
                const isNotOperationRecords = caseEvaluateRecords?.filter(
                    i =>
                        i.origin.predictRecordID
                        !== recordItem.origin.predictRecordID
                );
                if (
                    stageType === TaskStageEnum.EVALUATING
                    || needUpdateRecord
                ) {
                    const {needSort, sortMetric} = strategyInfo;
                    if (
                        !!needSort
                        && updateMetric === sortMetric
                        && caseEvaluateRecords
                        && isNotOperationRecords.every(
                            // todo 去除操作当条
                            item =>
                                item?.origin?.score
                                && item?.origin?.score?.some(
                                    scoreItem =>
                                        scoreItem?.metric === sortMetric
                                        && scoreItem?.score !== null
                                )
                        )
                    ) {
                        const sortList = caseEvaluateRecords
                            .map(record => ({
                                id: record.origin.predictRecordID,
                                score:
                                    record.origin.predictRecordID
                                    === recordItem.origin.predictRecordID
                                        ? updateScore
                                        : record.origin.score.find(
                                            (item: any) =>
                                                item?.metric === sortMetric
                                        ).score,
                            }))
                            .sort((a, b) => b.score - a.score);

                        const newRanking = sortList.map(item => item.id);
                        const current = sortList.reduce(
                            (result: string[], curr, index, arr) => {
                                if (index > 0) {
                                    if (curr.score > arr[index - 1].score) {
                                        result.push('<');
                                    } else if (
                                        curr.score < arr[index - 1].score
                                    ) {
                                        result.push('>');
                                    } else {
                                        result.push('=');
                                    }
                                }
                                return result;
                            },
                            []
                        );
                        await apiEvaluateCaseUpsert({
                            taskID,
                            stageID: groupCaseInfo.stageID,
                            caseID: groupCaseInfo.caseID,
                            groupID: groupCaseInfo.groupID,
                            ranking: newRanking,
                            operators: current,
                        });
                    }
                }
            },
            [
                caseEvaluateRecords,
                groupCaseInfo.caseID,
                groupCaseInfo.groupID,
                groupCaseInfo.stageID,
                needUpdateRecord,
                recordItem.origin.predictRecordID,
                stageType,
                strategyInfo,
                taskID,
            ]
        );

        const handleChange = useCallback(
            async (value: number, metric: string) => {
                if (blockHandle) {
                    return;
                }
                try {
                    await handleAutoUpdateSort?.(metric, value);
                } catch (error) {
                    /* empty */
                }
            },
            [blockHandle, handleAutoUpdateSort]
        );

        useEffect(
            () => {
                const handleScoreAge = async () => {
                    await Promise.all(
                        strategyInfo?.metric?.map(item => {
                            const metricScoreList = listData?.map(
                                chatItem =>
                                    chatItem?.score?.find(
                                        (i: any) => i.metric === item.metric
                                    )?.score || 0
                            );
                            const metricAgeScore = round(
                                mean(metricScoreList),
                                4
                            );
                            return handleChange(metricAgeScore, item.metric);
                        })
                    );
                    await caseEvaluateRefresh?.();
                };
                if (listData && !!taskInfo.isSessionAutoScore) {
                    handleScoreAge();
                }
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [listData, strategyInfo?.metric, taskInfo.isSessionAutoScore]
        );

        useImperativeHandle(
            ref,
            () => ({
                refresh,
                getList: () => {
                    return listData;
                },
            }),
            [listData, refresh]
        );

        const getPopupContainer = useCallback(
            () => {
                return document.getElementById(
                    `${isFullScreen}_scorePannel${data?.modelID}`
                );
            },
            [data?.modelID, isFullScreen]
        );

        const renderItem = useCallback(
            (item: any, index: number) => {
                return (
                    <MsgItem
                        item={item}
                        index={index}
                        data={data}
                        openScore={openScore}
                        setOpenScore={setOpenScore}
                        refresh={refresh}
                        getPopupContainer={getPopupContainer}
                        isFullScreen={isFullScreen}
                    />
                );
            },
            [data, getPopupContainer, isFullScreen, openScore, refresh, setOpenScore]
        );
        const isSingleScrol = useChatIsSingleScrol();
        const showScoreComp = useMemo(
            () => {
                return oneClickExpand && !isFullScreen && !isSingleScrol;
            },
            [isFullScreen, isSingleScrol, oneClickExpand]
        );
        return (
            <div className={isSingleScrol ? undefined : listContainer} id={`scrollableDiv${data?.modelID}`}>
                {showScoreComp && (
                    <Boundary>
                        <div id={`parent${data?.modelID}`}>
                            <ScoreForm modelID={data.modelID} style={{padding: 14}} getPopupContainer={() => document.getElementById(`parent${data?.modelID}`)} />
                        </div>
                    </Boundary>
                )}
                <CreateChatRecordButton
                    preID={0}
                    predictRecordID={predictRecordID}
                    onFinish={refresh}
                    chatTaskID={chatTaskID}
                />
                <ListLayout
                    id={`${isFullScreen}_scorePannel${data?.modelID}`}
                    dataSource={listData || []}
                    split={false}
                    renderItem={renderItem}
                    style={{padding: 0}}
                />
            </div>
        );
    }
);
