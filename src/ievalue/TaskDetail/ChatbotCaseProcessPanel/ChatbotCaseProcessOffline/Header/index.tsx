import {Affix, Checkbox, Col, Space, Tag, Typography} from 'antd';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {pick} from 'lodash';
import {ColumnSelect} from '@/components/ievalue/ColumnSelect';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {convertObjectToMarkdownTable} from '@/utils/ievalue/task';
import {
    useChatDisplayList,
    useChatOneClickExpand,
    useChatSetDisplayList,
    useChatSetOneClickExpand,
} from '@/providers/TaskDetailProviders/ChatExtendInfoProvider';
import {ChatDisplayOptionEnum} from '@/types/ievalue/task';
import {FullScreenComponent} from '@/components/TaskGroup/FullScreen';
import {useCaseEvaluateList} from '@/hooks/ievalue/task';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {EvaluateRuleButton} from '../../../CaseProcessPanel/EvaluateRuleButton';
import {FinishEvaluate} from '../../../CaseProcessPanel/FinishEvaluate';
import {ChatDisplayOptions} from '../../ChatbotCaseProcessOnline';
import EvaluateTerminatedTaskButton from '../../../CaseProcessPanel/EvaluateTerminatedTaskButton';
import {EvaluateReferenceButton} from '../../../CaseProcessPanel/EvaluateReferenceButton';

const Header = () => {
    const displayList = useChatDisplayList();
    const setDisplayList = useChatSetDisplayList();
    const oneClickExpand = useChatOneClickExpand();
    const setOneClickExpand = useChatSetOneClickExpand();
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const [customSelectList, setCustomSelectList] = useState<any[]>([]);
    const [allCustomSelectList, setAllCustomSelectList] = useState<string[]>([]);
    const {caseEvaluateRecords} = useCaseEvaluateList();

    const onChange = useCallback(
        (e: any) => {
            setOneClickExpand(e.target.checked);
        },
        [setOneClickExpand]
    );

    const customInfoStr = useMemo(
        () => {
            const customInfoObj = JSON.parse(groupCaseInfo?.optionalRaw || '{}');
            const headerList = groupCaseInfo?.header?.split(',') || [];
            return convertObjectToMarkdownTable(
                pick(customInfoObj, customSelectList), headerList
            );
        },
        [customSelectList, groupCaseInfo?.header, groupCaseInfo?.optionalRaw]
    );

    useEffect(
        () => {
            const customInfoObj = JSON.parse(groupCaseInfo?.optionalRaw || '{}');
            const allSelectList = Object.keys(customInfoObj);
            setAllCustomSelectList(allSelectList);
            setCustomSelectList(preList => {
                if (preList.length === 0) {
                    return allSelectList;
                }
                return preList;
            });
        },
        [groupCaseInfo?.optionalRaw]
    );

    return (
        <Affix offsetTop={50} style={{width: '100%'}}>
            <FlexLayout
                justify="space-between"
                style={{width: '100%', marginBottom: '5px', background: '#ffffff', height: '32px'}}
            >
                <Space style={{height: '100%'}}>
                    <Typography.Title
                        level={4}
                        style={{maxWidth: 220}}
                        ellipsis={{
                            tooltip: groupCaseInfo?.groupName || '-',
                        }}
                    >
                        {groupCaseInfo?.groupName || '-'}
                    </Typography.Title>
                    <Tag>{groupCaseInfo?.stageName || '-'}</Tag>
                </Space>
                <Space>
                    <Checkbox.Group
                        options={ChatDisplayOptions}
                        value={displayList}
                        onChange={setDisplayList}
                    />
                    <EvaluateReferenceButton
                        data={groupCaseInfo?.referenceIndicator}
                        predictRecordID={caseEvaluateRecords?.[0]?.origin?.predictRecordID}
                    />
                    <EvaluateRuleButton />
                    <FinishEvaluate />
                    <EvaluateTerminatedTaskButton />
                    <Checkbox checked={oneClickExpand} onChange={onChange}>
                        一键展开
                    </Checkbox>
                </Space>
            </FlexLayout>
            {displayList.includes(ChatDisplayOptionEnum.CustomVisible) && (
                <Col span={24}>
                    <FullScreenComponent
                        canCollapse
                        isCompact
                        title={
                            <Space align="baseline" id="customInfoPanel">
                                <Typography.Text strong>
                                    自定义内容
                                </Typography.Text>
                                <ColumnSelect
                                    options={allCustomSelectList}
                                    onChange={setCustomSelectList}
                                    selectList={customSelectList}
                                    getPopupContainer={() =>
                                        document.getElementById(
                                            'customInfoPanel'
                                        )
                                    }
                                />
                            </Space>
                        }
                        background="#F7F7F7"
                        content={customInfoStr}
                    />
                </Col>
            )}
        </Affix>
    );
};

export default Header;
