import {CustomBoundary} from '@/components/ievalue/Boundary';
import {TaskPredictTypeEnum} from '@/constants/ievalue/task';
import {useGroupCaseStageTimeInitialize, useTaskInfo} from '@/hooks/ievalue/task';
import {EvaluateDiffProvider} from '@/providers/TaskDetailProviders/EvaluateDiffProvider';
import {CaseProcessProvider} from '@/providers/TaskDetailProviders/CaseProcessProvider';
import {EvaluateOptionalRawProvider} from '@/providers/TaskDetailProviders/EvaluateOptionalRawProvider';
import {ChatExtendInfoProvider} from '@/providers/TaskDetailProviders/ChatExtendInfoProvider';
import {EvaluateCommonProvider} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import ChatbotCaseProcessOffline from './ChatbotCaseProcessOffline';
import ChatbotCaseProcessOnline from './ChatbotCaseProcessOnline';

const ChatbotCaseProcess = () => {
    const [taskInfo] = useTaskInfo();
    useGroupCaseStageTimeInitialize();

    if (taskInfo.predictType === TaskPredictTypeEnum.OFFLINE) {
        return (
            <CaseProcessProvider>
                <ChatbotCaseProcessOffline />
            </CaseProcessProvider>
        );
    }

    return <ChatbotCaseProcessOnline />;
};

export const ChatbotCaseProcessPanel = () => {
    return (
        <CustomBoundary.FullSizeLoading>
            <EvaluateDiffProvider>
                <ChatExtendInfoProvider>
                    <EvaluateCommonProvider>
                        <EvaluateOptionalRawProvider>
                            <ChatbotCaseProcess />
                        </EvaluateOptionalRawProvider>
                    </EvaluateCommonProvider>
                </ChatExtendInfoProvider>
            </EvaluateDiffProvider>
        </CustomBoundary.FullSizeLoading>
    );
};

export default ChatbotCaseProcessPanel;
