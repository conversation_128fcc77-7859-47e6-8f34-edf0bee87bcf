/* eslint-disable max-lines */
import {
    Affix,
    Button,
    Checkbox,
    Col,
    Popconfirm,
    Space,
    Tag,
    Typography,
} from 'antd';
import {useActionPending} from 'huse';
import {memo, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {pick} from 'lodash';
import LockIcon from '@/assets/ievalue/Lock';
import {
    ChatStatusItem,
    apiChatClose,
    apiChatCreate,
} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {ChatStatusEnum} from '@/constants/ievalue/case';
// import {SatisfactionResult} from './SatisfactionSort';
import {
    GroupChatContext,
    GroupPanelContext,
    useCaseEvaluateList,
    useGetChats,
    useGetPromptExcute,
    useGroupCaseID,
    useGroupChatContext,
    useProviderGetStatus,
    useTaskInfo,
    useWatchStatusList,
} from '@/hooks/ievalue/task';
import {ColumnSelect} from '@/components/ievalue/ColumnSelect';
import {convertObjectToMarkdownTable} from '@/utils/ievalue/task';
// eslint-disable-next-line max-len
import {useChatDisplayList, useChatIsSingleScrol, useChatSetDisplayList} from '@/providers/TaskDetailProviders/ChatExtendInfoProvider';
import {ChatDisplayOptionEnum} from '@/types/ievalue/task';
import {FullScreenComponent} from '@/components/TaskGroup/FullScreen';
import {useEvaluateCommonGroupCaseInfo} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {EvaluateRuleButton} from '../../CaseProcessPanel/EvaluateRuleButton';
import {FinishEvaluate} from '../../CaseProcessPanel/FinishEvaluate';
import {Chat} from '../Chat';
import {CaseProcessFooter} from '../Footer';
import {OptionalRawGroupButton} from '../../TaskGroupDetail/components/OptionalRawGroupButton';
import EvaluateTerminatedTaskButton from '../../CaseProcessPanel/EvaluateTerminatedTaskButton';
import {EvaluateReferenceButton} from '../../CaseProcessPanel/EvaluateReferenceButton';

export const ChatDisplayOptions = [
    {label: 'Query&参考答案', value: ChatDisplayOptionEnum.QueryVisible},
    {label: '系统人设', value: ChatDisplayOptionEnum.SystemVisible},
    {label: '整体滚动', value: ChatDisplayOptionEnum.ScrolType},
    {
        label: (
            <Space align="center">
                自定义内容
                <OptionalRawGroupButton />
            </Space>
        ),
        value: ChatDisplayOptionEnum.CustomVisible,
    },
];

interface BasicInfoProps {
    open: boolean;
    onChange?: (e: any) => void;
    stage: string;
    displayList: string[];
    setDisplayList: (value: string[]) => void;
}

const ChatPanel = memo(() => {
    const caseID = useGroupCaseID();
    const [actionChatCreate, pendingCreateCount] = useActionPending(apiChatCreate);
    const {startPolling} = useWatchStatusList();
    const [actionChatClose, pendingCloseCount] = useActionPending(apiChatClose);
    const {statusData} = useGroupChatContext();
    const [_, {refresh: predictRecordListRefresh}] = useGetPromptExcute();

    /**
     * 批量封存
     */
    const closeAllChat = useCallback(
        async () => {
            await actionChatClose({caseID});
            predictRecordListRefresh();
        },
        [actionChatClose, caseID, predictRecordListRefresh]
    );

    /**
     * 批量创建会话
     */
    const createAllChat = useCallback(
        async () => {
            try {
                await actionChatClose({caseID});
                await actionChatCreate({caseID});
                startPolling({
                    status: ChatStatusEnum.ChatCreating,
                    callback: () => {
                        predictRecordListRefresh();
                    },
                });
            } catch (error) {
                /** ** */
            }
        },
        [
            actionChatClose,
            actionChatCreate,
            caseID,
            predictRecordListRefresh,
            startPolling,
        ]
    );
    const createDisable = useMemo(
        () => {
            if (!statusData) {
                return true;
            }
            const idx = statusData.findIndex(
                (e: ChatStatusItem) => e.status !== ChatStatusEnum.ChatCreating
            );
            return idx > -1;
        },
        [statusData]
    );

    /**
     * 只要有运行中的对话，则封存按钮可用
     */
    const runningModels = useMemo(
        () => {
            return (
                statusData?.filter(
                    (e: ChatStatusItem) => e.status === ChatStatusEnum.ChatReady
                ) || []
            );
        },
        [statusData]
    );
    return (
        <Space>
            <Popconfirm
                title="提示"
                description="确认重置对话吗?"
                okText="确定"
                cancelText="取消"
                onConfirm={createAllChat}
            >
                <Button
                    loading={!!pendingCloseCount && !!pendingCreateCount}
                    disabled={createDisable && !!pendingCloseCount}
                    type="primary"
                >
                    一键重置对话
                </Button>
            </Popconfirm>
            <Popconfirm
                title="提示"
                description="确认封存对话吗?"
                okText="确定"
                cancelText="取消"
                onConfirm={closeAllChat}
            >
                <Button
                    loading={!!pendingCloseCount}
                    disabled={!!pendingCreateCount || runningModels.length <= 0}
                    style={{display: 'flex', alignItems: 'center'}}
                    icon={<LockIcon />}
                >
                    一键封存对话
                </Button>
            </Popconfirm>
        </Space>
    );
});

const BasicInfo = memo(
    ({
        open,
        onChange,
        stage,
        displayList,
        setDisplayList,
    }: BasicInfoProps) => {
        const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
        const [customSelectList, setCustomSelectList] = useState<any[]>([]);
        const [allCustomSelectList, setAllCustomSelectList] = useState<string[]>([]);
        const {caseEvaluateRecords} = useCaseEvaluateList();
        const customInfoStr = useMemo(
            () => {
                const customInfoObj = JSON.parse(groupCaseInfo?.optionalRaw || '{}');
                const headerList = groupCaseInfo?.header?.split(',') || [];
                return convertObjectToMarkdownTable(
                    pick(customInfoObj, customSelectList), headerList
                );
            },
            [customSelectList, groupCaseInfo?.header, groupCaseInfo?.optionalRaw]
        );

        useEffect(
            () => {
                const customInfoObj = JSON.parse(
                    groupCaseInfo?.optionalRaw || '{}'
                );
                const allSelectList = Object.keys(customInfoObj);
                setAllCustomSelectList(allSelectList);
                setCustomSelectList(preList => {
                    if (preList.length === 0) {
                        return allSelectList;
                    }
                    return preList;
                });
            },
            [groupCaseInfo?.optionalRaw]
        );

        const isEvaluatIng = useMemo(
            () => {
                // 已完成或者不是评估状态的，不可操作
                return groupCaseInfo.status !== 'FINISH';
            },
            [groupCaseInfo.status]
        );

        return (
            <>
                <FlexLayout
                    justify="space-between"
                    style={{width: '100%', marginBottom: '5px'}}
                >
                    <Space>
                        <Typography.Title level={4}>
                            {groupCaseInfo?.groupName || '-'}
                        </Typography.Title>
                        <Tag>{groupCaseInfo?.stageName || '-'}</Tag>
                        {stage !== 'TERMINATED' && isEvaluatIng && (
                            <ChatPanel />
                        )}
                    </Space>
                    <Space>
                        <Checkbox.Group
                            options={ChatDisplayOptions}
                            value={displayList}
                            onChange={setDisplayList}
                        />
                        <EvaluateReferenceButton
                            data={groupCaseInfo?.referenceIndicator}
                            predictRecordID={caseEvaluateRecords?.[0]?.origin?.predictRecordID}
                        />
                        <EvaluateRuleButton />
                        <FinishEvaluate />
                        <EvaluateTerminatedTaskButton />
                        <Checkbox checked={open} onChange={onChange}>
                            一键展开
                        </Checkbox>
                    </Space>
                </FlexLayout>
                {displayList.includes(ChatDisplayOptionEnum.CustomVisible) && (
                    <Col span={24}>
                        <FullScreenComponent
                            canCollapse
                            isCompact
                            title={
                                <Space align="baseline" id="customInfoPanel">
                                    <Typography.Text strong>
                                        自定义内容
                                    </Typography.Text>
                                    <ColumnSelect
                                        options={allCustomSelectList}
                                        onChange={setCustomSelectList}
                                        selectList={customSelectList}
                                        // eslint-disable-next-line react/jsx-no-bind
                                        getPopupContainer={() =>
                                            document.getElementById(
                                                'customInfoPanel'
                                            )
                                        }
                                    />
                                </Space>
                            }
                            background="#F7F7F7"
                            content={customInfoStr}
                        />
                    </Col>
                )}
            </>
        );
    }
);
const ChatbotPanel = ({
    open,
    onChange,
    stage,
}: {
    open: boolean;
    onChange?: (e: any) => void;
    stage: string;
}) => {
    const {statusData, startPolling, clearPolling} = useProviderGetStatus();
    const refs = useRef<any>({});
    const caseID = useGroupCaseID();
    const displayList = useChatDisplayList();
    const setDisplayList = useChatSetDisplayList();
    /**
     * 全局状态轮巡
     */
    useEffect(
        () => {
            startPolling(caseID);
            return clearPolling;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [caseID]
    );
    return (
        <GroupChatContext.Provider value={{statusData, refs}}>
            <Affix offsetTop={50} style={{width: '100%'}}>
                <div style={{background: '#ffffff', width: '100%'}}>
                    <BasicInfo
                        displayList={displayList}
                        setDisplayList={setDisplayList}
                        open={open}
                        onChange={onChange}
                        stage={stage}
                    />
                </div>
            </Affix>
            <Chat />
        </GroupChatContext.Provider>
    );
};
const ChatbotCaseProcessOnline = () => {
    const [taskInfo] = useTaskInfo();
    const checksRef = useRef<any>({});
    const {getChats} = useGetChats();
    const isSingleScrol = useChatIsSingleScrol();
    const [open, setOpen] = useState(true);
    const onChange = useCallback(
        (e: any) => {
            setOpen(e.target.checked);
        },
        []
    );
    return (
        <FlexLayout
            direction="column"
            justify="space-between"
            style={{width: '100%', height: isSingleScrol ? '100%' : 'calc(100vh - 90px)'}}
            gap={10}
        >
            <GroupPanelContext.Provider value={{checksRef, getChats, open}}>
                <ChatbotPanel
                    open={open}
                    onChange={onChange}
                    stage={taskInfo.stage}
                />
                {/*  <SatisfactionResult /> */}
                {taskInfo.stage !== 'TERMINATED' && <CaseProcessFooter />}
            </GroupPanelContext.Provider>
        </FlexLayout>
    );
};

export default ChatbotCaseProcessOnline;
