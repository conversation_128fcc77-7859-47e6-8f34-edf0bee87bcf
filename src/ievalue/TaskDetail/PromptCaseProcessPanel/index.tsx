import styled from '@emotion/styled';
import {Checkbox, Col, Row, Space, Tag, Typography} from 'antd';
import {Resizable} from 're-resizable';
import {useCallback, useMemo, useState} from 'react';
import {Boundary} from 'react-suspense-boundary';
import {CaseEvaluateRecordItem} from '@/api/ievalue/case';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    useCaseEvaluateList,
    useGroupCaseStageTimeInitialize,
    useSpaceMoveView,
    useTarget,
} from '@/hooks/ievalue/task';
import {EvaluateDiffProvider} from '@/providers/TaskDetailProviders/EvaluateDiffProvider';
import {
    EvaluateCommonProvider,
    useEvaluateCommonGroupCaseInfo,
} from '@/providers/TaskDetailProviders/EvaluateCommonProvider';
import {CaseProcessFooter} from '../CaseProcessPanel/CaseProcessFooter';
import {EvaluateRuleButton} from '../CaseProcessPanel/EvaluateRuleButton';
import {FinishEvaluate} from '../CaseProcessPanel/FinishEvaluate';
import {SatisfactionSort} from '../CaseProcessPanel/SatisfactionSort';
import EvaluateTerminatedTaskButton from '../CaseProcessPanel/EvaluateTerminatedTaskButton';
import {EvaluateReferenceButton} from '../CaseProcessPanel/EvaluateReferenceButton';
import {PromptPanel} from './PromptPanel';
import {Provider} from './Provider';
import styles from './index.module.less';
export enum DisplayOptionEnum {
    ContentVisible = 'content_visible',
    VariablesVisible = 'variables_visible',
    AnswerVisible = 'answer_visible',
}
const defaultDisplay = [
    DisplayOptionEnum.ContentVisible,
    DisplayOptionEnum.VariablesVisible,
];
const DisplayPromptOptions = [
    {label: 'Prompt内容', value: DisplayOptionEnum.ContentVisible},
    {label: '变量', value: DisplayOptionEnum.VariablesVisible},
    {label: '参考答案', value: DisplayOptionEnum.AnswerVisible},
];
const DisplayPromptFlowOptions = [
    {label: 'PromptFlow内容', value: DisplayOptionEnum.ContentVisible},
    {label: '参考答案', value: DisplayOptionEnum.AnswerVisible},
];
const Header = styled.div`
    flex: 0 1 auto;
`;
const PromptCaseProcess = () => {
    const target = useTarget();
    const [displayList, setDisplayList] = useState<string[]>(
        target === 'PromptFlow'
            ? [DisplayOptionEnum.ContentVisible]
            : defaultDisplay
    );
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const {caseEvaluateRecords, caseEvaluateRefresh} = useCaseEvaluateList();

    const DisplayOptions = useMemo(
        () => {
            return target === 'PromptFlow'
                ? DisplayPromptFlowOptions
                : DisplayPromptOptions;
        },
        [target]
    );
    const handleDisplayChange = useCallback(
        (checkedValues: any[]) => {
            setDisplayList(checkedValues);
        },
        [setDisplayList]
    );
    const width = useMemo(
        () => {
            const num =
                caseEvaluateRecords.length > 3
                    ? 3
                    : caseEvaluateRecords.length || 0;
            return 100 / num;
        },
        [caseEvaluateRecords.length]
    );
    useSpaceMoveView('#canvas');
    return (
        <>
            {groupCaseInfo && (
                <Provider
                    value={{
                        groupCaseInfo,
                        onRefresh: caseEvaluateRefresh,
                        displayList,
                    }}
                >
                    <FlexLayout
                        direction="column"
                        align="stretch"
                        style={{width: '100%', background: '#fff'}}
                    >
                        <Header>
                            <FlexLayout justify="space-between">
                                <Space>
                                    <Typography.Title level={4}>
                                        {groupCaseInfo.groupName}
                                    </Typography.Title>
                                    <Tag>{groupCaseInfo.stageName}</Tag>
                                </Space>
                                <Space split="|">
                                    <Checkbox.Group
                                        options={DisplayOptions}
                                        value={displayList}
                                        onChange={handleDisplayChange}
                                    />
                                    <EvaluateReferenceButton
                                        data={groupCaseInfo?.referenceIndicator}
                                        predictRecordID={
                                            caseEvaluateRecords?.[0]?.origin
                                                ?.predictRecordID
                                        }
                                    />
                                    <EvaluateRuleButton />
                                    <FinishEvaluate />
                                    <EvaluateTerminatedTaskButton />
                                </Space>
                            </FlexLayout>
                            {/* <Divider style={{margin: '8px 0'}} />
                        <Row gutter={[16, 12]} style={{padding: '12px 0'}}>
                            <Col
                                span={24}
                            >
                                <FullScreenComponent
                                    canCollapse
                                    title={<Typography.Text strong>Query</Typography.Text>}
                                    background="rgba(245,231,222,0.5)"
                                    content={groupCaseInfo.query}
                                />
                            </Col>
                        </Row> */}
                        </Header>
                        <div className={styles.goupScrol}>
                            <div className={styles.groupContainer}>
                                <div className={styles.content} id="canvas">
                                    {caseEvaluateRecords?.map(
                                        (
                                            item: CaseEvaluateRecordItem,
                                            index: number
                                        ) => {
                                            return (
                                                <Resizable
                                                    key={
                                                        item.origin
                                                            .predictRecordID
                                                    }
                                                    style={{height: 'auto'}}
                                                    defaultSize={{
                                                        width: `${width}%`,
                                                        height: 'auto',
                                                    }}
                                                    minWidth="400px"
                                                    minHeight={'100%'}
                                                    enable={{right: true}}
                                                >
                                                    <div
                                                        className={
                                                            styles.promptPanel
                                                        }
                                                    >
                                                        <PromptPanel
                                                            recordItem={item}
                                                            index={index}
                                                        />
                                                    </div>
                                                </Resizable>
                                            );
                                        }
                                    )}
                                </div>
                            </div>
                        </div>
                        <Row>
                            <Col span={24}>
                                <Boundary>
                                    <SatisfactionSort />
                                </Boundary>
                            </Col>
                        </Row>
                        <CaseProcessFooter
                            caseEvaluateRecords={caseEvaluateRecords}
                        />
                        {/* <SelectAction /> */}
                    </FlexLayout>
                </Provider>
            )}
        </>
    );
};

export const PromptCaseProcessPanel = () => {
    useGroupCaseStageTimeInitialize();

    return (
        <CustomBoundary.FullSizeLoading>
            <EvaluateDiffProvider>
                <EvaluateCommonProvider>
                    <PromptCaseProcess />
                </EvaluateCommonProvider>
            </EvaluateDiffProvider>
        </CustomBoundary.FullSizeLoading>
    );
};
export default PromptCaseProcessPanel;
