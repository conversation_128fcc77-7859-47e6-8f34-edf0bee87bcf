import constate from 'constate';
import {useMemo} from 'react';
import {useLocalStorageCustomList} from '@/hooks/ievalue/task';
import {useEvaluateCommonGroupCaseInfo} from '../EvaluateCommonProvider';

const useEvaluateOptionalRawContext = () => {
    const {customSelectList, customGroupList, groupName, setCustomData} =
        useLocalStorageCustomList();
    const groupCaseInfo = useEvaluateCommonGroupCaseInfo();
    const allColumnList = useMemo(
        () => {
            return Object.keys(JSON.parse(groupCaseInfo?.optionalRaw || '{}'));
        },
        [groupCaseInfo?.optionalRaw]
    );

    const headerList = useMemo(
        () => {
            return groupCaseInfo?.header?.split(',') || [];
        },
        [groupCaseInfo?.header]
    );

    const groupList = useMemo(
        () => {
            return customGroupList?.length
                ? customGroupList
                : [{name: '展示全部', columns: allColumnList}];
        },
        [allColumnList, customGroupList]
    );

    return {
        customSelectList,
        groupList,
        groupName,
        setCustomData,
        allColumnList,
        headerList,
    };
};

export const [
    EvaluateOptionalRawProvider,
    useEvaluateOptionalRawSelectList,
    useEvaluateOptionalRawGroupList,
    useEvaluateOptionalRawGroupName,
    useEvaluateOptionalRawSetCustomData,
    useEvaluateOptionalRawAllColumnList,
    useEvaluateOptionalRawHeaderList,
] = constate(
    useEvaluateOptionalRawContext,
    value => value.customSelectList,
    value => value.groupList,
    value => value.groupName,
    value => value.setCustomData,
    value => value.allColumnList,
    value => value.headerList
);
