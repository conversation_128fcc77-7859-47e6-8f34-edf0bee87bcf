import constate from 'constate';
import {head} from 'lodash';
import {useMemo} from 'react';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGetPredictRecordList,
    useGroupStageInfo,
    useTaskInfo,
    useTaskStrategyInfo,
} from '@/hooks/ievalue/task';

const useCaseProcessContext = () => {
    const [taskInfo] = useTaskInfo();
    const isCaseDispatch = !!taskInfo.dispatchType;
    const [, , groupInfo] = useGroupStageInfo();
    const {caseEvaluateRecords, caseEvaluateRankMap, caseEvaluateRefresh} =
        useCaseEvaluateList();
    const [predictList, {refresh: predictRecordListRefresh}] =
        useGetPredictRecordList();
    const caseStageType = useCaseStageType();
    const strategyInfo = useTaskStrategyInfo();

    const predictRecordList = useMemo(
        () => {
            const keys: any[] = Object.keys(predictList);
            if (keys?.length) {
                const headList = predictList[head(keys)];
                // 根据records 对chatslist进行排序
                return caseEvaluateRecords?.reduce((resultList: PromptExcuteResultListItem[], evaluateItem) => {
                    const predictItem = headList?.find(
                        item => item.ID === evaluateItem.origin.predictRecordID
                    );
                    if (predictItem) {
                        resultList.push(predictItem);
                    }
                    return resultList;
                }, []);
            }
            return [];
        },
        [caseEvaluateRecords, predictList]
    );

    return {
        taskInfo,
        groupInfo,
        caseEvaluateRecords,
        caseEvaluateRankMap,
        caseEvaluateRefresh,
        predictRecordList,
        predictRecordListRefresh,
        caseStageType,
        strategyInfo,
        isCaseDispatch,
    };
};

export const [
    CaseProcessProvider,
    useCaseProcessTaskInfo,
    useCaseProcessGroupInfo,
    useCaseProcessCaseEvaluateRecords,
    useCaseProcessCaseEvaluateRankMap,
    useCaseProcessCaseEvaluateRefresh,
    useCaseProcessPredictRecordList,
    useCaseProcessPredictRecordListRefresh,
    useCaseProcessCaseStageType,
    useCaseProcessStrategyInfo,
    useCaseProcessIsCaseDispatch,
] = constate(
    useCaseProcessContext,
    value => value.taskInfo,
    value => value.groupInfo,
    value => value.caseEvaluateRecords,
    value => value.caseEvaluateRankMap,
    value => value.caseEvaluateRefresh,
    value => value.predictRecordList,
    value => value.predictRecordListRefresh,
    value => value.caseStageType,
    value => value.strategyInfo,
    value => value.isCaseDispatch
);
