import constate from 'constate';
import {useRequest} from 'huse';
import {useMemo} from 'react';
import {useLocalStorageOtherNoteRegionData} from '@/regions/ievalue/task/otherNotePanelRegion';
import {apiGroupCaseInfo, GroupCaseInfoItem} from '@/api/ievalue/case';
import {useGroupCaseID, useTaskTaskID} from '@/hooks/ievalue/task';

const useEvaluateCommonContext = () => {
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    const {otherNoteRegionHeightList, setOtherNoteRegionData} =
        useLocalStorageOtherNoteRegionData();
    const {
        data: groupCaseInfo = {} as GroupCaseInfoItem,
        refresh: groupCaseInfoRefresh,
    } = useRequest(apiGroupCaseInfo, {
        caseID,
        taskID,
    });

    const categoryInfo = useMemo(
        () => {
            if (!groupCaseInfo) {
                return null;
            }
            const {l0 = '', l1 = ''} = groupCaseInfo;
            const strList = [l0, l1].filter(item => item !== '');
            return strList.length === 0 ? null : strList.join('-');
        },
        [groupCaseInfo]
    );

    return {
        otherNoteRegionHeightList,
        setOtherNoteRegionData,
        groupCaseInfo,
        groupCaseInfoRefresh,
        categoryInfo,
    };
};

export const [
    EvaluateCommonProvider,
    useEvaluateCommonOtherNoteRegionHeightList,
    useEvaluateCommonSetOtherNoteRegionData,
    useEvaluateCommonGroupCaseInfo,
    useEvaluateCommonGroupCaseInfoRefresh,
    useEvaluateCommonCategoryInfo,
] = constate(
    useEvaluateCommonContext,
    value => value.otherNoteRegionHeightList,
    value => value.setOtherNoteRegionData,
    value => value.groupCaseInfo,
    value => value.groupCaseInfoRefresh,
    value => value.categoryInfo
);
