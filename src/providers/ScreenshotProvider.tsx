import {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useRef,
    MutableRefObject,
} from 'react';
import html2canvas from 'html2canvas';
interface ContextValue {
    handleCapture: () => Promise<any>;
    canvasRef: MutableRefObject<HTMLDivElement>;
}
const Context = createContext<ContextValue>({} as ContextValue);
export const ScreenshotProvider = ({children, padding}: { children: ReactNode, padding?: string}) => {
    const canvasRef = useRef<HTMLDivElement>(null);
    const handleCapture = useCallback(
        async () => {
            const element = canvasRef.current;
            if (!element) {
                return {};
            }

            const cloneNode = element.cloneNode(true) as HTMLElement;
            if (element.parentElement) {
                element.parentElement.style.position = 'relative';
            }
            cloneNode.style.position = 'absolute';
            cloneNode.style.left = '-9999px';
            cloneNode.style.bottom = '-9999px';
            element.parentElement?.appendChild(cloneNode);
            const canvas = await html2canvas(element, {
                useCORS: true,
                logging: false,
                onclone: (documentClone, element) => {
                // 调整修改样式
                    element.style.padding = padding;
                    // 处理textarea渲染不换行问题
                    const textareas = documentClone.querySelectorAll('textarea');
                    textareas.forEach((e: HTMLTextAreaElement) => {
                        const ele = document.createElement('div');
                        // bca-disable-next-line
                        ele.innerHTML = e.value || '';
                        ele.style.width = '100%';
                        ele.style.border = '1px solid var(--ant-5-color-border)';
                        ele.style.borderRadius = '6px';
                        ele.style.boxSizing = 'border-box';
                        ele.style.padding = '4px 11px';
                        ele.style.minHeight = '40px';
                        ele.style.margin = '6px 10px 0px 10px';
                        ele.style.background = '#fff';
                        ele.style.whiteSpace = 'pre-wrap';
                        ele.style.wordWrap = 'break-word';
                        ele.style.overflowWrap = 'break-word';
                        e.parentElement?.replaceChild(ele, e);
                    });
                },
            });
            const blob: BlobPart = await new Promise(resolve => {
                canvas.toBlob(b => {
                    resolve(b);
                }, 'image/png');
            });
            const file = new File([blob], 'screenshot.png', {type: 'image/png'});
            const base64img = canvas.toDataURL('image/png');
            setTimeout(() => {
                element.parentElement?.removeChild(cloneNode);
            }, 100);
            return {canvas, base64img, blob, file};
        },
        [padding]
    );
    return (
        <Context.Provider
            value={{
                handleCapture,
                canvasRef,
            }}
        >
            {children}
        </Context.Provider>
    );
};

export const useScreenshotContext = () => {
    return useContext(Context);
};
