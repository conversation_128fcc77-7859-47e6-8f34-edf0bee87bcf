/* eslint-disable max-lines */

import {APP_WEBSOCKET_PREFIX} from '@/constants/app';
import {
    AssertionTypeEnum,
    DeployTypeEnum,
    EstimateTaskStatusEnum,
    InputTypeEnum,
    OnlineStatusEnum,
    ProjectSourceEnum,
    PromptAssertionStatusEnum,
    PromptEvolveStatusEnum,
    PromptPredictStatusEnum,
    PromptQualityResultTypeEnum,
    PromptSourceEnum,
    ReleaseStatusEnum,
    ReleaseTypesEnum,
    RunTestStatusEnum,
    WorkFlowStatusEnum,
} from '@/constants/ievalue/prompt';
import {getApiToken} from '@/regions/ievalue/apiToken';
import {createOnceWebSocket} from '@/utils/createInterface/createOnceWebSocket';
import {createInterface, createNetInterface, createSSEInterface} from './axios';
import {DisplayDebugItem, ModelItem} from './model';
import {
    EvaluateReportVersionDiffItem,
    Paginated,
    PromptVariable,
    PromptVersionVersionItem,
} from './prompt-version';
import {Type} from './task';
import {Tag} from './home';

export interface MessageItem {
    id: string;
    role: string;
    content: string;
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
}

interface PromptMetricItem {
    [key: string]: { metric: string, name: string };
}
// 开发中-DEVELOPING / 评估中-EVALUATING / 已发布-RELEASED / 已上线-DEPLOYED / 监控中-MONITORING
export type PromptStatusType =
    | 'DEVELOPING'
    | 'EVALUATING'
    | 'RELEASED'
    | 'DEPLOYED'
    | 'MONITORING';

export type PromptStoryType = 'researchStory' | 'productStory';
export interface PromptModel {
    promptID: number;
    promptName: string;
    spaceCode: string;
    suiteID: number;
    creator: string;
    updateUser: string;
    createTime: string;
    updateTime: string;
    text: string;
    variables: PromptVariable[];
    modelID: number;
    modelName: string;
    maxTokens: number;
    metric?: PromptMetricItem;
    temperature: number;
    topP: number;
    frequencyPenalty: number;
    stop: string;
    tags: PromptTag[];
    releaseStatus: number;
    releaseTypes: ReleaseTypesEnum[];
    desc: string;
    latestPromptVersion: PromptVersionVersionItem;
    promptType?: string;
    flowID?: number;
    inputType: InputTypeEnum;
    messages: MessageItem[];
    functions: any;
    debugParams: DisplayDebugItem[];
    promptCode: string;
    promptVersionID: number;
    versionID: number;
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
    userAuth?: 'READER' | 'EDITOR' | 'NONE';
    dirID?: number;
    dirName?: string;
    status?: PromptStatusType;
    storyType?: PromptStoryType;
}

export interface PromptOptimizeParams {
    promptID: number;
    spaceCode?: string;
    text: string;
    modelID?: number;
    model?: string;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    betterQuality?: number;
    conciseContent?: number;
    iterRounds?: number;
    optimizeText?: string;
    variables?: PromptVariable[];
    debugParams?: DisplayDebugItem[];
    versionID?: number;
    optID?: number;
    resultKey?: string;
}
export interface PromptApdotParams {
    promptID: number;
    spaceCode: string;
    text: string;
    optID: number;
}
export interface PromptOptimizeModel {
    completionTokens: number;
    output: string;
    reasoningContent: string;
    searchResults: SearchResultsItem[];
    promptTokens: number;
    startTime: string;
    totalTokens: number;
    optID: number;
}

export interface PromptTag {
    tagID: number;
    tagName: string;
    color: string;
}

export interface PaginationParams {
    pn?: number;
    size?: number;
}

export interface PromptListParams extends PaginationParams {
    suiteID?: number;
    spaceCode?: string;
    promptName?: string;
    tagIds?: string;
    promptType?: any;
    creator?: string;
    hasCard?: number;
}

export interface PromptDetailOrDeleteParams {
    spaceCode?: string;
    promptID: number;
    isDelTemplate?: number;
}

export interface PromptCreateParams {
    promptName: string;
    spaceCode?: string;
    desc: string;
    suiteID: number;
    text?: string;
    system?: string;
    variables?: PromptVariable[];
    promptType?: string;
    inputType?: InputTypeEnum;
    messages?: MessageItem[];
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
    dirID?: number;
    storyType?: PromptStoryType;
}

/**
 * 查看Prompt列表
 */
export const apiPromptList = createInterface<
    PromptListParams,
    Paginated<PromptModel>
>('GET', '/prompt/list');
export interface PromptWithVersionModel extends PromptModel {
    promptVersions: PromptVersionVersionItem[];
}
/**
 * 查看Prompt列表,包含历史版本
 */
export const apiPromptWithVersionList = createInterface<
    PromptListParams,
    PromptWithVersionModel[]
>('GET', '/prompt/list/withVersion');

/**
 * 创建Prompt
 */
export const apiPromptCreate = createInterface<PromptCreateParams, PromptModel>(
    'POST',
    '/prompt/create'
);

export const apiTemplateSquarePromptCreate = createInterface<
    PromptCreateParams,
    PromptModel
>('POST', '/templateSquare/prompt/create');

/**
 * 查看Prompt详情
 */
export const apiPromptDetail = createInterface<
    PromptDetailOrDeleteParams,
    PromptModel
>('GET', '/prompt/info');

/**
 * 优化Prompt
 */
export const apiPromptOptimize = createOnceWebSocket<
    PromptOptimizeParams,
    PromptOptimizeModel
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/prompt/optimize`);
/**
 * 优化PromptRun
 */
export const apiPromptOptimizeRun = createInterface<
    PromptOptimizeParams,
    PromptOptimizeModel
>('POST', 'prompt/optimize/run');

export const apiPromptOptimizeRunStream = createSSEInterface<
    PromptOptimizeParams,
    PromptOptimizeModel
>('POST', 'prompt/optimize/run/stream');

/**
 * Prompt采纳
 */
export const apiPromptAdopt = createInterface<PromptApdotParams, void>(
    'POST',
    '/prompt/optimize/adopt'
);

/**
 * 删除Prompt
 */
export const apiPromptDelete = createInterface<
    PromptDetailOrDeleteParams,
    void
>('POST', '/prompt/delete');

export interface PromptUpdateParams {
    promptID?: number;
    promptName?: string;
    desc?: string;
    text?: string;
    system?: string;
    variables?: PromptVariable[];
    modelID?: number;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    stop?: string;
    style?: string;
    resolution?: string;
    inputType?: string;
    debugParams?: DisplayDebugItem[];
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
    tags?: PromptTag[];
    storyType?: PromptStoryType;
}

/**
 * 更新Prompt
 */
export const apiPromptUpdate = createInterface<PromptUpdateParams, void>(
    'POST',
    '/prompt/update'
);
/**
 * 更新Prompt，编辑弹框
 */
export const apiPromptMetaUpdate = createInterface<PromptUpdateParams, void>(
    'POST',
    '/prompt/meta/update'
);

// #region Prompt tag

export interface PromptTagsCreateParams {
    tags: PromptTag[];
    spaceCode?: string;
    promptID: number;
    suiteID: number;
}

export interface PromptTagDeleteParams {
    promptID: number;
    tagID: number;
}

/**
 * 增加 Prompt 上的tags
 */
export const apiPromptTagsCreate = createInterface<
    PromptTagsCreateParams,
    void
>('POST', '/tag/prompt/create');

/**
 * 增加 Prompt 上的tags
 */
export const apiTemplateSquarePromptTagsCreate = createInterface<
    PromptTagsCreateParams,
    void
>('POST', '/templateSquare/tag/prompt/create');

/**
 * 删除 Prompt 上的单个tag
 */
export const apiPromptTagDelete = createInterface<PromptTagDeleteParams, void>(
    'POST',
    '/tag/prompt/delete'
);

// #endregion

// #region 运行 prompt

export interface PromptRunParams {
    /**
     * @deprecated
     */
    promptName?: string;
    promptID: number;
    /**
     * @deprecated
     */
    suiteID?: number;
    /**
     * @deprecated
     */
    desc?: string;
    /**
     * @deprecated
     */
    creator?: string;
    spaceCode: string;
    text: string;
    variables: PromptVariable[];
    modelID: number;
    maxTokens: number;
    temperature: number;
    topP: number;
    frequencyPenalty: number;
    stop: string;
    system: string;
    disableSearch: boolean;
    enableCitation: boolean;
    functions: any;
    inputType: InputTypeEnum;
    messages?: MessageItem[];
    debugParams?: DisplayDebugItem[];
    versionID: number;
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
}
export interface UploadFileItem {
    url: string;
    uid: string;
    name: string;
    size: number;
    type: string;
    mediaType?: string;
}
export interface ImageListItem {
    url: string;
    uid: string;
    name: string;
    size: number;
    type: string;
}
export interface SearchResultsItem {
    index: number;
    title: string;
    url: string;
}
export interface PromptSingleRunResult {
    /**
     * 输出的结果文本
     * @description 字符数可以用这个计算
     */
    output: string;
    reasoningContent: string;
    startTime: string;
    endTime: string;
    promptTokens: number;
    completionTokens: number;
    /**
     * 耗费的Token
     */
    totalTokens: number;
    searchResults: SearchResultsItem[];
}

/**
 * 按照配置运行 prompt
 */
export const apiPromptRun = createOnceWebSocket<
    PromptRunParams,
    PromptSingleRunResult
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/prompt/run`);

export interface PromptModelOption extends ModelItem {
    value?: number | string;
    label?: string;
}

export interface IReleaseSubjectsModel {
    id: number;
    name: string;
}
/**
 * 发布页面获取主题列表
 */
export const apiPromptReleaseSubjects = createInterface<
    string,
    IReleaseSubjectsModel[]
>('GET', '/prompt/release/subjects');

export interface PromptVariableValuesParams {
    promptID: number;
    variableKey: string;
}
export interface PromptVariableValuesResponse {
    key: string;
    values: string[];
}
export const apiPromptVariableValues = createInterface<
    PromptVariableValuesParams,
    PromptVariableValuesResponse
>('GET', '/prompt/variable/values');


export interface IReleaseParam {
    releaseType: number;
    promptID: number;
    versionID: number;
    subjectID: number;
    promptName?: string;
    promptDesc?: string;
    versionCommitID?: string;
}
export interface IReleaseResult {
    ID: number;
    name: string;
    releaseType: number;
}
/**
 * 发布页面获取主题列表
 */
export const apiPromptReleaseRaw = createInterface<
    IReleaseParams,
    IReleaseResult
>('POST', '/prompt/release');

export const apiPromptRelease = (params: IReleaseParams) => {
    return apiPromptReleaseRaw(params, {
        headers: {
            'Api-Token': getApiToken() ?? 'null',
        },
    });
};

export const apiPromptReleaseChannelHasDuoyi = createInterface<
    { spaceCode: string },
    boolean
>('GET', '/prompt/release/channel/hasDuoyi');

export interface UniconfInfo {
    releaseID?: number;
    schemaKey: string;
    schemaContentID: number;
    moduleName: string;
    moduleEname: string;
    moduleID: number;
    configName: string;
    configEname: string;
    configID: number;
    schemaContent: string;
    schemaContentOld: string;
    deployType: number; // 0:全量发布、1：紧急全量发布
    moduleLink: string;
    configLink: string;
}
export interface IReleaseParams {
    promptID: number;
    environmentID?: number;
    versionID: number;
    releaseType?: number;
    lastVersionID?: number;
    subjectID?: number;
    promptName?: string;
    promptDesc?: string;
    isPublicText?: number;
    versionCommitID?: string;
    isStreamOutput?: number;
    reviewers?: string;
    taskReportInfo?: EvaluateReportVersionDiffItem[];
    e2eTaskReportInfo?: EvaluateReportVersionDiffItem[];
    uniconfInfo?: UniconfInfo;
}

/**
 * 批量更新prompt标签
 */
export const apiTagPromptUpdate = createInterface<PromptTagsCreateParams, void>(
    'POST',
    '/tag/prompt/update'
);
export interface PromptReleaseDateRange {
    startTime: string;
    endTime: string;
}
/**
 * 获取发布截止日期
 */
export const apiTagPromptReleaseDateRange = createInterface<
    '',
    PromptReleaseDateRange
>('GET', '/templateSquare/dateRange');

export interface IPublicTemplate {
    ID: number;
    desc: string;
    name: string;
    subjectID: number;
    releaseType: number;
    isPublicText: number;
    subject: string;
    isRelease: boolean;
    releaseVersionID: number;
    createTime: string;
    creator: string;
    idcNames: string[];
}

/**
 * 根据prompt ID 查询发布的模板
 */
export const apiPromptPublicTemplate = createInterface<
    {
        promptID: number;
        releaseType: number;
        versionID: number;
        environmentID: number;
    },
    IPublicTemplate
>('GET', '/prompt/publicTemplate');
export interface PromptReleaseRecordListResponse {
    list: PromptReleaseRecordListItem[];
    total: number;
}
export interface PromptReleaseRecordListItem {
    ID: number;
    releaseType: ReleaseTypesEnum;
    versionID: number;
    versionCommitID: string;
    releaseName: string;
    releaseDesc: string;
    versionName: string;
    creator: string;
    createTime: string;
    promptID: number;
    releaseStatus: ReleaseStatusEnum;
    reviewers: string;
    confirmComment: string;
    lastVersionInfo?: PromptVersionVersionItem;
    taskReportInfo?: EvaluateReportVersionDiffItem[];
    e2eTaskReportInfo?: EvaluateReportVersionDiffItem[];
    environmentID: number;
    environmentName: string;
    uniconfInfo: UniconfInfo;
    deployType: DeployTypeEnum;
    type: string;
    isDeployed: number;
    releasePipelineInfoID: number;
    idcName: string;
}
export interface PromptReleaseRecordListParams {
    promptID: number;
    pn: number;
    size: number;
    versionID?: number;
    versionNameKeyword?: string;
    creator?: string;
}
export const apiPromptReleaseRecordList = createInterface<
    PromptReleaseRecordListParams,
    PromptReleaseRecordListResponse
>('GET', '/prompt/release/record/list');

export interface PromptReleaseApiDemoItem {
    body: string;
    parameters: string;
    promptCode: string;
    token: string;
    idcName: string;
}

/**
 * 获取API调用示例
 */
export const apiPromptReleaseApiInfo = createInterface<
    { spaceCode: string, promptID: number },
    PromptReleaseApiDemoItem
>('GET', '/prompt/release/api/info');

export interface ReleaseApiRecordItem {
    ID: number;
    versionCommitId: string;
    status: RunTestStatusEnum;
    creator: string;
    createTime: string;
    runtime: number;
    source: string;
    output: string;
    variables: PromptVariable[];
}

export interface ReleaseApiRecordList {
    total: number;
    list: ReleaseApiRecordItem[];
}

export const apiPromptReleaseApiRecordList = createInterface<
    { promptID: number, pn: number, size: number },
    ReleaseApiRecordList
>('GET', '/prompt/release/api/record/list');

interface ReleaseApiRunParams {
    token: string;
    promptCode: string;
    variables: PromptVariable[];
    idcName?: string;
    environmentName?: string;
}
export const apiPromptReleaseApiRun = createInterface<
    ReleaseApiRunParams,
    PromptSingleRunResult
>('POST', '/prompt/release/api/run');

export interface PromptResultParam {
    /**
     * 默认为0
     */
    pn?: number;
    /**
     * 1
     */
    promptID: number;
    /**
     * 支持通过promptName模糊查询
     */
    promptName?: string;
    promptVersionID?: number;
    /**
     * 默认为20
     */
    size?: number;
    spaceCode: string;
}

export interface PromptResultData {
    list: PromptResultList[];
    total: number;
}

export interface PromptResultList {
    promptResultID: number;
    promptID: number;
    promptVersionID: number;
    promptName: string;
    desc: string;
    spaceCode: string;
    suiteID: number;
    creator: string;
    createTime: string;
    text: string;
    variables?: any;
    modelID: number;
    modelName: string;
    maxTokens: number;
    temperature: number;
    topP: number;
    frequencyPenalty: number;
    stop: string;
    output: string;
    startTime: string;
    endTime: string;
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    commitID: string;
    releaseStatus: number;
}
export const apiPromptResultList = createInterface<
    PromptResultParam,
    PromptResultData
>('GET', '/prompt/result/list');

export interface PromptFastItem {
    ID: number;
    planID: number;
    spaceCode: string;
    name: string;
    target: string;
    stageID: number;
    priority: string;
    exePltID: string;
    templateID: number;
    stage: string;
    stageStatus: string;
    predictType: string;
    predictRound: string;
    evaluateMode: string;
    showMethod: string;
    cooperateType: string;
    taskType: string;
    models: string;
    datasets: string;
    spacePolicyID: number;
    policies: string;
    blind: number;
    isRoundScoring: number;
    isSampling: number;
    samplingRatio: number;
    promptHistoryID: string;
    secretLevel: string;
    status: number;
    deleted: number;
    creator: string;
    operator: string;
    conclusion: string;
    evaluateParam?: any;
    pass: number;
    note: string;
    sugarAddress: string;
    failMsg: string;
    gsb?: any;
    score?: any;
    startTime: string;
    endTime: string;
    createTime: string;
    updateTime: string;
    isFast: number;
    onlyPredict: number;
    datasetColumnMapID: number;
    TaskTemplateId: number;
}

export const apiTaskListPromptFast = createInterface<
    { promptID: number },
    PromptFastItem[]
>('GET', '/task/list/prompt/fast');

export interface FrameworkItem {
    /**
     * 内容，"情况,行动,目标,期望"
     */
    content: string;
    /**
     * 创建时间
     */
    createTime: string;
    /**
     * 示例
     */
    demo: string;
    /**
     * id
     */
    ID: number;
    /**
     * 名称
     */
    name: string;
    /**
     * 描述
     */
    note: string;
}
export const apiPromptFrameworkList = createInterface<'', FrameworkItem[]>(
    'GET',
    '/prompt/framework/list'
);

export interface RenerateParams {
    sceneDesc?: string;
    taskDesc?: string;
    examples?: Example[];
    otherInfo?: string;
    formatDesc?: string;
    promptID?: number;
    templateCode?: number;
    versionID?: number;
    source?: ProjectSourceEnum;
}

export interface Example {
    input: string;
    output: string;
}

export interface GenerateResponse {
    ID: number;
    result: string;
    isAccepted: number;
    inputParams: string;
    statusCode: number; // 1 生成中 2、成改、 3、失败
    templateCode: number;
    running: string;
    systemResult: string;
}

export const apiPromptGenerateRun = createInterface<
    RenerateParams,
    GenerateResponse
>('POST', '/prompt/generate/run');

export const apiPromptGenerateRecordLatestList = createInterface<
    { promptID: number, versionID: number },
    GenerateResponse[]
>('GET', '/prompt/generate/record/latest/list');

export interface RenerateInfo {
    ID: number;
    promptId: number;
    sceneDesc: string;
    taskDesc: string;
    formatDesc: string;
    examples: Example[];
    otherInfo: string;
    statusCode: number;
    result: string;
    creator: string;
    createTime: string;
    isDeleted: number;
    templateCode: number;
    isLastUpdate: number;
    running?: boolean;
    isAccepted: number;
    inputParams: string;
}

// prompt生成信息查询
export const apiPromptGenerateInfoList = createInterface<
    { promptID: number, versionID: number },
    RenerateInfo[]
>('GET', '/prompt/generate/info/list');
export interface GenerateRecordListParams {
    promptID?: number;
    templateCode?: number;
    pn: number;
    size: number;
    versionID?: number;
    source?: string;
    spaceCode?: string;
}
export interface RenerateRecordItem {
    ID: number;
    promptID: number;
    inputParams: string;
    statusCode: number;
    result: string;
    creator: string;
    createTime: string;
    isAccepted: number;
}
export interface RenerateRecordResponse {
    list: RenerateRecordItem[];
}

export const apiPromptGenerateRecordList = createInterface<
    GenerateRecordListParams,
    RenerateRecordResponse
>('GET', '/prompt/generate/record/list');

export const apiPromptGenerateInfo = createInterface<
    { promptID: number },
    RenerateInfo
>('GET', '/prompt/generate/info');

export const apiPromptGenerateRecordInfo = createInterface<
    { recordID: number },
    GenerateResponse
>('GET', '/prompt/generate/record/info');

// prompt生成参数更新
export const apiPromptGenerateUpdate = createInterface<
    RenerateParams,
    GenerateResponse
>('POST', '/prompt/generate/update');

// prompt生成参数更新
export const apiPromptGenerateAccepted = createInterface<
    { recordID: number },
    GenerateResponse
>('GET', '/prompt/generate/accepted');
export interface IssueItem {
    issueID: string | number;
    spaceCode?: string;
    title?: string;
    status?: string;
    type?: any;
    responsiblePerson?: any;
    cardID?: number;
    sequence?: number;
    cardCreateTime?: string;
}
export interface PromptVersionSubmitParams {
    versionID: number;
    versionName: string;
    debugParams: DisplayDebugItem[];
    inputType: InputTypeEnum;
    modelID: number;
    promptID: number;
    promptName: string;
    spaceCode: string;
    text: string;
    variables: PromptVariable[];
    issueList: IssueItem[];
    isFast: number;
}
export interface PromptVersionSubmitResponse {
    unsubmitVersionID: number;
    successRelateCardCount: number;
    versionID: string;
    versionInfo: PromptVersionVersionItem;
}

export const apiPromptVersionSubmit = createInterface<
    PromptVersionSubmitParams,
    PromptVersionSubmitResponse
>('POST', '/prompt/version/submit');
export interface PromptOptmizeHistoryItem {
    id: number;
    promptVersionID: number;
    modelID: number;
    debugParams: DisplayDebugItem;
    beforeOptPrompt: string;
    afterOptPrompt: string;
    beforeOptResult: string;
    afterOptResult: string;
    creator: string;
    createTime: string;
}
export const apiPromptOptmizeHistory = createInterface<
    { versionID: number },
    PromptOptmizeHistoryItem[]
>('GET', '/prompt/optimize/history');

export interface WorkflowListGroupByCardResponse {
    list: WorkflowListGroupByCardListItem[];
    total: number;
}

export interface WorkflowListGroupByCardListItem {
    /**
     * 卡片标识
     */
    issueID: string;
    /**
     * 卡片信息
     */
    story: Story;
    /**
     * 工作流列表
     */
    workflows: VersionWorkflow[];
}

/**
 * 卡片信息
 *
 * icafe卡片信息
 */
export interface Story {
    cardCreateTime: number;
    cardId: number;
    createTime: number;
    fe: string;
    id: number;
    isDel: boolean;
    isExempted: string;
    isFreeTest: string;
    isSelfTest: boolean;
    issueId: string;
    plan: string;
    pm: string;
    priory: string;
    qa: string;
    rd: string;
    responsiblePerson: string;
    sequence: number;
    spaceCode: string;
    spaceName: string;
    status: string;
    title: string;
    type: string;
    typeID: number;
    updateTime: number;
    url: string;
}

export interface CardItem {
    [key: string]: any;
    id?: number | string;
    issueId: string;
    spaceCode: string;
    spaceName?: string;
    title: string;
    type?: Type;
    typeID?: number;
    url?: string;
}

/**
 * 工作流
 */
export interface VersionWorkflow {
    /**
     * 创建时间，2024-02-28T18:13:21+08:00
     */
    createTime: string;
    /**
     * 创建人，即版本提交人
     */
    createUser: string;
    /**
     * 结束时间，2024-02-28T18:13:21+08:00
     */
    endTime?: string;
    /**
     * 工作流id
     */
    id: number;
    /**
     * promptID
     */
    promptID: number;
    /**
     * promptVersionID
     */
    promptVersionID: number;
    /**
     * 空间标识
     */
    spaceCode: string;
    /**
     * 工作流阶段
     */
    stages: VersionWorkflowStage[];
    /**
     * 开始时间，2024-02-28T18:13:21+08:00
     */
    startTime: string;
    /**
     * 工作流状态，WAITING / RUNNING / FINISHED / CANCEL
     */
    status: WorkFlowStatusEnum;
    /**
     * 更新时间，2024-02-28T18:13:21+08:00
     */
    updateTime: string;
    /**
     * 更新人
     */
    updateUser: string;
    /**
     * 版本信息
     */
    versionInfo: VersionInfo;
    promptInfo: PromptModel;
}

/**
 * 工作流阶段
 */
export interface VersionWorkflowStage {
    /**
     * 创建时间
     */
    createTime: string;
    /**
     * 创建人
     */
    createUser: string;
    /**
     * 结束时间，2024-02-28T18:13:21+08:00
     */
    endTime?: string;
    /**
     * 工作流id
     */
    flowID: number;
    /**
     * 阶段id
     */
    id: number;
    /**
     * 操作人
     */
    operator?: string;
    /**
     * 序号
     */
    sequence: number;
    /**
     * 阶段名称
     */
    stageName: string;
    /**
     * 开始时间
     */
    startTime?: string;
    /**
     * 阶段状态，WAITING / RUNNING / FINISHED / CANCEL
     */
    status: WorkFlowStatusEnum;
    /**
     * 更新时间
     */
    updateTime: string;
    /**
     * 更新人
     */
    updateUser: string;
}

/**
 * 版本信息
 */
export interface VersionInfo extends PromptVersionVersionItem {
    /**
     * 关联卡片
     */
    stories: Story[];
}

export interface Variable {
    key: string;
    type: string;
    value: string;
}

export interface WorkflowListGroupByCardParams {
    pn: number;
    size: number;
    issueID: string;
    startTimeStart: string;
    startTimeEnd: string;
    endTimeStart: string;
    endTimeEnd: string;
    spaceCode: string;
}

export const apiWorkflowListGroupByCard = createInterface<
    WorkflowListGroupByCardParams,
    WorkflowListGroupByCardResponse
>('GET', '/workflow/list/groupByCard');

export interface WorkflowQueryParams {
    /**
     * 结束时间结束
     */
    endTimeEnd?: string;
    /**
     * 结束时间起始
     */
    endTimeStart?: string;
    /**
     * 是否关联卡片, 是：1，否：2，全部：0
     */
    hasCard?: number;
    /**
     * 是否筛选有标签的版本，是：1，否：2，全部：0
     */
    hasTag?: number;
    /**
     * 页数
     */
    pn?: number;
    promptID?: number;
    /**
     * prompt名称关键词
     */
    promptNameKeyword?: string;
    /**
     * 每页数量
     */
    size?: number;
    /**
     * 空间标识
     */
    spaceCode: string;
    /**
     * 开始时间结束
     */
    startTimeEnd?: string;
    /**
     * 开始时间起始
     */
    startTimeStart?: string;
    versionID?: number;
    /**
     * 版本名称关键词
     */
    versionNameKeyword?: string;
}

export interface WorkflowQueryResponse {
    list: VersionWorkflow[];
    total: number;
}
// export interface WorkflowListItem {
//     id: number;
//     spaceCode: string;
//     promptID: number;
//     promptVersionID: number;
//     status: WorkFlowStatusEnum;
//     createUser: string;
//     createTime: string;
//     updateTime: string;
//     updateUser: string;
//     stages: VersionWorkflowStage[];
//     versionInfo: VersionInfo;
//     startTime: string;
//     endTime: string;
// }
export const apiWorkflowQuery = createInterface<
    WorkflowQueryParams,
    WorkflowQueryResponse
>('GET', '/workflow/query');

export interface ReleaseRecordListParams {
    /**
     * 发布人
     */
    creator?: string;
    pn: number;
    /**
     * prompt名称关键字
     */
    promptNameKeyword?: string;
    /**
     * 发布渠道，1：仅发布；2：一言；3：广场，4：预置Prompt模版
     */
    releaseType?: number;
    size: number;
    /**
     * 空间标识
     */
    spaceCode: string;
    /**
     * 版本名称关键字
     */
    versionNameKeyword?: string;
}

export interface ReleaseRecordListResponse {
    list: ReleaseRecordListItem[];
    total: number;
}

export interface ReleaseRecordListItem {
    ID: number;
    promptID: number;
    PromptName: string;
    releaseType: ReleaseTypesEnum;
    versionID: number;
    versionCommitID: string;
    commitID: string;
    releaseName: string;
    releaseDesc: string;
    versionName: string;
    creator: string;
    createUser: string;
    confirmComment?: string;
    createTime: string;
    releaseStatus: ReleaseStatusEnum;
    reviewers: any;
    diffInfo: PromptBatchReleaseDiffInfo;
    diffReportInfo?: any;
    ffTaskResult?: FFTaskResult;
    type: string;
    name: string;
    desc: string;
    versionCode: string;
    environmentID: number;
    environmentName: string;
    isDeployed: number; // isDeployed=1的话,已经上线，就不允许用户撤销了
    releasePipelineInfoID?: number;
    idcName: string;
    env: string;
}

export const apiPromptReleaseRecordListBySpace = createInterface<
    ReleaseRecordListParams,
    ReleaseRecordListResponse
>('GET', '/prompt/release/record/list/bySpace');

interface PromptVersionStatsInfo {
    runRecordCount: number;
    generateRecordCount: number;
    diagnosisRecordCount: number;
    optimizeRecordCount: number;
    promptTaskCount: number;
    e2eTaskCount: number;
}
export const apiPromptVersionStats = createInterface<
    { versionID: number },
    PromptVersionStatsInfo
>('GET', '/prompt/version/stats');

export interface SettingPromptInfo {
    id: number;
    spaceCode: string;
    releaseReviewRequired: boolean;
    releaseTaskReportRequired: boolean;
    e2eTaskRequired: boolean;
    defaultReleaseReviewer: string;
    createChildCard: boolean;
    versionStoryRequired: boolean;
    icafeSpaceCode: string;
    icafeSpaceName: string;
    icafeSpaceID: number;
    promptTagRequired: boolean;
    promptStoryTypeRequired: boolean;
    versionCardTypes: string;
    workspaceID: number;
    isPipelineAggregation: boolean;
    pipelineConfID: number;
    pipelineTaskRequired: boolean;
    pipelineTaskSuccRequired: boolean;
    pipelineBranch: string;
    workspaceName: string;
    noticeGroup: string;
}
export const apiSettingPrompt = createInterface<
    { spaceCode: string },
    SettingPromptInfo
>('GET', '/setting/prompt');

export const apiUpdateSettingPrompt = createInterface<
    SettingPromptInfo,
    SettingPromptInfo
>('PUT', '/setting/prompt');

export interface SettingEnvironmentItem {
    ID: number;
    name: string;
    spaceCode: string;
    creator: string;
    createTime: string;
    isDeleted: number;
    isActive: number;
}
export const apiSettingEnvironmentList = createInterface<
    { spaceCode: string, isActive?: number },
    SettingEnvironmentItem[]
>('GET', '/setting/environment/list');

export const apiSettingEnvironmentUpdate = createInterface<
    { spaceCode: string, isActive: number, name: string },
    void
>('POST', '/setting/environment/update');

export interface PromptReleaseConfirmParams {
    releaseID: number;
    confirmComment?: string;
    confirmStatus: string;
    type?: string;
}

const apiPromptReleaseConfirmRaw = createInterface<
    PromptReleaseConfirmParams,
    void
>('GET', '/prompt/release/confirm');

export const apiPromptReleaseConfirm = (params: PromptReleaseConfirmParams) => {
    return apiPromptReleaseConfirmRaw(params, {
        headers: {
            'Api-Token': getApiToken() ?? 'null',
        },
    });
};
export interface PromptTokenCountInfo {
    created: number;
    id: string;
    object: string;
    usage: Usage;
}

export interface Usage {
    prompt_tokens: number;
    total_tokens: number;
}
export const apiPromptTokenCount = createInterface<
    { modelID: number, spaceCode: string, prompt: string },
    PromptTokenCountInfo
>('POST', '/prompt/token/count');
export interface PromptReleaseDeployItem {
    ID: number;
    releaseID: number;
    versionID: number;
    status: OnlineStatusEnum;
    creator: string;
    createTime: string;
    updateUsername: string;
    updateTime: string;
    isDeleted: number;
    releaseType: ReleaseTypesEnum;
    feedback: string;
}
export const apiPromptReleaseDeployList = createInterface<
    { versionID: number },
    PromptReleaseDeployItem[]
>('GET', '/prompt/release/deploy/list');

export interface SettingJarvisInfo {
    id: number;
    spaceCode: string;
    productID: number;
    productName: string;
    appID: number;
    appName: string;
}
export const apiSettingJarvis = createInterface<
    { spaceCode: string },
    SettingJarvisInfo
>('GET', '/setting/jarvis');

export interface SettingPromptJarvisParams {
    spaceCode: string;
    productID: number;
    productName?: string;
    appID: number;
    appName?: string;
}
export const apiSettingPromptJarvis = createInterface<
    SettingPromptJarvisParams,
    void
>('POST', '/setting/jarvis');
export interface JarvisProductListItem {
    productID: number;
    productName: string;
}
export const apiJarvisProductList = createInterface<
    void,
    JarvisProductListItem[]
>('GET', '/jarvis/product/list');
export interface JarvisAppListItem {
    appID: number;
    appName: string;
}
export const apiJarvisAppList = createInterface<
    { productID: number },
    JarvisAppListItem[]
>('GET', '/jarvis/app/list');

export const apiJarvisAppUrl = createNetInterface<
    { productID: number, appID: number },
    string
>('GET', '/jarvis/app/url');
export interface PromptBatchReleaseDiffInfo {
    current: PromptBatchReleaseDiffItem;
    latest: PromptBatchReleaseDiffItem;
}

export interface PromptBatchReleaseDiffItem {
    id: number;
    name: string;
    versionName: string;
    versionCode: string;
    releaseType: number;
    env: string;
    desc: string;
    creator: string;
    createTime: string;
    confirmUser: string;
    confirmTime: string;
    prompts: PromptInfo[];
}

export interface PromptInfo {
    promptID: number;
    promptName: string;
    desc: string;
    promptCode: string;
    spaceCode: string;
    creator: string;
    createTime: string;
    versionInfo: PromptVersionVersionItem;
    tags: Tag[];
    userAuth: 'READER' | 'EDITOR' | 'NONE';
}

export const apiPromptBatchReleaseDiff = createInterface<
    { spaceCode: string },
    PromptBatchReleaseDiffInfo
>('GET', '/prompt/batchRelease/diff');

export interface PromptReleaseUniconfModuleListItem {
    ID: number;
    productID: number;
    productName: string;
    moduleID: number;
    moduleName: string;
    moduleEname: string;
}

// 查询uniconf模块列表
export const apiPromptReleaseUniconfModuleList = createInterface<
    { name: string },
    PromptReleaseUniconfModuleListItem[]
>('GET', '/prompt/release/uniconf/module/list');

export interface PromptReleaseUniconfConfigListItem {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: null;
    Name: string;
    Ename: string;
    Memo: string;
    OptUser: string;
    ProductLineEname: string;
    ModuleEname: string;
    Status: number;
    StepRelease: string;
    Schema: string;
    SchemaKey: string;
    PostProcess: string;
    Script: string;
}
// 查询uniconf配置列表
export const apiPromptReleaseUniconfConfigList = createInterface<
    { moduleEname: string },
    PromptReleaseUniconfConfigListItem[]
>('GET', '/prompt/release/uniconf/config/list');

export interface PromptReleaseUniconfConfigDataListItem {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: null;
    ConfigurationId: number;
    SchemaKey: string;
    SchemaContent: string;
    ReleaseLogId: number;
    Status: number;
    Memo: string;
    OptUser: string;
    SchemaMd5: string;
}
// 查询uniconf配置下面的数据列表
export const apiPromptReleaseUniconfConfigDataList = createInterface<
    { moduleEname: string, configEname: string },
    PromptReleaseUniconfConfigDataListItem[]
>('GET', '/prompt/release/uniconf/config/data/list');
export interface PromptReleaseUniconfConfigDataNewItem {
    ID: number;
    CreatedAt: string;
    UpdatedAt: string;
    DeletedAt: null;
    ConfigurationId: number;
    SchemaKey: string;
    SchemaContent: string;
    ReleaseLogId: number;
    Status: number;
    Memo: string;
    OptUser: string;
    SchemaMd5: string;
}
//   查询新的uniconf配置数据

export const apiPromptReleaseUniconfConfigDataNew = createInterface<
    {
        moduleEname: string;
        configEname: string;
        schemaKey: string;
        promptKey: string;
    },
    PromptReleaseUniconfConfigDataNewItem[]
>('GET', '/prompt/release/uniconf/config/data/new');

export interface FFTaskResult {
    canPublish: number;
    message: string;
    estimateTaskId: string;
    estimateTaskStatus: EstimateTaskStatusEnum;
    estimateTaskLink: string;
}
export const apiPromptBatchReleaseFFTaskResult = createInterface<
    { spaceCode: string },
    FFTaskResult
>('GET', '/prompt/batchRelease/ff/task/result');
export interface PromptBatchReleaseParams {
    name: string;
    versionName: string;
    environmentID: number;
    releaseType: ReleaseTypesEnum;
    reviewers: string;
    desc: string;
    spaceCode: string;
    diffInfo: any;
    diffIDList: any;
    ffTaskResult?: FFTaskResult;
    pipelineInfo?: any;
}
export const apiPromptBatchReleaseRaw = createInterface<
    PromptBatchReleaseParams,
    void
>('POST', '/prompt/batch/release');
export const apiPromptBatchRelease = (params: PromptBatchReleaseParams) => {
    return apiPromptBatchReleaseRaw(params, {
        headers: {
            'Api-Token': getApiToken() ?? 'null',
        },
    });
};
export const apiPromptBatchReleaseInfo = createInterface<
    { batchID: number },
    ReleaseRecordListItem
>('GET', '/prompt/batch/release/info');

export const apiCardSpaceIssueTypes = createInterface<
    { icafeSpaceCode: string },
    string[]
>('GET', '/card/space/issueTypes');

export const apiPromptVersionVersionNameGenerate = createInterface<
    { promptID: number },
    string
>('GET', '/prompt/version/versionName/generate');

export interface PromptVersionVersionNameCheckResult {
    result: boolean;
    reason: string;
}

export const apiPromptVersionVersionNameCheck = createInterface<
    { promptID: number, versionName: string },
    PromptVersionVersionNameCheckResult
>('GET', '/prompt/version/versionName/check');

export const apiPromptBatchReleaseVersionVersionNameGenerate = createInterface<
    { spaceCode: string },
    string
>('GET', '/prompt/batchRelease/versionName/generate');

export const apiPromptBatchReleaseVersionVersionNameCheck = createInterface<
    { spaceCode: string, versionName: string },
    PromptVersionVersionNameCheckResult
>('GET', '/prompt/batchRelease/versionName/check');

// 1开启，0关闭 监控开关
export const apiPromptMonitorSwitch = createInterface<
    { cronState: number, promptVersionID: number },
    void
>('POST', '/prompt/monitor/switch');

/**
 * 创建监控任务配置
 */
export interface PromptMonitorTaskCreateParams {
    promptID: number;
    promptVersionID: number;
    assertionType: AssertionTypeEnum;
    cronPolicy: CronPolicy;
    isSendEmail: number;
    alarmStrategy: number;
    usernames: string[];
    assertionList: AssertionList[];
}
export interface PromptMonitorTaskInfo extends PromptMonitorTaskCreateParams {
    id: number;
    ID: number;
    createTime: string;
    creator: string;
    cronState: number;
}

export interface AssertionList {
    variables: PromptVariable[];
    content: string;
    threshold: number;
}

export interface CronPolicy {
    minute: string;
    day: string;
    hour: string;
    month: string;
    week: string;
}

/**
 * 创建监控配置
 */
export const apiPromptMonitorTaskCreate = createInterface<
    PromptMonitorTaskInfo,
    void
>('POST', '/prompt/monitor/task/create');

/**
 * 分页查询监控配置历史列表
 */
export const apiPromptMonitorTaskList = createInterface<
    { promptVersionID: number, pn: number, size: number },
    Paginated<PromptMonitorTaskInfo>
>('GET', '/prompt/monitor/task/list');

/**
 * 监控任务详情
 */
export const apiPromptMonitorTaskInfo = createInterface<
    { promptVersionID: number, taskID?: number },
    PromptMonitorTaskInfo
>('GET', '/prompt/monitor/task/info');

/**
 * 分页查询监控运行记录
 */
export interface PromptMonitorRecordParams {
    promptVersionID: number;
    queryStartTime: string;
    queryEndTime: string;
    status: string; // SUCCESS,FAIL,RUNNING
    pn?: number;
    size?: number;
}

export interface PromptMonitorRecordListItem {
    ID: number;
    promptID: number;
    promptVersionID: number;
    taskID: number;
    status: string;
    startTime: string;
    endTime: string;
}
/**
 * 分页查询监控运行记录
 */
export const apiPromptMonitorRecordList = createInterface<
    PromptMonitorRecordParams,
    Paginated<PromptMonitorRecordListItem>
>('GET', '/prompt/monitor/record/list');

export interface PromptMonitorRecordInfo {
    ID: number;
    monitorRecordID: number;
    promptVersionID: number;
    variables: string;
    predictStatus: string;
    predictResult: string;
    assertionResult: string;
    startTime: string;
    endTime: string;
}
export interface PromptMonitorRecordInfo {
    ID: number;
    promptID: number;
    promptVersionID: number;
    taskID: number;
    status: string;
    startTime: string;
    endTime: string;
    recordItems: PromptMonitorRecordItem[];
}

export interface PromptMonitorRecordItem {
    ID: number;
    monitorRecordID: number;
    promptVersionID: number;
    variables: string;
    predictStatus: PromptPredictStatusEnum;
    predictResult: string;
    assertionResult: PromptAssertionStatusEnum;
    startTime: string;
    endTime: string;
    assertionType: AssertionTypeEnum;
}
/**
 * 分页查询监控运行记录
 */
export const apiPromptMonitorRecordInfo = createInterface<
    { monitorRecordID: number },
    PromptMonitorRecordInfo
>('GET', '/prompt/monitor/record/info');

export interface PromptQualityRunParams {
    spaceCode?: string;
    modelID: number;
    inputType: PromptSourceEnum;
    source: ProjectSourceEnum;
    text: string;
    promptID?: number;
    versionID?: number;
    variables?: PromptVariable[];
}
export interface PromptQualityRunResponse {
    ID: number;
    result: QualityResult[];
}

export interface QualityResult {
    ID: number;
    qualityID: number;
    resultType: PromptQualityResultTypeEnum;
    resultFlag: number;
    reason: string;
    resultSrc: string;
    resultTgt: string;
    isAccepted: number;
}
/**
 * 执行prompt问题诊断
 */

export const apiPromptQualityRun = createOnceWebSocket<
    PromptQualityRunParams,
    PromptQualityRunResponse
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/prompt/quality/run`);

export interface PromptQualityResultAcceptParams {
    resultID: number;
    qualityID: number;
    isAccepted: number; // 是否采纳，1是0否
}
/**
 * 采纳某个诊断结果
 */
export const apiPromptQualityResultAccept = createInterface<
    PromptQualityResultAcceptParams,
    void
>('POST', '/prompt/quality/result/accept');

export interface PromptQualityOptimizeParams {
    qualityID: number;
    text?: string;
    resultIDs?: number[];
}
export interface PromptQualityOptimizeResponse {
    afterOptPrompt: string;
}
/**
 * 基于诊断结果进行优化
 */
export const apiPromptQualityOptimize = createOnceWebSocket<
    PromptQualityOptimizeParams,
    PromptQualityOptimizeResponse
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/prompt/quality/optimize`);

export interface PromptTextListItem {
    text: string;
    variables?: PromptVariable[];
}
export interface PromptVersionBatchSubmitParams {
    promptID: number;
    promptTextList: PromptTextListItem[];
}

interface PromptVersionBatchSubmitResponse {
    promptVersionIDs: string[];
}
/**
 * 批量创建版本
 */
export const apiPromptVersionBatchSubmit = createInterface<
    PromptVersionBatchSubmitParams,
    PromptVersionBatchSubmitResponse
>('POST', '/prompt/version/batchSubmit');

export interface PromptQualityListParams {
    spaceCode: string;
    promptID?: number;
    versionID?: number;
    pn: number;
    size: number;
    keyword: string;
}

export interface PromptQualityListItem {
    ID: number;
    spaceCode: string;
    promptID: number;
    promptName: string;
    text: string;
    creator: string;
    createTime: string;
}

/**
 * 查看诊断历史列表
 */
export const apiPromptQualityList = createInterface<
    PromptQualityListParams,
    Paginated<PromptQualityListItem>
>('GET', '/prompt/quality/list');

export interface PromptQualityInfoParams {
    spaceCode: string;
    qualityID: number;
}

export interface PromptQualityInfo {
    ID: number;
    spaceCode: string;
    modelID: number;
    inputType: string;
    text: string;
    qualityModelType: string;
    promptID: number;
    promptName: string;
    afterOptPrompt: string;
    result: QualityResult[];
    variables?: PromptVariable[];
}

/**
 * 查看诊断历史列表
 */
export const apiPromptQualityInfo = createInterface<
    PromptQualityInfoParams,
    PromptQualityInfo
>('GET', '/prompt/quality/info');

export interface PromptQualityUpdateParams {
    promptID: number;
    qualityID: number;
}
/**
 * 更新诊断的promptID
 */
export const apiPromptQualityUpdate = createInterface<
    PromptQualityUpdateParams,
    void
>('POST', '/prompt/quality/update');

export interface PromptGenerateRecordUpdateParams {
    promptID: number;
    recordID: number;
}

/**
 * 更新生成的promptID
 */
export const apiPromptGenerateRecordUpdate = createInterface<
    PromptGenerateRecordUpdateParams,
    void
>('POST', '/prompt/generate/record/update');

/**
 * 发布上线
 */
export const apiPromptReleaseDeploy = createInterface<
    { releaseID: number, feedback: string },
    void
>('POST', '/prompt/release/deploy');

/**
 * 修改上线内容
 */
export const apiPromptReleaseDeployUpdate = createInterface<
    { deployID?: number, feedback: string, versionID?: number },
    void
>('PUT', '/prompt/release/deploy');

export interface PromptVariableExtractResponse {
    text: string;
    variables: PromptVariable[];
}

/**
 * 提取变量接口
 */
export const apiPromptVariableExtract = createOnceWebSocket<
    { text: string },
    PromptVariableExtractResponse
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/prompt/variable/extract`);

export interface PromptEvolveInfo {
    spaceCode: string;
    promptID?: number;
    source: ProjectSourceEnum;
    inputType: PromptSourceEnum;
    text: string;
    params: PromptParams;
    samples: PromptSample[];
}

export interface PromptSample {
    variables: PromptVariable[];
    orgResponse: string;
    isBadCase: number;
    groundTruth: string;
    errorInfo: string;
}

export interface PromptParams {
    predictModelID: number;
    topp: number;
    temperature: number;
    step: number;
    candidateNum: number;
    evalStandard: string;
}
/**
 * 创建自动调优任务接口
 */
export const apiPromptEvolveCreate = createInterface<
    PromptEvolveInfo,
    { evolveID: number }
>('POST', '/prompt/evolve/create');
export interface PromptEvolveListParams extends PaginationParams {
    keyword?: string;
    spaceCode?: string;
    source?: ProjectSourceEnum;
}
export interface PromptEvolveListItem {
    id: number;
    spaceCode: string;
    promptID: number;
    source: string;
    promptName: string;
    isPromptDeleted: number;
    text: string;
    status: PromptEvolveStatusEnum;
    creator: string;
    createTime: string;
    endTime: string;
    failMsg: string;
}
/**
 * 生成评估标准
 */
export const apiPromptEvolveStandardGenerate = createInterface<
    { text: string, samples: PromptSample[], spaceCode: string },
    string
>('POST', '/prompt/evolve/standard/generate');
/**
 * 调优列表
 */
export const apiPromptEvolveList = createInterface<
    PromptEvolveListParams,
    Paginated<PromptEvolveListItem>
>('GET', '/prompt/evolve/list');
export interface PromptEvolveResultInfo extends PromptEvolveInfo {
    id: number;
    creator: string;
    createTime: string;
    status: PromptEvolveStatusEnum;
    failMsg: string;
    curStep: number;
    results: EvolveResult[];
    promptVersionID: number;
}

export interface EvolveResult {
    orgText: string;
    orgScore: number;
    afterText: string;
    afterScore: number;
    modifyActions: string[];
    samples: EvolveResultSample[];
    evaluateModelID: number;
}

export interface EvolveResultSample extends PromptSample {
    id: number;
    afterResponse: string;
}

/**
 * 调优详情
 */
export const apiPromptEvolveInfo = createInterface<
    { evolveID: number },
    PromptEvolveResultInfo
>('GET', '/prompt/evolve/info');

/**
 * 更新自动调优的promptID
 */
export const apiPromptEvolveUpdate = createInterface<
    { evolveID: number, promptID: number },
    void
>('POST', '/prompt/evolve/update');

/**
 * Prompt迁移到指定项目
 */
export const apiPromptTransfer = createInterface<
    { promptID: number, spaceCode: string, targetSpaceCode: string },
    void
>('POST', '/prompt/transfer');
export interface ICafeCreateInfo {
    id: string;
    title: string;
    sequence: number;
    status: string;
    spacePrefixCode: string;
    createTime: string;
    type: ICafeType;
    createdUser: ICafecreatedUser;
    responsiblePeople: ResponsiblePeopleItem[];
}
export interface ICafeType {
    localId: number;
    name: string;
}
export interface ICafecreatedUser {
    username: string;
}
export interface ResponsiblePeopleItem {
    username: string;
}
/**
 * 创建icafe卡片
 */
export const apiCardCreate = createInterface<
    { icafeSpaceCode: string, title: string },
    ICafeCreateInfo
>('POST', '/card/create');

export interface PromptStatusStatsType {
    total: number;
    developingNum: number;
    evaluatingNum: number;
    releasedNum: number;
    deployedNum: number;
    monitoringNum: number;
}

/**
 * 查看prompt状态统计
 */
export const apiPromptStatusStats = createInterface<
    { spaceCode: string, dirID: number },
    PromptStatusStatsType
>('GET', '/prompt/status/stats');
export interface PromptReleasePipelineTask {
    name: string;
    id: number;
    url: string;
    status: 'RUNNING' | 'FAIL' | 'SUCCESS';
    releasePipelineInfoID: number;
    pipelineConfID: number;
    branch: string;
    pipelineBuildID: number;
    pipelineBuildNum: number;
}
export interface PromptReleasePipelineTaskParams {
    spaceCode?: string;
    versionID?: number;
    releaseID?: number;
    batchReleaseID?: number;
    releasePipelineInfoID?: number;
}
/**
 * 查询发布关联的流水线评估任务
 */
export const apiPromptReleasePipelineTask = createInterface<
    PromptReleasePipelineTaskParams,
    PromptReleasePipelineTask
>('GET', '/prompt/release/pipeline/task');

export interface PromptReleaseIdcListItem {
    ID: number;
    idcName: string;
}
/**
 * 查询机房列表
 */
export const apiPromptReleaseIdcList = createInterface<
    void,
    PromptReleaseIdcListItem[]
>('GET', '/prompt/release/idc/list');

export interface PromptPolishParams {
    promptID: number;
    versionID: number;
    text: string;
    chatbotID?: number;
}

export interface PromptPolishInfo {
    /**
     * 润色后文本
     */
    afterText: string;
    /**
     * 润色前文本
     */
    beforeText: string;
    /**
     * 聊天会话id
     */
    chatbotID: number;
    /**
     * 创建时间
     */
    createTime: string;
    /**
     * 创建人
     */
    createUser: string;
    /**
     * 润色id
     */
    id: number;
    /**
     * 是否采纳
     */
    isAccepted: boolean;
    promptID: number;
    versionID: number;
}
/**
 * Prompt润色
 */
export const apiPromptPolish = createInterface<
    PromptPolishParams,
    PromptPolishInfo
>('POST', '/prompt/polish');

/**
 * Prompt润色采纳
 */
export const apiPromptPolishAccept = createInterface<
    { id: number, chatbotRecordID?: number },
    void
>('POST', '/prompt/polish/accept');

export interface ModelListItem {
    modelID: number;
    modelName: string;
    debugParams: DisplayDebugItem[];
}

export interface PromptModelComparisonCreateParams {
    promptText?: string;
    spaceCode: string;
    /**
     * ievalue_home / stack_home
     */
    source: string;
    type: 'multi' | 'single';
    modelList: ModelListItem[];
    compModelType: string; // 'text2image' | 'image2text'
}
export interface PromptModelComparisonCreateResponse {
    comparisonID: number;
}

/**
 * 创建模型对比任务
 */
export const apiPromptModelComparisonCreate = createInterface<
    PromptModelComparisonCreateParams,
    PromptModelComparisonCreateResponse
>('POST', '/prompt/model/comparison/create');

export interface ComparisonMessageListItem {
    chatbotID: number;
    comparisonID: number;
    modelID: number;
    modelName: string;
    debugParams: DisplayDebugItem[];
    messages: Message[];
}

export interface Message {
    ID: number | string;
    chatbotID: number;
    parentID: number | string;
    role: string;
    content: string;
    searchResults?: SearchResultsItem[];
    reasoningContent?: string;
    status: number;
    imageList?: ImageListItem[];
    multiModal?: UploadFileItem[];
}
/**
 * 创建模型对比任务
 */
export const apiPromptModelComparisonMessageList = createInterface<
    { comparisonID: number},
    ComparisonMessageListItem[]
>('GET', '/prompt/model/comparison/message/list');

export interface PromptModelComparisonListParams {
    keyword?: string;
    spaceCode: string;
    pn: number;
    size: number;
    source: string;
    type: string;
    compModelType: string;
}
export interface PromptModelComparisonListItem {
    ID: number;
    models: string;
    promptText: string;
    name: string;
    spaceCode: string;
    status: string;
    creator: string;
    startTime: string;
    endTime: string;
    promptID: number;
    promptVersionID: number;
    source: string;
}
export interface PromptModelComparisonListResponse {
    list: PromptModelComparisonListItem[];
    total: number;
}
export const apiPromptModelComparisonList = createInterface<
    PromptModelComparisonListParams,
    PromptModelComparisonListResponse
>('GET', '/prompt/model/comparison/list');

// #endregion
