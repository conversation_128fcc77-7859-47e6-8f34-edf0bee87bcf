// 本身就是一个配置，需要写到一起，不再拆分
/* eslint-disable max-lines */
import {LineChartOutlined} from '@ant-design/icons';
import {IconHome} from '@baidu/devops-components';
import {
    IconAutomation,
    IconDataset,
    IconErnie,
    IconEvaluateReport,
    IconEvaluateSettings,
    IconEvaluateTask,
    IconInstrumentalization,
    IconLabel,
    IconModelManage,
    IconModelService,
    IconPermissionSettings,
    IconProjectSettings,
    IconPromptGenerate,
    IconPromptManage,
    IconEvaluateDataset,
    IconMonitor,
    IconQianfan,
    IconReflow,
    IconPromptFlow,
} from '@/icons/leftNav';
import {IconCampaign} from '@/icons/comatestack';
import {
    ErnieAutomationLink,
    ErnieInstrumentalizationLink,
    ErnieLabelLink,
    ProjectApprovalLink, ProjectCampaignLink, ProjectEvaluateDatasetLink,
    ProjectEvaluateSettingsLink,
    ProjectFilesLink,
    ProjectMembersLink,
    ProjectModelLink, ProjectMonitorsLink,
    ProjectPromptDiagnosisLink,
    ProjectPromptFlowsLink,
    ProjectPromptGenerateLink,
    ProjectPromptOptimizeLink,
    ProjectPromptsLink,
    ProjectPromptTasksLink,
    ProjectReflowLink,
    ProjectReportsLink,
    ProjectLink,
    ProjectServicesLink,
    ProjectSettingsLink,
    ProjectTasksLink,
    ProjectTrainingLink,
    ProjectStatisticsLink,
    ProjectDataAgentLink,
} from '@/links/comatestack';
import {LabelProjectListLink} from '@/links/label';
import {Project} from '@/types/comatestack/project';
import {IconStar} from '@/icons/dataAgent';
import {enableDataAgent} from '@/flags/temporary';
import {MenuItemType} from '@/types/menu/menu';
import {setLeftNavigationCollapsed} from './region';

export const getProjectItems = (
    project: Project, moduleName: string, routeParams: any, hasCompaign: boolean = false
): MenuItemType[] => {
    const {uuid} = project ?? {};
    const params = {projectUuid: uuid || routeParams?.projectUuid};

    return [
        {
            title: '推荐场景',
            icon: <IconHome />,
            to: ProjectLink.toUrl(params),
        },
        ...(enableDataAgent ? [
            {
                title: '数据Agent',
                shortTitle: 'Agent',
                icon: <IconStar />,
                to: ProjectDataAgentLink.toUrl(params),
                onClick: () => {
                    setLeftNavigationCollapsed(true);
                },
            },
        ] : []),
        // {
        //     title: 'AI Lab',
        //     icon: <IconProjectHome />,
        //     isActive: false,
        //     to: ProjectEffectEvaluateLink.toUrl(params),
        //     children: [
        //         {
        //             title: 'Prompt开发',
        //             shortTitle: 'Prompt开发',
        //             icon: <IconPromptManage />,
        //             to: ProjectPromptDevLink.toUrl(params),
        //         },
        //         {
        //             title: '效果评估',
        //             shortTitle: '评估',
        //             icon: <IconPromptManage />,
        //             to: ProjectEffectEvaluateLink.toUrl(params),
        //         },
        //     ],
        // },
        // {
        //     title: '首页',
        //     icon: <IconProjectHome />,
        //     to: ProjectLink.toUrl(params),
        // },
        ...hasCompaign ? [{
            title: '训练营',
            icon: <IconCampaign />,
            to: ProjectCampaignLink.toUrl(params),
        }] : [],
        {
            type: 'group',
            title: 'Prompt工程',
            shortTitle: 'Prompt',
            isActive: false,
            children: [
                {
                    title: 'Prompt管理',
                    shortTitle: 'Prompt',
                    icon: <IconPromptManage />,
                    isActive: moduleName === 'prompt',
                    to: ProjectPromptsLink.toUrl(params),
                },
                {
                    title: 'Prompt Flow',
                    shortTitle: 'Flow',
                    icon: <IconPromptFlow />,
                    isActive: moduleName === 'promptflow',
                    to: ProjectPromptFlowsLink.toUrl(params),
                },
                {
                    title: 'Prompt工具箱',
                    shortTitle: '工具箱',
                    icon: <IconPromptGenerate />,
                    isActive: moduleName === 'promptTools',
                    children: [
                        {
                            title: 'Prompt生成',
                            shortTitle: '生成',
                            icon: <></>,
                            to: ProjectPromptGenerateLink.toUrl(params),
                        },
                        {
                            title: 'Prompt调优',
                            shortTitle: '调优',
                            icon: <></>,
                            to: ProjectPromptOptimizeLink.toUrl(params),
                        },
                        {
                            title: 'Prompt评估',
                            shortTitle: '评估',
                            icon: <></>,
                            to: ProjectPromptTasksLink.toUrl(params),
                        },
                        {
                            title: 'Prompt诊断',
                            shortTitle: '诊断',
                            icon: <></>,
                            to: ProjectPromptDiagnosisLink.toUrl(params),
                        },
                    ],
                },

            ],
        },
        {
            type: 'group',
            title: '数据工程',
            shortTitle: '数据研发',
            isActive: false,
            children: [
                {
                    title: '数据集',
                    icon: <IconDataset />,
                    to: ProjectFilesLink.toUrl(params),
                },
                {
                    title: '数据回流',
                    icon: <IconReflow />,
                    to: ProjectReflowLink.toUrl(params),
                },
                {
                    title: '一言专业标注',
                    shortTitle: '一言标注',
                    icon: <IconErnie />,
                    to: ErnieLabelLink.toUrl(params),
                },
                {
                    title: '通用数据标注',
                    shortTitle: '通用标注',
                    icon: <IconLabel />,
                    to: LabelProjectListLink.toUrl(params),
                },
                {
                    title: '数据生产算子',
                    shortTitle: '生产算子',
                    icon: <IconInstrumentalization />,
                    to: ErnieInstrumentalizationLink.toUrl(params),
                },
                {
                    title: '数据生产流',
                    shortTitle: '生产流',
                    icon: <IconAutomation />,
                    to: ErnieAutomationLink.toUrl(params),
                },
            ],
        },
        {
            type: 'group',
            title: '评估',
            isActive: false,
            children: [
                {
                    title: '评估任务',
                    icon: <IconEvaluateTask />,
                    to: ProjectTasksLink.toUrl(params),
                    isActive: moduleName === 'task',
                },
                {
                    title: '评估集',
                    icon: <IconEvaluateDataset />,
                    to: ProjectEvaluateDatasetLink.toUrl(params),
                    isActive: moduleName === 'dataset',
                },
                {
                    title: '监控',
                    icon: <IconMonitor />,
                    to: ProjectMonitorsLink.toUrl(params),
                    isActive: moduleName === 'monitor',
                },
                {
                    title: '评估报告',
                    icon: <IconEvaluateReport />,
                    to: ProjectReportsLink.toUrl(params),
                    isActive: moduleName === 'report',
                },
            ],
        },
        {
            type: 'group',
            title: '模型工程',
            isActive: false,
            children: [
                {
                    title: '模型训练',
                    icon: <IconQianfan />,
                    isActive: moduleName === 'AITraining',
                    children: [
                        {
                            title: '一言上车',
                            icon: <></>,
                            to: ProjectApprovalLink.toUrl(params),
                        },
                        {
                            title: '千帆训练',
                            icon: <></>,
                            to: ProjectTrainingLink.toUrl(params),
                        },
                    ],
                },
                {
                    title: '模型管理',
                    icon: <IconModelManage />,
                    to: ProjectModelLink.toUrl(params),
                },
                {
                    title: '模型服务',
                    icon: <IconModelService />,
                    to: ProjectServicesLink.toUrl(params),
                },
            ],
        },
        {
            type: 'group',
            title: '设置',
            isActive: false,
            children: [
                {
                    title: '基本设置',
                    icon: <IconProjectSettings />,
                    to: ProjectSettingsLink.toUrl(params),
                },
                {
                    title: '统计',
                    icon: <LineChartOutlined />,
                    to: ProjectStatisticsLink.toUrl(params),
                },
                {
                    title: '权限设置',
                    shortTitle: '权限设置',
                    icon: <IconPermissionSettings />,
                    to: ProjectMembersLink.toUrl(params),
                },
                {
                    title: '评估设置',
                    shortTitle: '评估设置',
                    icon: <IconEvaluateSettings />,
                    isActive: ['setting'].includes(moduleName ?? ''),
                    to: ProjectEvaluateSettingsLink.toUrl(params),
                },
            ],
        },
    ];
};
