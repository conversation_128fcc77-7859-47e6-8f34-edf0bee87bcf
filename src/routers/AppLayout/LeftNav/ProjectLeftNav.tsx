import {useMemo} from 'react';
import {useMatch, useParams} from 'react-router-dom';
import {css} from '@emotion/css';
import {useProject} from '@/regions/project/project';
import {useInCampaignPlan} from '@/regions/campaigns/campaignProject';
import {WorkspaceListLink} from '@/links';
import {IconReturnSpace} from '@/icons/comatestack';
import {Menu} from '@/components/Menu';
import {WorkspaceLink} from '@/links/comatestack';
import {getProjectItems} from './getProjectItems';
import {setLeftNavigationCollapsed, useLeftNavigationCollapsed} from './region';

export const ProjectLeftNav = () => {
    const collapsed = useLeftNavigationCollapsed();
    const match = useMatch('/comatestack/app/:projectUuid/:moduleName/*');
    const projectUuid = match?.params?.projectUuid;
    const moduleName = match?.params?.moduleName;
    const project = useProject(projectUuid);
    const params = useParams();
    const inCampaignPlan = useInCampaignPlan();

    const items = useMemo(
        () => getProjectItems(project, moduleName, params, inCampaignPlan === 'yes'),
        [inCampaignPlan, moduleName, params, project]
    );

    return (
        <Menu
            className={css`background: #fff !important;`}
            collapsed={collapsed}
            onCollapse={setLeftNavigationCollapsed}
            logo={
                {
                    icon: <IconReturnSpace />,
                    title: 'Comate Stack',
                    shortTitle: '返回空间',
                    iconTo: WorkspaceLink.toUrl({workspaceUuid: project?.workspaceUuid}),
                    titleTo: WorkspaceListLink.toUrl(),
                }
            }
            items={items}
        />
    );
};
