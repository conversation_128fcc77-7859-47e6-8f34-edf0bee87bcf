/* eslint-disable max-len, max-lines */
import {RouteObject} from '@panda-design/router';
import {lazy} from 'react';
import {Navigate, useParams} from 'react-router-dom';
import {IsDatasetProvider} from '@/providers/icode/IsDatasetProvider';
import {withTrack} from '@/design/TrackRoute';
import {ProjectLink, ProjectTrainingLink} from '@/links/comatestack';
import CheckAuthLayout from '@/ievalue/CheckAuthLayout';
import {spaceRouteChildren} from '../ievalue/spaceRouteChildren';
import {
    ProjectCampaignBreadCrumb,
    ProjectDataAgentBreadCrumb,
    ProjectDocDetailBreadCrumb,
    // ProjectDocDetailBreadCrumb,
    // ProjectHomeBreadCrumb,
    ProjectTrainingBreadcrumb,
} from './BreadCrumbs/ProjectBreadcrumbs';
import {labelRouteChildren} from './labelRouteChildren';
import {ProjectScenarioBreadCrumb} from './BreadCrumbs/ProjectScenariaBreadcrumbs';
import {SecondaryContent} from './SecondaryContent';

// const ProjectHome = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/ProjectHome/Home'));

const DocIntroduction = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/DocIntroduction'));

const ProjectScenarioChat = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/ProjectScenarioChat'));

const ProjectPromptDev = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/ProjectPromptDev'));

const ProjectEffectEvaluate = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/ProjectEffectEvaluate'));

const ProjectScenario = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/ProjectScenario'));

const ProjectDataAgent = lazy(() => import(/* webpackChunkName: "ProjectHome" */ '@/comatestack/DataAgent'));

const ProjectEvaluateDocDetail = lazy(() => import(/* webpackChunkName: "ProjectCampaignDetail" */ '@/comatestack/ProjectHome/EvaluateDocDetail'));

const ProjectCampaignHome = lazy(() => import(/* webpackChunkName: "ProjectCampaignHome" */ '@/comatestack/ProjectCampaign/Home'));

const ProjectCampaignDetail = lazy(() => import(/* webpackChunkName: "ProjectCampaignDetail" */ '@/comatestack/ProjectCampaign/Detail'));

const DatasetApproval = lazy(() => import(/* webpackChunkName: "DatasetApproval" */ '@/icode/Approval'));

const DatasetCreateApproval = lazy(() => import(/* webpackChunkName: "DatasetCreateApproval" */ '@/icode/CreateApproval'));

const RedirectDefaultBranch = lazy(() => import(/* webpackChunkName: "RedirectDefaultBranch" */ '@/icode/RedirectPages/RedirectDefaultBranch'));

const DatasetFilesLayout = lazy(() => import(/* webpackChunkName: "DatasetFilesLayout" */ '@/icode/FilesLayout/DatasetFilesLayout'));

const DatasetMarkdownEdit = lazy(() => import(/* webpackChunkName: "DatasetMarkdownEdit" */ '@/icode/DatasetMarkdownEditPage/Edit'));

const DatasetMarkdownCreate = lazy(() => import(/* webpackChunkName: "DatasetMarkdownCreate" */ '@/icode/DatasetMarkdownEditPage/Create'));

const DatasetFileListMain = lazy(() => import(/* webpackChunkName: "DatasetFileListMain" */ '@/icode/DatasetFileList/FileListMain'));

const ProjectModel = lazy(() => import(/* webpackChunkName: "ProjectModel" */ '@/comatestack/ProjectModel'));

const CreateProjectModel = lazy(() => import(/* webpackChunkName: "CreateProjectModel" */ '@/comatestack/CreateProjectModel'));

const ProjectTraining = lazy(() => import(/* webpackChunkName: "ProjectTraining" */ '@/comatestack/ProjectTraining'));

const ProjectTrainingTask = lazy(() => import(/* webpackChunkName: "ProjectTrainingTask" */ '@/comatestack/ProjectTrainingTask'));

const ProjectServices = lazy(() => import(/* webpackChunkName: "ProjectServices" */ '@/comatestack/ProjectServices'));

const ProjectSettings = lazy(() => import(/* webpackChunkName: "ProjectSettings" */ '@/comatestack/ProjectSettings'));

const ProjectStatistics = lazy(() => import(/* webpackChunkName: "ProjectStatistics" */ '@/comatestack/ProjectStatistics'));

const ProjectMembers = lazy(() => import(/* webpackChunkName: "ProjectMembers" */ '@/comatestack/ProjectMembers'));

const CreateRole = lazy(() => import(/* webpackChunkName: "CreateRole" */ '@/comatestack/CreateRole'));

const RolePermission = lazy(() => import(/* webpackChunkName: "RolePermission" */ '@/comatestack/RolePermission'));

const ErnieLabel = lazy(() => import(/* webpackChunkName: "ErnieLabel" */ '@/comatestack/ErnieLabel'));

const CreateLabel = lazy(() => import(/* webpackChunkName: "CreateLabel" */ '@/comatestack/CreateLabel'));

const ProjectReflow = lazy(() => import(/* webpackChunkName: "ProjectDataFlow" */ '@/comatestack/ProjectReflow'));

const ApplyPermission = lazy(() => import('@/comatestack/ApplyPermission'));

const ErnieDataProduce = lazy(() => import(/* webpackChunkName: "ErnieDataProduce" */ '@/comatestack/ErnieDataProduce'));

const NavigateProjectHome = () => {
    const {projectUuid} = useParams();
    return <Navigate to={ProjectLink.toUrl({projectUuid})} replace />;
};

const NavigateTraining = () => {
    const {projectUuid} = useParams();
    return <Navigate to={ProjectTrainingLink.toUrl({projectUuid})} replace />;
};

const ElementDatasetFilesLayout = () => (
    <SecondaryContent>
        <IsDatasetProvider isDataset>
            <DatasetFilesLayout />
        </IsDatasetProvider>
    </SecondaryContent>
);

const ElementDatasetFileListMain = () => (
    <IsDatasetProvider isDataset>
        <DatasetFileListMain />
    </IsDatasetProvider>
);

const ElementDatasetApproval = () => (
    <SecondaryContent>
        <IsDatasetProvider isDataset>
            <DatasetApproval />
        </IsDatasetProvider>
    </SecondaryContent>
);

const ElementDatasetCreateApproval = () => (
    <SecondaryContent>
        <IsDatasetProvider isDataset>
            <DatasetCreateApproval />
        </IsDatasetProvider>
    </SecondaryContent>
);

const ElementDatasetMarkdownEdit = () => (
    <SecondaryContent>
        <IsDatasetProvider isDataset>
            <DatasetMarkdownEdit />
        </IsDatasetProvider>
    </SecondaryContent>
);

const ElementDatasetMarkdownCreate = () => (
    <SecondaryContent>
        <IsDatasetProvider isDataset>
            <DatasetMarkdownCreate />
        </IsDatasetProvider>
    </SecondaryContent>
);

export const projectRouteChildren: RouteObject[] = [
    {
        path: 'home',
        documentTitle: '推荐场景',
        breadcrumb: <ProjectScenarioBreadCrumb />,
        children: [
            {
                index: true,
                element: withTrack('ProjectScenario', ProjectScenario),
            },
            {
                path: 'intro/:docID',
                documentTitle: '场景简介',
                breadcrumb: '场景简介',
                element: withTrack('DocIntroduction', DocIntroduction),
            },
            {
                path: 'chat',
                documentTitle: 'Prompt调试',
                breadcrumb: 'Prompt调试',
                element: withTrack('ProjectScenarioChat', ProjectScenarioChat),
            },
        ],
    },
    {
        path: 'dataAgent',
        documentTitle: '数据Agent',
        breadcrumb: <ProjectDataAgentBreadCrumb />,
        children: [
            {
                index: true,
                element: withTrack('ProjectDataAgent', ProjectDataAgent),
            },
        ],
    },
    {
        path: 'lab',
        documentTitle: 'AI Lab',
        breadcrumb: <ProjectScenarioBreadCrumb />,
        children: [
            // {
            //     index: true,
            //     element: withTrack('ProjectScenario', ProjectScenario),
            // },
            {
                path: 'promptDev',
                documentTitle: '效果评估',
                breadcrumb: '效果评估',
                element: withTrack('ProjectPromptDev', ProjectPromptDev),
            },
            {
                path: 'evaluate',
                documentTitle: '效果评估',
                breadcrumb: '效果评估',
                element: withTrack('ProjectEffectEvaluate', ProjectEffectEvaluate),
            },
            {
                path: 'evaluateDocDetail',
                documentTitle: '评估能力',
                breadcrumb: <ProjectDocDetailBreadCrumb />,
                element: withTrack('ProjectEvaluateDocDetail', ProjectEvaluateDocDetail),
            },
        ],
    },
    {
        path: 'campaign',
        documentTitle: '训练营',
        breadcrumb: <ProjectCampaignBreadCrumb />,
        children: [
            {
                path: 'home',
                documentTitle: '首页',
                breadcrumb: '首页',
                element: withTrack('ProjectCampaignHome', ProjectCampaignHome),
            },
            {
                path: 'detail',
                documentTitle: '详情',
                breadcrumb: '详情',
                element: withTrack('ProjectCampaignDetail', ProjectCampaignDetail),
            },
        ],
    },
    {
        path: 'ernieLabel',
        documentTitle: '一言专业标注',
        breadcrumb: '一言专业标注',
        element: withTrack('ErnieLabel', ErnieLabel),
    },
    {
        path: 'createApproval',
        documentTitle: '新建一言上车',
        breadcrumb: '新建一言上车',
        element: withTrack('DatasetCreateApproval', ElementDatasetCreateApproval),
    },
    {path: 'tree', element: <RedirectDefaultBranch />},
    {path: 'blob', element: <RedirectDefaultBranch />},
    {path: 'edit', element: <RedirectDefaultBranch />},
    {path: 'create', element: <RedirectDefaultBranch />},
    {path: 'edit/:refName/*', element: <ElementDatasetMarkdownEdit />},
    {path: 'create/:refName/*', element: <ElementDatasetMarkdownCreate />},
    {
        element: withTrack('数据集', ElementDatasetFilesLayout),
        children: [
            {path: 'tree/:refName/*', element: <ElementDatasetFileListMain />},
            {path: 'blob/:refName/*', element: <ElementDatasetFileListMain />},
            {path: 'batchResult/*', element: <ElementDatasetFileListMain />},
        ],
    },
    {
        path: 'reflow',
        documentTitle: '数据回流',
        breadcrumb: '数据回流',
        element: withTrack('ProjectReflow', ProjectReflow),
    },
    {
        path: 'model',
        documentTitle: '模型管理',
        breadcrumb: '模型管理',
        element: withTrack('ProjectModel', ProjectModel),
    },
    {
        path: 'createModel',
        documentTitle: '创建模型',
        breadcrumb: '创建模型',
        element: withTrack('CreateProjectModel', CreateProjectModel),
    },
    {
        path: 'createLabel',
        documentTitle: '创建标注任务',
        breadcrumb: '创建标注任务',
        element: withTrack('CreateProjectLabel', CreateLabel),
    },
    ...labelRouteChildren,
    // 菜单栏改版后，将 approval 和 training 合并到 AITraining 下
    // 但是考虑到可能有其他地方依赖旧有的路由，所以暂时保留原本的路由
    {
        path: 'AITraining',
        documentTitle: '模型训练',
        breadcrumb: <ProjectTrainingBreadcrumb />,
        children: [
            {
                path: 'approval',
                documentTitle: '一言上车',
                breadcrumb: '一言上车',
                element: withTrack('DatasetApproval', ElementDatasetApproval),
            },
            {
                path: 'training',
                documentTitle: '模型训练',
                breadcrumb: <ProjectTrainingBreadcrumb />,
                children: [
                    {
                        path: 'task',
                        breadcrumb: '模型训练任务',
                        element: withTrack('ProjectTrainingTask', ProjectTrainingTask),
                    },
                    {
                        index: true,
                        element: withTrack('ProjectTraining', ProjectTraining),
                    },
                    {
                        path: '*',
                        element: <NavigateTraining />,
                    },
                ],
            },
        ],
    },
    // 以下为旧版路由
    {
        path: 'approval',
        documentTitle: '一言上车',
        breadcrumb: '一言上车',
        element: withTrack('DatasetApproval', ElementDatasetApproval),
    },
    {
        path: 'training',
        documentTitle: '模型训练',
        breadcrumb: <ProjectTrainingBreadcrumb />,
        children: [
            {
                path: 'task',
                breadcrumb: '模型训练任务',
                element: withTrack('ProjectTrainingTask', ProjectTrainingTask),
            },
            {
                index: true,
                element: withTrack('ProjectTraining', ProjectTraining),
            },
            {
                path: '*',
                element: <NavigateTraining />,
            },
        ],
    },
    {
        path: 'services',
        documentTitle: '模型服务',
        breadcrumb: '模型服务',
        element: withTrack('ProjectService', ProjectServices),
    },
    {
        path: 'settings',
        documentTitle: '基本设置',
        breadcrumb: '基本设置',
        element: withTrack('ProjectSettings', ProjectSettings),
    },
    {
        path: 'statistics',
        documentTitle: '统计',
        breadcrumb: '统计',
        element: withTrack('ProjectStatistics', ProjectStatistics),
    },
    {
        path: 'members',
        documentTitle: '权限设置',
        breadcrumb: '权限设置',
        element: withTrack('ProjectMembers', ProjectMembers),
    },
    {
        path: 'createRole',
        documentTitle: '创建角色',
        breadcrumb: '创建角色',
        element: withTrack('CreateRole', CreateRole),
    },
    {
        path: 'applyPermission',
        documentTitle: '申请权限',
        breadcrumb: '申请权限',
        element: <ApplyPermission />,
    },
    {
        path: 'permission/:roleId',
        documentTitle: '配置权限',
        breadcrumb: '配置权限',
        element: withTrack('RolePermission', RolePermission),
    },
    {
        // 在以下页面切换时，不触发 unmount
        element: withTrack('ErnieDataProduce', ErnieDataProduce),
        children: [
            {
                path: 'instrumentalization',
                documentTitle: '数据生产算子',
                breadcrumb: '数据生产算子',
            },
            {
                path: 'instrumentalization/*',
                documentTitle: '数据生产算子',
                breadcrumb: '数据生产算子',
            },
            {
                path: 'automation',
                documentTitle: '数据生产流',
                breadcrumb: '数据生产流',
            },
            {
                path: 'automation/*',
                documentTitle: '数据生产流',
                breadcrumb: '数据生产流',
            },
        ],
    },
    {
        element: <CheckAuthLayout />,
        children: spaceRouteChildren,
    },
    {
        index: true,
        element: <NavigateProjectHome />,
    },
    {
        path: '*',
        element: <NavigateProjectHome />,
    },
];
