/* eslint-disable max-len */
import {lazy} from 'react';
import {RouteObject} from '@panda-design/router';
import {Outlet} from 'react-router-dom';
import {withTrack} from '@/design/TrackRoute';
import {PageLayout} from '@/design/Layouts/PageLayout';
import {SpaceSecondaryBreadCrumbLink} from './BreadCrumbs/SpaceSecondaryBreadCrumbLink';
import {promptRouteChildren} from './promptRouteChildren';
import {taskRouteChildren} from './taskRouteChildren';
import {datasetRouteChildren} from './datasetRouteChildren';
import {reportRouteChildren} from './reportRouteChildren';
import {promptflowRouteChildren} from './promptflowRouteChildren';

const MonitorPage = lazy(() => import(/* webpackChunkName: "MonitorPage" */ '@/ievalue/MonitorPage'));
const SettingsPage = lazy(() => import(/* webpackChunkName: "SettingsPage" */ '@/ievalue/SettingsPage'));
const PromptDiagnosis = lazy(() => import('@/ievalue/PromptDiagnosis'));
const PromptOptimize = lazy(() => import('@/ievalue/PromptOptimize'));
const ComateSatckPromptEvaluatePage = lazy(() => import('@/ievalue/TaskPage/ComateSatckPromptEvaluatePage/index'));
const ComateStackGenerate = lazy(() => import('@/ievalue/PromptPage/ComateStackGenerate/index'));

const PageLayoutOutlet = () => {
    return (
        <PageLayout paddingTop>
            <Outlet />
        </PageLayout>
    );

};

export const spaceRouteChildren: RouteObject[] = [
    {
        path: 'prompt',
        documentTitle: 'Prompt',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: <PageLayoutOutlet />,
        children: promptRouteChildren,
    },
    {
        path: 'promptFlow',
        documentTitle: 'PromptFlow',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: <PageLayoutOutlet />,
        children: promptflowRouteChildren,
    },
    {
        path: 'promptTools',
        documentTitle: 'Prompt工具箱',
        breadcrumb: 'Prompt工具箱',
        children: [
            {
                path: 'promptDiagnosis',
                documentTitle: 'Prompt诊断',
                breadcrumb: 'Prompt诊断',
                element: withTrack('PromptDiagnosis', PromptDiagnosis),
            },
            {
                path: 'promptTasklist',
                documentTitle: 'Prompt评估',
                breadcrumb: 'Prompt评估',
                element: withTrack('ComateSatckPromptEvaluatePage', ComateSatckPromptEvaluatePage),
            },
            {
                path: 'promptGenerate',
                breadcrumb: 'Prompt生成',
                documentTitle: 'Prompt生成',
                element: withTrack('ComateStackGenerate', ComateStackGenerate),
            },
            {
                path: 'promptOptimize',
                documentTitle: 'Prompt调优',
                breadcrumb: 'Prompt调优',
                element: withTrack('PromptOptimize', PromptOptimize),
            },
        ],
    },
    // 菜单栏改版后，将 prompt四个工具 合并到 promptTools 下
    // 但是考虑到可能有其他地方依赖旧有的 prompt 路由，所以暂时保留原本的 prompt 路由
    {
        path: 'promptDiagnosis',
        documentTitle: 'Prompt诊断',
        breadcrumb: 'Prompt诊断',
        element: withTrack('PromptDiagnosis', PromptDiagnosis),
    },
    {
        path: 'promptTasklist',
        documentTitle: 'Prompt评估',
        breadcrumb: 'Prompt评估',
        element: withTrack('ComateSatckPromptEvaluatePage', ComateSatckPromptEvaluatePage),
    },
    {
        path: 'promptGenerate',
        breadcrumb: 'Prompt生成',
        documentTitle: 'Prompt生成',
        element: withTrack('ComateStackGenerate', ComateStackGenerate),
    },
    {
        path: 'promptOptimize',
        documentTitle: 'Prompt调优',
        breadcrumb: 'Prompt调优',
        element: withTrack('PromptOptimize', PromptOptimize),
    },
    {
        path: 'task',
        documentTitle: '评估',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: <PageLayoutOutlet />,
        children: taskRouteChildren,
    },
    {
        path: 'dataset',
        documentTitle: '评估集',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: <PageLayoutOutlet />,
        children: datasetRouteChildren,
    },
    {
        path: 'report',
        documentTitle: '报告',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: <PageLayoutOutlet />,
        children: reportRouteChildren,
    },
    {
        path: 'monitor/:moduleName/*',
        documentTitle: '监控',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: withTrack('MonitorPage', MonitorPage),
    },
    {
        path: 'setting/:moduleName/*',
        documentTitle: '设置',
        breadcrumb: <SpaceSecondaryBreadCrumbLink />,
        element: withTrack('SettingsPage', SettingsPage),
    },
];
