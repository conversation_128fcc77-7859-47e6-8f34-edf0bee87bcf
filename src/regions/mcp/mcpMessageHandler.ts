import {ChatMessage} from '@/types/staff/chat';
import {APP_IS_ONLINE_PRODUCTION} from '@/constants/app';
import {
    addMCPMessage,
    updateMCPMessage,
    getMCPMessage,
    setMCPChatStatus,
} from './mcpPlaygroundChat';

const isDirectMessageFormat = (data: ChatMessage): boolean => {
    return (
        data
        && typeof data === 'object'
        && data.messageId
        && (data.role === 'USER' || data.role === 'AGENT')
    );
};

const processMessage = (messageData: ChatMessage) => {
    if (!messageData.messageId) {
        console.error('消息缺少必要的 messageId：', messageData);
        return;
    }
    const existingMessage = getMCPMessage(messageData.messageId);
    if (existingMessage) {
        updateMCPMessage(messageData.messageId, messageData);
    } else {
        addMCPMessage(messageData);
    }
    if (messageData.role === 'AGENT' && messageData.finish) {
        setMCPChatStatus('success');
    }
};

export const mcpMessageArrived = (data: ChatMessage) => {
    if (!APP_IS_ONLINE_PRODUCTION) {
        // eslint-disable-next-line no-console
        console.log('ws message arrived:', data);
    }
    try {

        if (isDirectMessageFormat(data)) {
            processMessage(data);
            return;
        }

        console.error('无效的 WebSocket 消息格式：', data);
        setMCPChatStatus('error');
    } catch (error) {
        console.error('处理 MCP 消息失败：', error);
        setMCPChatStatus('error');
    }
};
