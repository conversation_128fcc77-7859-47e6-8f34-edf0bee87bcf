
import {createMappedRegion} from 'region-core';
import {apiPromptVariableValues, PromptVariableValuesParams, PromptVariableValuesResponse} from '@/api/ievalue/prompt';
const promptVariableValuesRegion = createMappedRegion<PromptVariableValuesParams, PromptVariableValuesResponse>();

export const usePromptVariableValuesByRegion = promptVariableValuesRegion.useValue;
export const usePromptVariableValuesLoadingByRegion = promptVariableValuesRegion.useLoading;
export const loadPromptVariableValuesByRegion = promptVariableValuesRegion.loadBy(
    params => params,
    apiPromptVariableValues
);
export const getPromptVariableValuesByRegion = promptVariableValuesRegion.getValue;
export const resetAllPromptVariableValuesByRegion = promptVariableValuesRegion.resetAll;
