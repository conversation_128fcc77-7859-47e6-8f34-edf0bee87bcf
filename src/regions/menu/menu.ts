import {MutableRefObject} from 'react';
import {createRegion} from 'region-core';

// 创建 activeGroupKey 状态区域
const activeMenuGroupKeyRegion = createRegion<string | null>(null);

// 创建 menuGroupElements 状态区域
const menuGroupElementsRegion = createRegion<MutableRefObject<Map<string, HTMLElement>>>({
    current: new Map(),
});

export const useActiveMenuGroupKey = activeMenuGroupKeyRegion.useValue;
export const setActiveMenuGroupKey = activeMenuGroupKeyRegion.set;

export const useMenuGroupElements = menuGroupElementsRegion.useValue;
export const setMenuGroupElements = menuGroupElementsRegion.set;
