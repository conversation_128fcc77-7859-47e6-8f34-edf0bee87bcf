import type {SVGProps} from "react";
const SvgStar = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 16 16"
        width="1em"
        height="1em"
        {...props}
    >
        <g clip-path="url(#clip0_1440_5042)">
            <path
                d="M7.07627 11.8636C6.85133 12.3786 6.13851 12.3786 5.9136 11.8636L5.3285 10.5234C4.80781 9.3309 3.87059 8.38157 2.70153 7.86264L1.09105 7.14777C0.579024 6.9205 0.579024 6.17562 1.09105 5.94834L2.65123 5.2558C3.85035 4.72352 4.80438 3.73904 5.31618 2.50578L5.90887 1.07766C6.12879 0.547682 6.86107 0.547681 7.081 1.07766L7.67367 2.5058C8.18547 3.73904 9.13947 4.72352 10.3386 5.2558L11.8988 5.94834C12.4108 6.17562 12.4108 6.9205 11.8988 7.14777L10.2883 7.86264C9.11927 8.38157 8.18207 9.3309 7.66133 10.5234L7.07627 11.8636ZM3.02637 6.54806C4.55027 7.2245 5.78935 8.33637 6.49492 9.86624C7.20053 8.33637 8.4396 7.2245 9.96347 6.54806C8.4214 5.86355 7.1784 4.69748 6.49493 3.14042C5.81146 4.69749 4.56845 5.86355 3.02637 6.54806ZM12.9343 15.1264L13.0988 14.7493C13.3921 14.0769 13.9205 13.5415 14.5797 13.2486L15.0866 13.0234C15.3608 12.9016 15.3608 12.5031 15.0866 12.3813L14.6081 12.1686C13.9319 11.8682 13.3941 11.313 13.1057 10.6178L12.9368 10.2102C12.819 9.92624 12.4263 9.92624 12.3085 10.2102L12.1396 10.6178C11.8513 11.313 11.3135 11.8682 10.6373 12.1686L10.1587 12.3813C9.8846 12.5031 9.8846 12.9016 10.1587 13.0234L10.6657 13.2486C11.3249 13.5415 11.8532 14.0769 12.1465 14.7493L12.3111 15.1264C12.4315 15.4025 12.8138 15.4025 12.9343 15.1264ZM12.2497 12.6978L12.6247 12.3254L12.9919 12.6978L12.6247 13.0597L12.2497 12.6978Z"
                fill="currentColor"
            />
        </g>
        <defs>
            <clipPath id="clip0_1440_5042">
                <rect
                    width="16"
                    height="16"
                    fill="currentColor"
                />
            </clipPath>
        </defs>
    </svg>

);
export default SvgStar;
