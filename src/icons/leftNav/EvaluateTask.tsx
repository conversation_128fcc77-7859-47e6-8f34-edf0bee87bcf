import type { SVGProps } from "react";
const SvgEvaluateTask = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="currentColor"
        viewBox="0 0 16 16"
        {...props}
    >
        <rect opacity="0.01" x="1" y="1" width="14" height="14" fill="currentColor" />
        <path d="M13.7148 3.2793V8.58398C13.7148 9.57767 13.2081 10.5417 12.249 11.4766C11.3589 12.3442 10.0554 13.2117 8.33496 14.0898L7.99805 14.2617L7.66406 14.084C5.96792 13.1832 4.67734 12.3065 3.78906 11.4424C2.83371 10.513 2.31577 9.57052 2.28516 8.60742V3.27734L8.02637 1.49219L13.7148 3.2793ZM3.42578 4.11816V8.57715L3.43555 8.7041C3.49903 9.28007 3.85735 9.90953 4.56152 10.6006L4.58398 10.623C5.34149 11.36 6.46557 12.1365 7.96191 12.9482L8.00879 12.9736L8.01074 12.9727C9.53082 12.1824 10.6682 11.4178 11.4297 10.6826L11.4531 10.6592C12.2223 9.90935 12.5742 9.22155 12.5742 8.58398V4.11719L8.02539 2.68848L3.42578 4.11816Z" fill="currentColor" />
        <path d="M7.95361 4.6377C9.33557 4.6379 10.4556 5.75953 10.4556 7.14258C10.4555 7.59374 10.3361 8.01723 10.1274 8.38281L11.1206 9.37598L10.314 10.1826L9.35107 9.21973C8.95204 9.48912 8.47116 9.64641 7.95361 9.64648C6.57163 9.64648 5.45092 8.52555 5.45068 7.14258C5.45068 5.75941 6.57149 4.6377 7.95361 4.6377ZM7.95361 5.7793C7.20152 5.7793 6.59131 6.38991 6.59131 7.14258C6.59154 7.89505 7.20166 8.50488 7.95361 8.50488C8.7054 8.50468 9.31471 7.89492 9.31494 7.14258C9.31494 6.39004 8.70554 5.7795 7.95361 5.7793Z" fill="currentColor" />
    </svg>
);
export default SvgEvaluateTask;
