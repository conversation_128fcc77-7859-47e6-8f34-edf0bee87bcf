import type { SVGProps } from "react";
const SvgInstrumentalization = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <g clip-path="url(#clip0_1412_11181)">
            <path d="M10.4499 8L13.0499 10.6C13.1199 10.67 13.1599 10.78 13.1499 10.88L13.0299 12.23C13.0199 12.38 12.9099 12.51 12.7599 12.55L10.9699 12.97C10.8599 13 10.7299 12.97 10.6499 12.88L8 10.2301M5.76986 7.94986L2.85988 5.09002C2.71988 4.95002 2.71988 4.73002 2.85988 4.59002L4.69988 2.75002C4.83988 2.61002 5.05988 2.61002 5.19988 2.75002L8 5.5" stroke="currentColor" stroke-width="1.14" />
            <path d="M10.49 2.9302C10.63 2.7902 10.85 2.7902 10.99 2.9302L13.22 5.1602C13.36 5.3002 13.36 5.5202 13.22 5.6602L5.48002 13.4002C5.34002 13.5402 5.12002 13.5402 4.98002 13.4002L2.75002 11.1702C2.61002 11.0302 2.61002 10.8102 2.75002 10.6702L10.49 2.9302Z" stroke="currentColor" stroke-width="1.14" />
            <path d="M5.62012 8.50977L6.62012 9.50977" stroke="currentColor" stroke-width="1.14" />
            <path d="M7.12012 6.50977L8.62012 8.00977" stroke="currentColor" stroke-width="1.14" />
            <path d="M9.12012 5.00977L10.1201 6.00977" stroke="currentColor" stroke-width="1.14" />
        </g>
        <defs>
            <clipPath id="clip0_1412_11181">
                <rect width="11.96" height="12.15" fill="white" transform="translate(2 2)" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgInstrumentalization;
