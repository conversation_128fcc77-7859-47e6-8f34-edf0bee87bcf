import type { SVGProps } from "react";
const SvgPromptFlow = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="currentColor"
        viewBox="0 0 16 16"
        {...props}
    >
        <path d="M4.59961 10.5C4.59961 10.6931 4.73691 10.8541 4.91895 10.8916L5 10.9004H9V10C9 9.44772 9.44772 9 10 9H13C13.5523 9 14 9.44772 14 10V13C14 13.5523 13.5523 14 13 14H10C9.44772 14 9 13.5523 9 13V12.0996H5C4.16544 12.0996 3.47982 11.4609 3.40625 10.6455L3.40039 10.5V6.5H2.5C1.94772 6.5 1.5 6.05228 1.5 5.5V2.5C1.5 1.94772 1.94772 1.5 2.5 1.5H5.5C6.05228 1.5 6.5 1.94772 6.5 2.5V5.5C6.5 6.05228 6.05228 6.5 5.5 6.5H4.59961V10.5ZM10.2002 12.7998H12.7998V10.2002H10.2002V12.7998ZM2.7002 5.2998H5.2998V2.7002H2.7002V5.2998Z" fill="currentColor" />
    </svg>
);
export default SvgPromptFlow;
