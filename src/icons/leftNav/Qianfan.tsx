import type { SVGProps } from "react";
const SvgQianfan = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="currentColor"
        viewBox="0 0 16 16"
        {...props}
    >
        <path d="M1 1H15V15H1V1Z" fill="white" fill-opacity="0.01" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.35457 4.19802C8.13441 4.07592 7.86686 4.07592 7.6467 4.19802H7.64757L4.78895 5.78615C4.55795 5.91477 4.41357 6.15802 4.41357 6.42227V9.58452C4.41341 9.84959 4.55715 10.0938 4.78895 10.2224L7.64495 11.8114C7.86511 11.9335 8.13266 11.9335 8.35282 11.8114L11.2114 10.2233C11.4433 10.0947 11.587 9.85046 11.5868 9.5854V6.42227C11.5864 6.15784 11.4427 5.9144 11.2114 5.78615L8.35457 4.19802ZM5.4627 6.61127L8.0002 5.20165L10.5377 6.61215V9.39552L7.99932 10.806L5.4627 9.3964V6.61127Z" fill="currentColor" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.1501 1.8751C4.1501 1.58515 3.91505 1.3501 3.6251 1.3501H1.8751C1.58515 1.3501 1.3501 1.58515 1.3501 1.8751V3.6251C1.3501 3.91505 1.58515 4.1501 1.8751 4.1501H3.6251C3.76434 4.1501 3.89787 4.09479 3.99633 3.99633C4.09479 3.89787 4.1501 3.76434 4.1501 3.6251V1.8751ZM2.4001 2.4001H3.1001V3.1001H2.4001V2.4001ZM4.1501 12.3751V14.1251C4.1501 14.2643 4.09479 14.3979 3.99633 14.4963C3.89787 14.5948 3.76434 14.6501 3.6251 14.6501H1.8751C1.58515 14.6501 1.3501 14.415 1.3501 14.1251V12.3751C1.3501 12.0851 1.58515 11.8501 1.8751 11.8501H3.6251C3.91505 11.8501 4.1501 12.0851 4.1501 12.3751ZM2.4001 12.9001H3.1001V13.6001H2.4001V12.9001ZM14.1251 1.3501C14.415 1.3501 14.6501 1.58515 14.6501 1.8751V3.6251C14.6501 3.91505 14.415 4.1501 14.1251 4.1501H12.3751C12.2359 4.1501 12.1023 4.09479 12.0039 3.99633C11.9054 3.89787 11.8501 3.76434 11.8501 3.6251V1.8751C11.8501 1.58515 12.0851 1.3501 12.3751 1.3501H14.1251ZM12.9001 2.4001H13.6001V3.1001H12.9001V2.4001ZM14.1251 11.8501C14.415 11.8501 14.6501 12.0851 14.6501 12.3751V14.1251C14.6501 14.415 14.415 14.6501 14.1251 14.6501H12.3751C12.0851 14.6501 11.8501 14.415 11.8501 14.1251V12.3751C11.8501 12.0851 12.0851 11.8501 12.3751 11.8501H14.1251ZM10.6251 2.2251C10.915 2.2251 11.1501 2.46015 11.1501 2.7501C11.1501 3.04005 10.915 3.2751 10.6251 3.2751H5.3751C5.08515 3.2751 4.8501 3.04005 4.8501 2.7501C4.8501 2.46015 5.08515 2.2251 5.3751 2.2251H10.6251ZM11.1501 13.2501C11.1501 12.9601 10.915 12.7251 10.6251 12.7251H5.3751C5.08515 12.7251 4.8501 12.9601 4.8501 13.2501C4.8501 13.54 5.08515 13.7751 5.3751 13.7751H10.6251C10.915 13.7751 11.1501 13.54 11.1501 13.2501ZM12.9001 12.9001H13.6001V13.6001H12.9001V12.9001ZM3.2751 10.6251C3.2751 10.915 3.04005 11.1501 2.7501 11.1501C2.46015 11.1501 2.2251 10.915 2.2251 10.6251V5.3751C2.2251 5.08515 2.46015 4.8501 2.7501 4.8501C3.04005 4.8501 3.2751 5.08515 3.2751 5.3751V10.6251ZM13.2501 11.1501C13.54 11.1501 13.7751 10.915 13.7751 10.6251V5.3751C13.7751 5.18753 13.675 5.01422 13.5126 4.92043C13.3502 4.82665 13.15 4.82665 12.9876 4.92043C12.8252 5.01422 12.7251 5.18753 12.7251 5.3751V10.6251C12.7251 10.915 12.9601 11.1501 13.2501 11.1501Z" fill="currentColor" />
    </svg>
);
export default SvgQianfan;
