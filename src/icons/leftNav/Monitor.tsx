import type { SVGProps } from "react";
const SvgMonitor = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="currentColor"
        viewBox="0 0 16 16"
        width="1em"
        height="1em"
        {...props}
    >
        <path d="M12.5 8C13.8806 8.00012 15 9.11936 15 10.5V12.7041H15.5322V14H9.45117V12.7041H10V10.5C10 9.11929 11.1193 8 12.5 8ZM12.5 9.09961C11.7723 9.09961 11.1743 9.6553 11.1064 10.3652L11.0996 10.5V12.7041H13.9004V10.5C13.9004 9.77236 13.3446 9.17439 12.6348 9.10645L12.5 9.09961Z" fill="currentColor" />
        <path d="M12.9 9.5L12.7113 10.7771H13.5L12.1 12.5L12.2886 11.2229H11.5L12.9 9.5Z" fill="currentColor" />
        <path d="M2.2998 2.59961H4.2998C4.52072 2.59961 4.7002 2.77909 4.7002 3V13C4.7002 13.2209 4.52072 13.4004 4.2998 13.4004H2.2998C2.07889 13.4004 1.89941 13.2209 1.89941 13V3C1.89941 2.77909 2.07889 2.59961 2.2998 2.59961Z" stroke="currentColor" stroke-width="1.2" fill="none" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5369 2C13.1583 2 13.6619 2.52382 13.6619 3.17V7H12.4119L12.4119 3.75499C12.4119 3.5037 12.2161 3.3 11.9744 3.3H7.9873C7.74809 3.3 7.55372 3.49965 7.54986 3.74747L7.5498 12.245C7.5498 12.4937 7.74178 12.6959 7.98006 12.6999L7.9873 12.7L8.69221 12.7V14L7.4248 14C6.80348 14 6.2998 13.4761 6.2998 12.83L6.2998 3.17C6.2998 2.52382 6.80348 2 7.4248 2L12.5369 2Z" fill="currentColor" />
    </svg>
);
export default SvgMonitor;
