import type { FC, SVGProps } from "react";
import { createIcon } from "@panda-design/components";
import CustomTemplate from "./CustomTemplate";
import Pass from "./Pass";
import PromptStatics from "./PromptStatics";
import Acceptance from "./Acceptance";
import AkSk from "./AkSk";
import AllTemplate from "./AllTemplate";
import Angle from "./Angle";
import AutoEntry from "./AutoEntry";
import Back from "./Back";
import BasicSettings from "./BasicSettings";
import Campaign from "./Campaign";
import CaretRight from "./CaretRight";
import ChatLoading from "./ChatLoading";
import Clear from "./Clear";
import Close from "./Close";
import CodePreview from "./CodePreview";
import Collapse from "./Collapse";
import Enter from "./Enter";
import EntryArrow from "./EntryArrow";
import EntryDesc from "./EntryDesc";
import Expand from "./Expand";
import Experiment from "./Experiment";
import FastEntry from "./FastEntry";
import Filter from "./Filter";
import Flow from "./Flow";
import Flywheel from "./Flywheel";
import IcDuoyanghua from "./IcDuoyanghua";
import IcJiemianqingxi from "./IcJiemianqingxi";
import IcLiuchenglinghuo from "./IcLiuchenglinghuo";
import IcShishijindu from "./IcShishijindu";
import IcXiangmugaoxiao from "./IcXiangmugaoxiao";
import IcZhiguanshuju from "./IcZhiguanshuju";
import Lv1 from "./Lv1";
import Lv2 from "./Lv2";
import Lv3 from "./Lv3";
import Lv4 from "./Lv4";
import Lv5 from "./Lv5";
import Members from "./Members";
import Model from "./Model";
import ModelColor from "./ModelColor";
import More from "./More";
import Number2 from "./Number2";
import Number3 from "./Number3";
import Number4 from "./Number4";
import Number5 from "./Number5";
import Number6 from "./Number6";
import Passport from "./Passport";
import Permission from "./Permission";
import PersonEval from "./PersonEval";
import Project from "./Project";
import ProjectHome from "./ProjectHome";
import React from "./React";
import RectangleTool from "./RectangleTool";
import Refresh from "./Refresh";
import Rule from "./Rule";
import Send from "./Send";
import SerialNumber from "./SerialNumber";
import Settings from "./Settings";
import Share from "./Share";
import StaffLogo from "./StaffLogo";
import TaskAssignment from "./TaskAssignment";
import UserGroup from "./UserGroup";
import View from "./View";
import ViewSizeLarge from "./ViewSizeLarge";
import ViewSizeMiddle from "./ViewSizeMiddle";
import ViewSizeSmall from "./ViewSizeSmall";
import WithDraw from "./WithDraw";
import Wrench from "./Wrench";
import ReturnSpace from "./IconReturnSpace";
import MenuListArrow from "./IconMenuListArrow";

export const IconReturnSpace = createIcon(ReturnSpace);
export const IconMenuListArrow = createIcon(MenuListArrow);
export const IconCustomTemplate = createIcon(CustomTemplate);
export const IconPass = createIcon(Pass);
export const IconPromptStatics = createIcon(PromptStatics);
export const IconAcceptance = createIcon(Acceptance);
export const IconAkSk = createIcon(AkSk);
export const IconAllTemplate = createIcon(AllTemplate);
export const IconAngle = createIcon(Angle);
export const IconAutoEntry = createIcon(AutoEntry);
export const IconBack = createIcon(Back);
export const IconBasicSettings = createIcon(BasicSettings);
export const IconCampaign = createIcon(Campaign);
export const IconCaretRight = createIcon(CaretRight);
export const IconChatLoading = createIcon(ChatLoading);
export const IconClear = createIcon(Clear);
export const IconClose = createIcon(Close);
export const IconCodePreview = createIcon(CodePreview);
export const IconCollapse = createIcon(Collapse);
export const IconEnter = createIcon(Enter);
export const IconEntryArrow = createIcon(EntryArrow);
export const IconEntryDesc = createIcon(EntryDesc);
export const IconExpand = createIcon(Expand);
export const IconExperiment = createIcon(Experiment);
export const IconFastEntry = createIcon(FastEntry);
export const IconFilter = createIcon(Filter);
export const IconFlow = createIcon(Flow);
export const IconFlywheel = createIcon(Flywheel);
export const IconIcDuoyanghua = createIcon(IcDuoyanghua);
export const IconIcJiemianqingxi = createIcon(IcJiemianqingxi);
export const IconIcLiuchenglinghuo = createIcon(IcLiuchenglinghuo);
export const IconIcShishijindu = createIcon(IcShishijindu);
export const IconIcXiangmugaoxiao = createIcon(IcXiangmugaoxiao);
export const IconIcZhiguanshuju = createIcon(IcZhiguanshuju);
export const IconLv1 = createIcon(Lv1);
export const IconLv2 = createIcon(Lv2);
export const IconLv3 = createIcon(Lv3);
export const IconLv4 = createIcon(Lv4);
export const IconLv5 = createIcon(Lv5);
export const IconMembers = createIcon(Members);
export const IconModel = createIcon(Model);
export const IconModelColor = createIcon(ModelColor);
export const IconMore = createIcon(More);
export const IconNumber2 = createIcon(Number2);
export const IconNumber3 = createIcon(Number3);
export const IconNumber4 = createIcon(Number4);
export const IconNumber5 = createIcon(Number5);
export const IconNumber6 = createIcon(Number6);
export const IconPassport = createIcon(Passport);
export const IconPermission = createIcon(Permission);
export const IconPersonEval = createIcon(PersonEval);
export const IconProject = createIcon(Project);
export const IconProjectHome = createIcon(ProjectHome);
export const IconReact = createIcon(React);
export const IconRectangleTool = createIcon(RectangleTool);
export const IconRefresh = createIcon(Refresh);
export const IconRule = createIcon(Rule);
export const IconSend = createIcon(Send);
export const IconSerialNumber = createIcon(SerialNumber);
export const IconSettings = createIcon(Settings);
export const IconShare = createIcon(Share);
export const IconStaffLogo = createIcon(StaffLogo);
export const IconTaskAssignment = createIcon(TaskAssignment);
export const IconUserGroup = createIcon(UserGroup);
export const IconView = createIcon(View);
export const IconViewSizeLarge = createIcon(ViewSizeLarge);
export const IconViewSizeMiddle = createIcon(ViewSizeMiddle);
export const IconViewSizeSmall = createIcon(ViewSizeSmall);
export const IconWithDraw = createIcon(WithDraw);
export const IconWrench = createIcon(Wrench);

export const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {
    MenuListArrow: IconMenuListArrow,
    ReturnSpace: IconReturnSpace,
    CustomTemplate: IconCustomTemplate,
    Pass: IconPass,
    PromptStatics: IconPromptStatics,
    acceptance: IconAcceptance,
    "ak-sk": IconAkSk,
    allTemplate: IconAllTemplate,
    angle: IconAngle,
    auto_entry: IconAutoEntry,
    back: IconBack,
    "basic-settings": IconBasicSettings,
    campaign: IconCampaign,
    caretRight: IconCaretRight,
    chatLoading: IconChatLoading,
    clear: IconClear,
    close: IconClose,
    codePreview: IconCodePreview,
    collapse: IconCollapse,
    enter: IconEnter,
    entry_arrow: IconEntryArrow,
    entry_desc: IconEntryDesc,
    expand: IconExpand,
    experiment: IconExperiment,
    fast_entry: IconFastEntry,
    filter: IconFilter,
    flow: IconFlow,
    flywheel: IconFlywheel,
    icDuoyanghua: IconIcDuoyanghua,
    icJiemianqingxi: IconIcJiemianqingxi,
    icLiuchenglinghuo: IconIcLiuchenglinghuo,
    icShishijindu: IconIcShishijindu,
    icXiangmugaoxiao: IconIcXiangmugaoxiao,
    icZhiguanshuju: IconIcZhiguanshuju,
    lv1: IconLv1,
    lv2: IconLv2,
    lv3: IconLv3,
    lv4: IconLv4,
    lv5: IconLv5,
    members: IconMembers,
    model: IconModel,
    modelColor: IconModelColor,
    more: IconMore,
    number2: IconNumber2,
    number3: IconNumber3,
    number4: IconNumber4,
    number5: IconNumber5,
    number6: IconNumber6,
    passport: IconPassport,
    permission: IconPermission,
    person_eval: IconPersonEval,
    project: IconProject,
    project_home: IconProjectHome,
    react: IconReact,
    rectangleTool: IconRectangleTool,
    refresh: IconRefresh,
    rule: IconRule,
    send: IconSend,
    serialNumber: IconSerialNumber,
    settings: IconSettings,
    share: IconShare,
    staffLogo: IconStaffLogo,
    taskAssignment: IconTaskAssignment,
    userGroup: IconUserGroup,
    view: IconView,
    viewSizeLarge: IconViewSizeLarge,
    viewSizeMiddle: IconViewSizeMiddle,
    viewSizeSmall: IconViewSizeSmall,
    withDraw: IconWithDraw,
    wrench: IconWrench,
};

export default iconsMap;
