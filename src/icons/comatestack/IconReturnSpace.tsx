import type {SVGProps} from "react";
const SvgIconReturnSpace = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <rect
            x="0.5"
            y="0.5"
            width="19"
            height="19"
            rx="3.5"
            stroke="currentColor"
            strokeOpacity="0.22"
        />
            <path
                d="M7.21903 9.33327H15.3334V10.6666H7.21903L10.795 14.2425L9.85221 15.1853L4.66675 9.99993L9.85221 4.81445L10.795 5.75726L7.21903 9.33327Z"
                fill="currentColor"
            />
    </svg>
);
export default SvgIconReturnSpace;
