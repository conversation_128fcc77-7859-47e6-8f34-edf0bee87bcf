import {CSSProperties, ReactNode, useMemo} from 'react';
import {css, cx} from '@emotion/css';
import {colors} from '@/constants/colors';
import {APP_IS_EXTERNAL} from '@/constants/app';

export {PageLayoutHeader, PageLayoutActions} from './PageLayoutHeader';

const grayViewCss = css`
    background-color: ${colors['gray-2']};
    padding-top: 20px;
    padding-bottom: 20px;
`;

const maxWidthMap: Record<string, number | undefined> = {
    large: undefined,
    middle: 1280,
    small: 1000,
};

// 搞那么多 props 就是想要尽量覆盖所有场景
interface Props {
    type?: 'large' | 'middle' | 'small';
    // 基本是兼容 iEValue 用的
    paddingTop?: boolean;
    // 首页等卡片场景
    grayView?: boolean;
    children: ReactNode;
    footer?: ReactNode;
    // 特殊场景的特殊样式
    style?: CSSProperties;
}

export function PageLayout({
    children,
    paddingTop,
    grayView,
    footer,
    type = 'large',
    style,
}: Props) {
    const rootCss = useMemo(
        () => css`
            display: flex;
            justify-content: center;
            flex: 1;
            overflow-x: auto;
            min-height: ${APP_IS_EXTERNAL ? '100vh' : 'calc(100vh - 48px)'};
            ${paddingTop ? 'padding-top: 20px;' : ''}
        `,
        [paddingTop]
    );

    const layoutCss = useMemo(
        () => css`
            min-width: 1000px;
            margin: 0 40px;
            flex: 1;
            ${type === 'large' ? '' : `max-width: ${maxWidthMap[type]}px;`}
        `,
        [type]
    );

    return (
        <div className={cx(rootCss, grayView && grayViewCss)} style={{...style}}>
            <div className={layoutCss}>
                {children}
            </div>
            {footer}
        </div>
    );
}
