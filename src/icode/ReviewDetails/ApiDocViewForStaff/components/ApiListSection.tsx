import {FC, RefObject} from 'react';
import styled from '@emotion/styled';
import {ApiTreeNode} from '../types';
import ApiTreeList from '../ApiTreeList';

const ListSection = styled.div<{height?: number}>`
    position: sticky;
    top: 46px;
    height: ${props => (props.height ? `${props.height}px` : 'calc(100vh - 165px)')};
    overflow: auto;
    max-width: 235px;
    border-right: 1px solid #EBEBEB;
    padding-right: 12px;

    ::-webkit-scrollbar {
        width: 4px !important;
    }

    ::-webkit-scrollbar-thumb {
        background-color: #E8E8E8 !important;
        border-radius: 4px !important;
    }
`;

interface ApiListSectionProps {
    treeData: ApiTreeNode[];
    listHeight: number;
    listSectionRef: RefObject<HTMLDivElement>;
    selectionState: {
        selectedApiIds: number[];
        currentApiId: number | null;
        expandedCatalogs: string[];
    };
    handlers: {
        handleSelectionChange: (newSelectedIds: number[]) => void;
        handleApiClick: (apiId: number) => void;
        handleCatalogExpand: (catalog: string, expanded: boolean) => void;
        handleSelectAll: (checked: boolean) => void;
    };
    selectionStatus: {
        isAllSelected: boolean;
        isIndeterminate: boolean;
    };
}

const ApiListSection: FC<ApiListSectionProps> = ({
    treeData,
    listHeight,
    listSectionRef,
    selectionState,
    handlers,
    selectionStatus,
}) => {
    if (treeData.length === 0) {
        return (
            <ListSection ref={listSectionRef} height={listHeight}>
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: '40vh',
                        borderBottom: '1px solid #EBEBEB',
                    }}
                >
                    暂无API文档数据
                </div>
            </ListSection>
        );
    }

    return (
        <ListSection ref={listSectionRef} height={listHeight}>
            <ApiTreeList
                treeData={treeData}
                selectedApiIds={selectionState.selectedApiIds}
                currentApiId={selectionState.currentApiId}
                expandedCatalogs={selectionState.expandedCatalogs}
                isAllSelected={selectionStatus.isAllSelected}
                isIndeterminate={selectionStatus.isIndeterminate}
                onSelectionChange={handlers.handleSelectionChange}
                onApiClick={handlers.handleApiClick}
                onCatalogExpand={handlers.handleCatalogExpand}
                onSelectAll={handlers.handleSelectAll}
            />
        </ListSection>
    );
};

export default ApiListSection;
