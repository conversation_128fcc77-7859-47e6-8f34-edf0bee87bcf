import {useMemo, useRef} from 'react';
import {Empty, Tooltip} from 'antd';
import moment from 'moment';
import {
    containerStyles,
    apiHeaderStyles,
    apiInfoStyles,
    apiNameStyles,
    apiPathStyles,
    methodTagStyles,
} from './styles';
import {ApiDetailViewProps} from './types';
import {MethodTag} from './components';
import RequestParametersSection from './RequestParametersSection';
import ResponseSection from './ResponseSection';

const ApiDetailView = ({
    apiDetail,
    loading = false,
    definitions = {},
    dualMode = false,
}: ApiDetailViewProps) => {
    const containerRef = useRef<HTMLDivElement>(null);

    const cardMode = useMemo(
        () => {
            if (dualMode) {
                return 'dual';
            }
            return 'single';
        },
        [dualMode]
    );

    const adaptedResponseData = useMemo(
        () => {
            if (!apiDetail) {
                return null;
            }

            const responses = apiDetail.responses?.map(response => ({
                id: response.id,
                name: response.name,
                code: response.code,
                contentType: response.contentType,
                jsonSchema: response.jsonSchema,
            })) || [];

            const responseExamples = apiDetail.responseExamples?.map(example => ({
                name: example.name,
                data: example.data,
                responseId: example.responseId,
            })) || [];

            return {
                responses,
                responseExamples,
            };
        },
        [apiDetail]
    );

    if (loading) {
        return (
            <div className={containerStyles}>
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '200px',
                    color: '#666',
                }}
                >
                    加载API详情中...
                </div>
            </div>
        );
    }

    if (!apiDetail) {
        return (
            <div className={containerStyles}>
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '300px',
                }}
                >
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="请选择一个API查看详情"
                    />
                </div>
            </div>
        );
    }

    return (
        <div ref={containerRef} className={containerStyles}>
            <div className={apiHeaderStyles}>
                <div style={{display: 'flex', flexDirection: 'row', gap: '8px', alignItems: 'center'}}>
                    <MethodTag
                        method={apiDetail.apiMethod}
                        type="solid"
                        className={methodTagStyles}
                    />

                    <div className={apiInfoStyles}>
                        <Tooltip title={apiDetail.apiName}>
                            <div className={apiNameStyles}>
                                {apiDetail.apiName}
                            </div>
                        </Tooltip>
                        <div className={apiPathStyles}>
                            {apiDetail.apiPath}
                        </div>
                    </div>
                </div>

                {apiDetail.status === 'ACCEPTED' && apiDetail.savedByUser && apiDetail.updatedTime && (
                    <div style={{
                        fontSize: '12px',
                        color: '#666',
                        marginTop: '4px',
                        lineHeight: '16px',
                    }}
                    >
                        {apiDetail.savedByUser} 保存于 {moment(apiDetail.updatedTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                )}
            </div>

            <RequestParametersSection
                parameters={apiDetail.parameters}
                requestBody={apiDetail.requestBody}
                definitions={definitions}
                mode={cardMode}
            />

            {adaptedResponseData && (
                <ResponseSection
                    responses={adaptedResponseData.responses}
                    responseExamples={adaptedResponseData.responseExamples}
                    definitions={definitions}
                    mode={cardMode}
                />
            )}
        </div>
    );
};

export default ApiDetailView;
