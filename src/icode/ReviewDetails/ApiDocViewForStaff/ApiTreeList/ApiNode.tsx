/* eslint-disable max-len */
import {Row, Col, Tooltip} from 'antd';
import {LinkOutlined} from '@ant-design/icons';
import {ApiTreeNode} from '../types';
import {formatApiMethod} from '../utils';
import {CustomCheckbox, MethodTag} from '../components';
import {
    apiContent,
    methodTag,
    apiName,
    apiNameText,
    getApiNodeStyles,
} from './styles';

interface ApiNodeProps {
    apiNode: ApiTreeNode;
    isSelected: boolean;
    isCurrent: boolean;
    onApiClick: (apiId: number) => void;
    onApiCheck: (apiId: number, checked: boolean) => void;
}

const ApiNode = ({
    apiNode,
    isSelected,
    isCurrent,
    onApiClick,
    onApiCheck,
}: ApiNodeProps) => {
    return (
        <Row
            key={apiNode.key}
            align="middle"
            className={getApiNodeStyles(isCurrent)}
            onClick={() => {
                if (apiNode.apiParseId) {
                    onApiClick(apiNode.apiParseId);
                }
            }}
        >
            <Col className={apiContent}>
                <CustomCheckbox
                    checked={isSelected}
                    disabled={apiNode.status === 'ACCEPTED'}
                    onChange={e => {
                        e.stopPropagation();
                        if (apiNode.apiParseId) {
                            onApiCheck(apiNode.apiParseId, e.target.checked);
                        }
                    }}
                    style={{marginRight: '8px'}}
                />

                <MethodTag
                    method={formatApiMethod(apiNode.apiMethod || '')}
                    type="outline"
                    className={methodTag}
                />

                <span className={apiName}>
                    <Tooltip title={apiNode.apiName}>
                        <span className={apiNameText}>
                            {apiNode.apiName}
                        </span>
                    </Tooltip>
                    {apiNode.status === 'ACCEPTED' && apiNode.iapiUrl && (
                        <Tooltip title={'该接口已保存，可点击跳转至接口详情页面。'}>
                            <LinkOutlined
                                style={{
                                    marginLeft: '6px',
                                    color: '#1890ff',
                                    cursor: 'pointer',
                                }}
                                onClick={() => {window.open(apiNode.iapiUrl, '_blank');}}
                            />
                        </Tooltip>
                    )}
                </span>
            </Col>
        </Row>
    );
};

export default ApiNode;
