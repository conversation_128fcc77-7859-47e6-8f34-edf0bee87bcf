/* eslint-disable max-lines */
import {CaseStatusEnum} from './case';

export enum EvaluateAccessWayEnum {
    API = 'api',
    GRADING = 'grading',
    ALGORITHM = 'ALGORITHM',
}

export enum EvaluateGranularityEnum {
    RECORD = 'RECORD',
    ALL = 'ALL',
    MULTI = 'MULTI',
}

export const EvaluateGranularityOptions = [
    {value: EvaluateGranularityEnum.ALL, label: '全部'},
    {value: EvaluateGranularityEnum.RECORD, label: '单条推理记录'},
];

export enum EvaluateDeptEnum {
    PUBLIC = 'PUBLIC',
    BEP = 'BEP',
    YIYAN = 'YIYAN',
    YIYANBUSINESS = 'YIYANBUSINESS',
}

export const EvaluateDeptEnumMap = {
    [EvaluateDeptEnum.PUBLIC]: '公共',
    [EvaluateDeptEnum.BEP]: EvaluateDeptEnum.BEP,
    [EvaluateDeptEnum.YIYAN]: '一言底座',
    [EvaluateDeptEnum.YIYANBUSINESS]: '一言业务',
};

export const EvaluateDeptOptions = Object.entries(EvaluateDeptEnumMap).map(
    ([key, value]) => ({
        label: value,
        value: key,
    })
);

export const EvaluateAccessWayMap = {
    [EvaluateAccessWayEnum.API]: '自定义脚本',
    [EvaluateAccessWayEnum.GRADING]: 'AI-Grading',
    [EvaluateAccessWayEnum.ALGORITHM]: '算法自动化',
};

export enum StrategyRunningModeEnum {
    MANUAL = 'MANUAL',
    AUTO = 'AUTO',
    MIX = 'MIX',
    YIYANDIY = 'YIYANDIY',
    YIYANSAMPLE = 'YIYANSAMPLE',
}

export const StrategyRunningModeMap = {
    [StrategyRunningModeEnum.MANUAL]: '人工',
    [StrategyRunningModeEnum.AUTO]: '业务评估服务',
    [StrategyRunningModeEnum.MIX]: '自定义',
    [StrategyRunningModeEnum.YIYANDIY]: '一言定制',
    [StrategyRunningModeEnum.YIYANSAMPLE]: '根据样本生成',
};

export enum StrategyGenreEnum {
    MANUAL = 'MANUAL',
    AUTO = 'AUTO',
}

export const StrategyGenreMap = {
    [StrategyGenreEnum.MANUAL]: '人工策略',
    [StrategyGenreEnum.AUTO]: '自动化策略',
};

export enum EvaluateShowMethodEnum {
    LIST = 'LIST',
    CARD = 'CARD',
}

export const EvaluateShowMethodEnumMap = {
    [EvaluateShowMethodEnum.LIST]: '列表',
    [EvaluateShowMethodEnum.CARD]: '答题',
};

export enum CaseSelectValueEnum {
    ALL = 'ALL',
    FAIL = 'FAIL',
    DISPUTE = 'DISPUTE',
    NEW = 'NEW',
    SPECIAL = 'SPECIAL',
    SAMPLING = 'SAMPLING',
}

export const CaseSelectOption = [
    {value: CaseSelectValueEnum.ALL, label: '全部'},
    {value: CaseSelectValueEnum.FAIL, label: '只看审核不通过'},
    {value: CaseSelectValueEnum.DISPUTE, label: '只看有争议'},
    {value: CaseSelectValueEnum.SAMPLING, label: '只看已抽样'},
    {value: CaseSelectValueEnum.NEW, label: '只看未答'},
    {value: CaseSelectValueEnum.SPECIAL, label: '待讨论'},
];

export const CaseSelectValueMap = {
    [CaseSelectValueEnum.ALL]: undefined as CaseStatusEnum[],
    [CaseSelectValueEnum.FAIL]: [
        CaseStatusEnum.REJECT,
        CaseStatusEnum.REJECTED,
    ],
    [CaseSelectValueEnum.DISPUTE]: [
        CaseStatusEnum.SUBMIT_DISPUTE,
        CaseStatusEnum.RESOLVE_DISPUTE,
    ],
    [CaseSelectValueEnum.NEW]: [CaseStatusEnum.NEW],
    [CaseSelectValueEnum.SPECIAL]: undefined as CaseStatusEnum[],
    [CaseSelectValueEnum.SAMPLING]: undefined as CaseStatusEnum[],
};

export enum StrategyMetricGenreEnum {
    MANUAL = 'MANUAL',
    API = 'API',
    ALGORITHM = 'ALGORITHM',
    AIGRADING = 'AIGRADING',
    YIYANDIY = 'YIYANDIY',
}

export const StrategyMetricGenreMap = {
    [StrategyMetricGenreEnum.MANUAL]: '人工',
    [StrategyMetricGenreEnum.API]: '评估服务',
    [StrategyMetricGenreEnum.ALGORITHM]: '算法自动化',
    [StrategyMetricGenreEnum.AIGRADING]: 'AI Grading',
    [StrategyMetricGenreEnum.YIYANDIY]: '一言定制',
};

export enum StrategyMetricSelectTypeEnum {
    INPUT = 'INPUT',
    SELECT = 'SELECT',
    CHECKBOX = 'CHECKBOX',
    GSB = 'GSB',
}

export const StrategyMetricSelectTypeMap = {
    [StrategyMetricSelectTypeEnum.INPUT]: '手动输入',
    [StrategyMetricSelectTypeEnum.SELECT]: '单选',
    [StrategyMetricSelectTypeEnum.CHECKBOX]: '多选',
    [StrategyMetricSelectTypeEnum.GSB]: 'GSB',
};

export enum StrategyTagDenominatorTypeEnum {
    ALL,
    SELECT,
}

export const StrategyTagDenominatorTypeMap = {
    [StrategyTagDenominatorTypeEnum.ALL]: '按全部数据集',
    [StrategyTagDenominatorTypeEnum.SELECT]: '按标签筛选',
};

export enum StrategyTagCategoryEnum {
    FREQUENCY,
    PERCENTAGE,
}

export const StrategyTagCategoryMap = {
    [StrategyTagCategoryEnum.FREQUENCY]: '频次',
    [StrategyTagCategoryEnum.PERCENTAGE]: '占比',
};

export enum OrderStageEnum {
    NEW = 'NEW',
    BUY = 'BUY',
    RENEWAL = 'RENEWAL',
    FINISHED = 'FINISHED',
}
export const OrderStagMap = {
    [OrderStageEnum.NEW]: '新建',
    [OrderStageEnum.BUY]: '购买',
    [OrderStageEnum.RENEWAL]: '续费',
    [OrderStageEnum.FINISHED]: '完成',
};
export enum OrderStatusEnum {
    FAIL = 'FAIL',
    SUCCESS = 'SUCCESS',
    RUNNING = 'RUNNING',
}
export const OrderStatusMap = {
    [OrderStatusEnum.FAIL]: '失败',
    [OrderStatusEnum.SUCCESS]: '成功',
    [OrderStatusEnum.RUNNING]: '进行中',
};

export enum ChatStepRecordTypeEnum {
    ANSWER = 'answer',
    TOOL = 'tool',
}

export enum DefalutMetricChoiceEnum {
    THREE = '三分制',
    FIVE = '五分制',
}

export const DefalutMetricChoices = {
    [DefalutMetricChoiceEnum.THREE]: [
        {
            name: '0',
            score: 0,
        },
        {
            name: '1',
            score: 1,
        },
        {
            name: '2',
            score: 2,
        },
    ],
    [DefalutMetricChoiceEnum.FIVE]: [
        {
            name: '0',
            score: 0,
        },
        {
            name: '1',
            score: 1,
        },
        {
            name: '2',
            score: 2,
        },
        {
            name: '3',
            score: 3,
        },
        {
            name: '4',
            score: 4,
        },
    ],
};

export const GSB_MANUAL_POLICY_ID = 419;

export const Evaluate_Split_Str = '$$';

export enum UserRoleEnum {
    ADMIN = 'ADMIN',
    EDITOR = 'EDITOR',
    READER = 'READER',
}

export enum PolicyOptTaskStatusEnum {
    NEW = 'NEW',
    ANALYZING = 'ANALYZING',
    FINISHED = 'FINISHED',
    FAILED = 'FAILED',
}

export enum StrategyAutoComputeStatisticTypeEnum {
    AVG = 'AVG',
    MAX = 'MAX',
    MIN = 'MIN',
    WEIGHTED_SUM = 'WEIGHTED_SUM',
}

export const StrategyAutoComputeStatisticTypeMap = {
    [StrategyAutoComputeStatisticTypeEnum.AVG]: '平均值',
    [StrategyAutoComputeStatisticTypeEnum.MAX]: '最大值',
    [StrategyAutoComputeStatisticTypeEnum.MIN]: '最小值',
    [StrategyAutoComputeStatisticTypeEnum.WEIGHTED_SUM]: '加权求和',
};

export const StrategyAutoComputeStatisticTypeOptions = Object.keys(
    StrategyAutoComputeStatisticTypeEnum
)?.map(item => ({
    label: StrategyAutoComputeStatisticTypeMap[
        item as StrategyAutoComputeStatisticTypeEnum
    ],
    value: item,
}));

export enum StrategyAutoComputeVisibilityEnum {
    TURN = 'TURN',
    SESSION = 'SESSION',
    ALL = 'ALL',
}

export const StrategyAutoComputeVisibilityOptions = Object.keys(
    StrategyAutoComputeVisibilityEnum
)?.map(item => ({
    label: item,
    value: item,
}));

export enum StrategyStatusEnum {
    ACCEPTED = 'ACCEPTED', // 接受优化
    OPTIMIZED = 'OPTIMIZED', // 优化完成
    OPTIMIZING = 'OPTIMIZING', // 优化中
    VIEWED = 'VIEWED', // 已查看
    FAILED = 'FAILED', // 优化失败
    CREATING = 'CREATING', // 创建中
    CREATED = 'CREATED', // 已创建
}
