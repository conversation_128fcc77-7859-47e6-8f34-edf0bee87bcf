const MenuColors = {
    bg: '#FFFFFF',
    borderColor: '#E8E8E8',
    color: '#181818',
    activeColor: '#0080FF',
    listBg: 'transparent',
    listBgDeep: '#F5F5F5',
    itemBgActive: '#E5F2FF',
    itemBgHover: '#F2F2F2',
};

export const MenuVariables = {
    top: 'var(--panda-left-navigation-top, 48px)',
    bg: `var(--panda-left-navigation-bg, ${MenuColors.bg})`,
    zIndex: 'var(--panda-left-navigation-z-index, 2)',
    borderColor: `var(--panda-left-navigation-border-color, ${MenuColors.borderColor})`,
    widthCollapsed: 'var(--panda-left-navigation-width-collapsed, 56px)',
    paddingCollapsed: 'var(--panda-left-navigation-padding-collapsed, 2px)',
    widthExpanded: 'var(--panda-left-navigation-width-expanded, 190px)',
    paddingExpanded: 'var(--panda-left-navigation-padding-expanded, 4px)',
    color: `var(--panda-left-navigation-color, ${MenuColors.color})`,
    activeColor: `var(--panda-left-navigation-active-color, ${MenuColors.activeColor})`,
    listBg: `var(--panda-left-navigation-list-bg, ${MenuColors.listBg})`,
    listBgDeep: `var(--panda-left-navigation-list-bg-deep, ${MenuColors.listBgDeep})`,
    itemBgActive: `var(--panda-left-navigation-item-bg-active, ${MenuColors.itemBgActive})`,
    itemBgHover: `var(--panda-left-navigation-item-bg-hover, ${MenuColors.itemBgHover})`,
    iconSize: 'var(--panda-left-navigation-icon-size, 16px)',
    logoIconSize: 'var(--panda-left-navigation-logo-icon-size, 20px)',
    iconTop: 'var(--panda-left-navigation-icon-top, 7px)',
    gapVerticalCollapsed: 'var(--panda-left-navigation-gap-vertical-collapsed, 4px)',
    gapVerticalExpanded: 'var(--panda-left-navigation-gap-vertical-expanded, 4px)',
};

export const MenuCalculated = {
    innerWidthCollapsed: `calc(${MenuVariables.widthCollapsed} - 8px)`,
    innerWidthExpanded: `calc(${MenuVariables.widthExpanded} - 20px)`,
};
