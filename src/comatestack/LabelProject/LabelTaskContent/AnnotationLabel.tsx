/* eslint-disable complexity */
import styled from '@emotion/styled';
import {fontSize, Text, Tag} from '@panda-design/components';
import {Popover} from 'antd';
import {Annotation, LabelPrediction} from '@/types/label/annotation';
import {getTimeDistance} from '@/utils/getTimeDistance';
import {IconModelColor, IconRule} from '@/icons/comatestack';
import {APP_IS_EXTERNAL} from '@/constants/app';
import UsersAvatarGroup from '@/design/UsersAvatarGroup';
import {useLabelTaskId} from '@/hooks/label/useLabelTaskId';
import {useReviewResultItem} from '@/regions/projectLabelTask/LabelTaskReview';
import AnnoPopoverContent from './AnnoPopoverContent';

const StyledTag = styled(Tag)`
    border-radius: 14px !important;
    margin-left: 10px !important;
    margin-right: 0px !important;
`;

interface Params {
    type: 'annotation' | 'prediction';
    idataset_model_id?: number;
    annotation?: Annotation;
    nameType?: 'create' | 'update';
}

interface Props {
    type: 'annotation' | 'prediction';
    annotation?: Annotation;
    prediction?: LabelPrediction;
    atType?: 'create' | 'update';
}

const getUsername = ({type, annotation, idataset_model_id, nameType}: Params) => {
    if (type === 'prediction') {
        if (idataset_model_id) {
            return '模型预标注';
        }
        return '规则预标注';
    }
    if (nameType === 'create') {
        return annotation.created_username.split('@')[0].trim();
    }
    return annotation?.updated_username?.split('@')[0]?.trim();
};

const getIsDraft = ({type, annotation}: Params) => {
    if (type === 'prediction') {
        return false;
    }
    return !(annotation?.id > 0);
};

const getAt = ({type, annotation, prediction, atType}: Props) => {
    if (type === 'prediction') {
        return getTimeDistance(prediction.created_at);
    }
    if (atType === 'create') {
        return getTimeDistance(annotation.created_at);
    }
    return getTimeDistance(annotation.updated_at);
};

const Container = styled.div`
    display: flex;
    align-items: center;
    gap: 10px;
`;

const iconStyle = {width: 20, height: 20};

export const AnnotationLabel = ({type, prediction, annotation}: Props) => {
    // 本来是想用 prediction_type 来判断，但是发现 prediction_type 字段有的时候没值（后端问题），所以只能用 idataset_model_id 来判断，如果有值就是模型标注，没有值就是规则标注
    const {idataset_model_id} = prediction ?? {};
    const isDraft = getIsDraft({type, annotation});
    const createUser = getUsername({type, annotation, idataset_model_id, nameType: 'create'});
    const updateUser = getUsername({type, annotation, idataset_model_id, nameType: 'update'});
    const usernames = createUser === updateUser ? [createUser] : [createUser, updateUser];
    const createAt = getAt({type, annotation, prediction, atType: 'create'});
    const updateAt = getAt({type, annotation, prediction, atType: 'update'});
    const taskId = useLabelTaskId();
    const {result: reviewData} = useReviewResultItem({taskId}) ?? {};

    const content = (
        <Container>
            <div>
                {type === 'prediction' ? (
                    idataset_model_id ? <IconModelColor style={iconStyle} /> : <IconRule style={iconStyle} />
                ) : <UsersAvatarGroup usernames={usernames} />}
            </div>
            <div style={{display: 'flex', flexDirection: 'column', flex: 1}}>
                <div>
                    <Text>{updateUser || createUser}</Text>
                    {isDraft && <Text type="tertiary">(草稿)</Text>}
                    {
                        type !== 'prediction' && (
                            <>
                                {reviewData?.result === 'PASS'
                                    && <StyledTag type="flat" color="success">审核通过</StyledTag>}
                                {reviewData?.result === 'FAILED'
                                    && <StyledTag type="flat" color="error">审核不通过</StyledTag>}
                            </>
                        )
                    }
                </div>
                <Text type="secondary" className={fontSize(12)}>
                    {updateAt || createAt}
                </Text>
            </div>
        </Container>
    );

    return type === 'annotation' ? (
        <Popover
            content={APP_IS_EXTERNAL ? null : <AnnoPopoverContent createUser={createUser} updateUser={updateUser} />}
            placement="bottom"
        >
            {content}
        </Popover>
    ) : content;
};
