/* eslint-disable max-lines */
import {Flex, Form, Input} from 'antd';
import {SearchOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {RadioButtonGroup} from '@/components/MCP/RadioButtonGroup';
import TitleTabs from '@/components/MCP/TitleTabs';
import {MCPPlaygoundButton} from '@/components/MCP/MCPPlaygoundButton';
import {CreateMCPButton} from '@/components/MCP/CreateMCPButton';
import LabelsFilterContent from './LabelsFilterContent';
import PublishOrderSelect from './PublishOrderSelect';
import ViewOrderSelect from './ViewOrderSelect';

const StyleForm = styled(Form)`
    position: relative !important;
    .ant-5-form-css-var {
        --ant-5-form-item-margin-bottom: 0px !important;
    }
    label {
        color: #8F8F8F!important;
        gap: 16px !important;
        &: before{
            display: none !important;
        }
        &: after{
            content: '' !important;
            width: 1px !important;
            height: 16px !important;
            background: #D9D9D9 !important;
        }
    }
    .noLine {
        .ant-5-form-item-label{
            margin-right: 12px !important;
        }
        label{
            &: after{
                display: none !important;
            }
        }
    }

`;

const RightFilterWrapper = styled(Flex)`
    position: absolute;
    right: 0;
    bottom: 0;
`;

const items = [
    // {
    //     label: '精选',
    //     key: 'id1',
    // },
    {
        label: '全部',
        key: 'all',
    },
    {
        label: '我收藏的',
        key: 'favorite',
    },
    {
        label: '我发布的',
        key: 'isMine',
    },
];

export interface FilterValues {
    serverSourceType?: string;
    serverProtocolType?: string;
    labels?: number[];
    viewOrder?: 'DESC' | 'ASC';
    publishOrder?: 'DESC' | 'ASC';
}

export interface TabValues {
    tab: string;
    keywords?: string;
    favorite?: boolean;
    isMine?: boolean;
}

interface Props {
    initialTabFormValue: TabValues;
    initialFilterFormValue: FilterValues;
}
const SquireFilter = ({initialTabFormValue, initialFilterFormValue}: Props) => {
    const [tabForm] = Form.useForm();
    const [filterForm] = Form.useForm();

    return (
        <>
            <Form
                name="tab"
                form={tabForm}
                initialValues={initialTabFormValue}
            >
                <Form.Item name="tab" noStyle>
                    <TitleTabs
                        tabBarExtraContent={(
                            <Flex align="center" gap={8} style={{marginTop: 8}}>
                                <Form.Item name="keywords" noStyle>
                                    <Input
                                        autoFocus
                                        style={{width: 250}}
                                        placeholder="输入MCP Server名称"
                                        suffix={<SearchOutlined />}
                                        allowClear
                                    />
                                </Form.Item>
                                <CreateMCPButton />
                                <MCPPlaygoundButton />
                            </Flex>
                        )}
                        items={items}
                    />
                </Form.Item>
            </Form>
            <StyleForm
                colon={false}
                labelCol={{flex: '60px'}}
                labelAlign="left"
                form={filterForm}
                initialValues={initialFilterFormValue}
                name="filter"
            >
                <Flex vertical gap={8}>
                    <Form.Item name="serverSourceType" label="类型">
                        <RadioButtonGroup
                            options={[
                                {label: '全部', value: 'all'},
                                {label: '标准MCP', value: 'external'},
                                {label: 'Remote', value: 'openapi'},
                                {label: 'Local', value: 'script'},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item name="serverProtocolType" label="协议">
                        <RadioButtonGroup
                            options={[
                                {label: '全部', value: 'all'},
                                {label: 'SSE', value: 'sse'},
                                {label: 'STDIO', value: 'stdio'},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item name="labels" label="场景">
                        <LabelsFilterContent />
                    </Form.Item>
                    <RightFilterWrapper gap={24}>
                        <Form.Item name="viewOrder" className="noLine" label="浏览量">
                            <ViewOrderSelect />
                        </Form.Item>
                        <Form.Item name="publishOrder" className="noLine" label="发布时间">
                            <PublishOrderSelect />
                        </Form.Item>
                    </RightFilterWrapper>
                </Flex>
            </StyleForm>
        </>
    );
};

export default SquireFilter;
