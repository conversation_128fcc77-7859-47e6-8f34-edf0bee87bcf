import {useRequest} from 'huse';
import {useCallback, useMemo} from 'react';
import {uniq} from 'lodash';
import {apiGetDefaultLabels} from '@/api/mcp';
import {RadioButtonGroup} from '@/components/MCP/RadioButtonGroup';

interface Props {
    value?: number[];
    onChange?: (value: number[]) => void;
}

export const ALL_LABELS = -2;
export const OTHER_LABELS = -1;

const LabelsFilterContent = ({value = [], onChange}: Props) => {
    const {data} = useRequest(apiGetDefaultLabels, null);
    const options = useMemo(
        () => data?.map(({labelValue, id}) => ({
            label: labelValue,
            value: id,
        })),
        [data]
    );
    const handleChange = useCallback(
        (labelValue: number) => {
            if (labelValue === ALL_LABELS || labelValue === OTHER_LABELS) {
                return onChange?.([labelValue]);
            } else if (value?.includes(labelValue)) {
                const result = value?.filter(i => i !== labelValue);
                onChange?.(result?.length > 0 ? result : [ALL_LABELS]);
            } else {
                onChange?.(uniq([...value?.filter(i => i !== ALL_LABELS && i !== OTHER_LABELS), labelValue]));
            }
        },
        [onChange, value]
    );
    return (
        <RadioButtonGroup
            isMulti
            value={value || []}
            onChange={handleChange}
            options={[
                {label: '全部', value: ALL_LABELS},
                ...options ?? [],
                {label: '其他', value: OTHER_LABELS},
            ]}
        />
    );
};

export default LabelsFilterContent;

