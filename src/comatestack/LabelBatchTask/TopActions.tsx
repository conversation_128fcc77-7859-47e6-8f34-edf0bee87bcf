import styled from '@emotion/styled';
import {Button, Tag, Text} from '@panda-design/components';
import {Divider, Popover, Space} from 'antd';
import {CaretLeftFilled, CaretRightFilled} from '@ant-design/icons';
import {colors} from '@/constants/colors';
import {IconCodePreview} from '@/icons/comatestack';
import {
    useBatchMarkTaskIndex, useSetBatchMarkTaskIndex,
} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {setIsShowLabelTaskDetailModal} from '@/regions/label/tableAction';
import {useBatchTaskTotal} from '@/hooks/label/useLabelBatchTaskParams';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {SidebarFloatButton} from '@/components/Label/LabelPredictionDetail/SidebarFloatButton';
import {useCurrentLabelTaskDetail} from '@/regions/projectLabelTask/labelTaskDetail';
import UsersAvatarGroup from '@/design/UsersAvatarGroup';
import {getTimeDistance} from '@/utils/getTimeDistance';
import {APP_IS_EXTERNAL} from '@/constants/app';
import {useReviewResultItem} from '@/regions/projectLabelTask/LabelTaskReview';
import {useLabelTaskId} from '@/hooks/label/useLabelTaskId';
import AnnoPopoverContent from '../LabelProject/LabelTaskContent/AnnoPopoverContent';
import {CurrentIndexQuickEdit} from './CurrentIndexQuickEdit';

const TopContainer = styled.div`
    border-bottom: 1px solid ${colors['gray-4']};
    padding: 10px 20px;
    background-color: ${colors['gray-2']};
    position: relative;
`;

const Buttons = styled.div`
    display: flex;
    align-items: center;
`;

const StyledTag = styled(Tag)`
    border-radius: 14px !important;
    margin-left: 5px !important;
    margin-right: 0px !important;
`;

function TopActions() {
    const {labelStudioProjectId} = useLabelParams();
    const currentIndex = useBatchMarkTaskIndex();
    const total = useBatchTaskTotal();
    const {backward, forward} = useSetBatchMarkTaskIndex();
    const {annotations} = useCurrentLabelTaskDetail() ?? {};
    const createUser = annotations?.[0]?.created_username?.split('@')[0].trim();
    const updateUser = annotations?.[0]?.updated_username?.split('@')[0].trim();
    const usernames = createUser === updateUser ? [createUser] : [createUser, updateUser];
    const createAt = getTimeDistance(annotations?.[0]?.created_at);
    const updateAt = getTimeDistance(annotations?.[0]?.updated_at);
    const taskId = useLabelTaskId();
    const {result: reviewData} = useReviewResultItem({taskId}) ?? {};

    return (
        <TopContainer>
            <Space size={1}>
                #{labelStudioProjectId}
                <Divider type="vertical" />
                <Buttons>
                    <Button
                        type="text"
                        onClick={backward}
                        disabled={currentIndex === 0}
                    >
                        <CaretLeftFilled />上一条
                    </Button>
                    <CurrentIndexQuickEdit />
                    <Button
                        type="text"
                        onClick={forward}
                        disabled={currentIndex === total - 1}
                    >
                        下一条<CaretRightFilled />
                    </Button>
                    <Divider type="vertical" />
                    {annotations?.[0] && (
                        <Space size={5}>
                            <Popover
                                content={APP_IS_EXTERNAL
                                    ? null
                                    : <AnnoPopoverContent createUser={createUser} updateUser={updateUser} />}
                                placement="bottom"
                                trigger="hover"
                            >
                                <div style={{display: 'flex', alignItems: 'center'}}>
                                    <UsersAvatarGroup usernames={usernames} />
                                </div>
                            </Popover>
                            <Text>
                                {updateAt || createAt}
                            </Text>
                            {annotations?.[0]?.id < 0 && <Text type="tertiary">(草稿)</Text>}
                            {reviewData?.result === 'PASS' && <StyledTag type="flat" color="success">审核通过</StyledTag>}
                            {reviewData?.result === 'FAILED' && <StyledTag type="flat" color="error">审核不通过</StyledTag>}
                        </Space>
                    )}
                    <Divider type="vertical" />
                    <Button
                        type="text"
                        onClick={() => {
                            setIsShowLabelTaskDetailModal(true);
                        }}
                    >
                        <IconCodePreview />数据详情
                    </Button>
                </Buttons>
            </Space>
            <SidebarFloatButton />
        </TopContainer>
    );
}

export default TopActions;
