/* eslint-disable max-lines */
import {Table, TableColumnsType} from 'antd';
import {useState, useEffect, useCallback, useMemo, useRef, UIEvent} from 'react';
import {marginTop, message} from '@panda-design/components';
import {debounce} from 'lodash';
import {Project} from '@/types/comatestack/project';
import {MemberItem} from '@/types/comatestack/suggestUsers';
import UsersAvatarGroup from '@/design/UsersAvatarGroup';
import {WorkspaceNameLink} from '@/components/comatestack/EntityCells/WorkspaceNameLink';
import {Workspace} from '@/types/comatestack/workspace';
import {ProjectTableNameLink} from '@/components/comatestack/EntityCells/ProjectTableNameLink';
import {ProjectLinkGroup} from '@/components/comatestack/EntityCells/ProjectLinkGroup';
import {IsPrivateTag} from '@/components/comatestack/EntityCells/IsPrivateTag';
import {apiGetPagedProjectList} from '@/api/project';
import {loadProjectList} from '@/regions/project/projectList';
import {getRecentProject, loadProjectListById} from '@/regions/project/recentProjectList';

const getColumns = (
    searchValue: string,
    onFavorite: (project: Project) => void
): TableColumnsType<Project> => [
    {
        dataIndex: 'name',
        title: '项目名称',
        render: (_, project) => {
            return (
                <ProjectTableNameLink
                    searchValue={searchValue}
                    project={project}
                    onFavorite={onFavorite}
                />
            );
        },
    },
    {
        dataIndex: 'workspaceUuid',
        title: '所属空间',
        render: (_, project) => {
            const workspace = {uuid: project.workspaceUuid, name: project.workspaceName} as Workspace;
            return (
                <WorkspaceNameLink
                    enableTable
                    searchValue={searchValue}
                    workspace={workspace}
                />
            );
        },
    },
    {
        dataIndex: 'admins',
        title: '项目管理员',
        render: (admins: MemberItem[]) => {
            return (
                <UsersAvatarGroup usernames={admins?.map(item => item.name)} />
            );
        },
    },
    {
        dataIndex: 'visibleType',
        title: '项目类型',
        render: (visibleType: string) => <IsPrivateTag isPrivate={visibleType === 'PRIVATE'} />,
    },
    {
        dataIndex: 'operations',
        title: '操作',
        width: 320,
        render: (_, project) => {
            return <ProjectLinkGroup type="table" project={project} />;
        },
    },
];

interface Props {
    searchValue: string;
}

function AllProjectTabContent({searchValue}: Props) {
    const [loading, setLoading] = useState(false);
    const [projectList, setProjectList] = useState<Project[]>([]);
    const [totalNum, setTotalNum] = useState(0);
    const [pageNum, setPageNum] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const pageSize = 10;
    const containerRef = useRef<HTMLDivElement>(null);

    const hasMoreData = useMemo(
        () => {
            return pageNum * pageSize < totalNum;
        },
        [totalNum, pageNum]
    );

    const getPagedProjects = useCallback(
        async (offset: number, limit: number, keyword?: string) => {
            try {
                setLoading(true);
                const res = await apiGetPagedProjectList({
                    _offset: offset,
                    _limit: limit,
                    ...(keyword ? {keyword, scope: 'ALL'} : {scope: 'MINE'}),
                });
                setProjectList(prev => (offset === 0 ? res.list : [...prev, ...res.list]));
                setTotalNum(res.totalNum);
                return res;
            } catch (err) {
                message.error('获取项目列表失败');
                throw err;
            } finally {
                setLoading(false);
            }
        },
        []
    );

    useEffect(
        () => {
            getPagedProjects(0, pageSize, searchValue);
            setPageNum(1);
        },
        [searchValue, getPagedProjects]
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const loadMoreData = useCallback(
        debounce(async () => {
            if (hasMoreData && !isLoadingMore) {
                try {
                    setIsLoadingMore(true);
                    await getPagedProjects(pageNum * pageSize, pageSize, searchValue);
                    setPageNum(pageNum + 1);
                } catch (err) {
                    message.error('加载更多数据失败');
                } finally {
                    setIsLoadingMore(false);
                }
            }
        }, 300),
        [hasMoreData, isLoadingMore, pageNum, pageSize, searchValue, getPagedProjects]
    );

    useEffect(
        () => {
            if (containerRef.current && hasMoreData && !isLoadingMore) {
                requestAnimationFrame(() => {
                    const containerHeight = containerRef.current?.clientHeight || 0;
                    const contentHeight = containerRef.current?.scrollHeight || 0;

                    // 如果内容高度小于容器高度，并且还有更多数据，则自动加载更多数据
                    // 添加20px缓冲，避免边缘情况
                    if (contentHeight < containerHeight + 20 && hasMoreData && !isLoadingMore) {
                        setIsLoadingMore(true);
                        loadMoreData();
                    }
                });
            }
        },
        [hasMoreData, isLoadingMore, loadMoreData]
    );

    const handleScroll = useCallback(
        (e: UIEvent<HTMLDivElement>) => {
            const {scrollTop, scrollHeight, clientHeight} = e.currentTarget;
            const scrollThreshold = 50;
            if (scrollHeight - (scrollTop + clientHeight) <= scrollThreshold && hasMoreData && !isLoadingMore) {
                setIsLoadingMore(true);
                loadMoreData();
            }
        },
        [hasMoreData, isLoadingMore, loadMoreData]
    );

    const onFavorite = useCallback(
        async (project: Project) => {
            setProjectList([]);
            await getPagedProjects(0, pageSize);
            setPageNum(1);

            const {workspaceUuid} = project;
            loadProjectList({workspaceUuid});
            const uuids = getRecentProject();
            if (uuids.length) {
                loadProjectListById({uuids});
            }
        },
        [pageSize, getPagedProjects]
    );

    const columns = useMemo(
        () => getColumns(searchValue, onFavorite),
        [searchValue, onFavorite]
    );

    return (
        <div
            ref={containerRef}
            style={{
                maxHeight: 'calc(100vh - 200px)',
                overflow: 'auto',
            }}
            onScroll={handleScroll}
        >
            <Table<Project>
                className={marginTop(20)}
                rowKey="uuid"
                columns={columns}
                dataSource={projectList}
                pagination={false}
                loading={loading && !projectList.length}
                scroll={{y: undefined}}
            />
            {isLoadingMore && <div style={{textAlign: 'center', padding: '10px 0'}}>加载中...</div>}
            {totalNum !== 0 && !hasMoreData && !isLoadingMore && (
                <div style={{textAlign: 'center', padding: '10px 0', color: '#999'}}>没有更多数据了</div>
            )}
        </div>
    );
}

export default AllProjectTabContent;
