import {Tabs, type TabsProps} from 'antd';
import {useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {SectionCard} from '@/design/Layouts/SectionCard';
import {myToken} from '@/constants/colors';
import AllSpaceTabContent from './AllSpaceTabContent';
import AllProjectTabContent from './AllProjectTabContent';
import AllSpaceTabContentExtra from './AllSpaceTabContentExtra';
import AllProjectTabContentExtra from './AllProjectTabContentExtra';

const StyledTabs = styled(Tabs)`
    .ant-5-tabs {
        padding: 0 !important;
    }

    .ant-5-tabs-tab-active {
        font-size: 24px !important;
        font-weight: 500 !important;
    }

    .ant-5-tabs-tab-btn {
        width: 95px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .ant-5-tabs-ink-bar {
        --ant-5-line-width-bold: 6px !important;
        background: ${myToken.gradientSolid2} !important;
        border-radius: 4px;
        z-index: -1;
        bottom: 4px !important;
    }
`;

const getColumns = (searchValue: string): TabsProps['items'] => {
    return [
        {
            key: 'allProject',
            label: '全部项目',
            children: <AllProjectTabContent searchValue={searchValue} />,
        },
        {
            key: 'allSpace',
            label: '全部空间',
            children: <AllSpaceTabContent searchValue={searchValue} />,
        },
    ];
};

export const HomeTabs = () => {
    const [searchValue, setSearchValue] = useState<string>('');
    const [tab, setTab] = useState<string>('allProject');

    const items: TabsProps['items'] = useMemo(
        () => getColumns(searchValue),
        [searchValue]
    );

    return (
        <SectionCard>
            <StyledTabs
                items={items}
                tabBarGutter={20}
                onChange={(activeKey: string) => {
                    setSearchValue('');
                    setTab(activeKey);
                }}
                tabBarExtraContent={tab === 'allSpace' ? (
                    <AllSpaceTabContentExtra setSearchValue={setSearchValue} />
                ) : (
                    <AllProjectTabContentExtra setSearchValue={setSearchValue} />
                )}
            />
        </SectionCard>
    );
};
