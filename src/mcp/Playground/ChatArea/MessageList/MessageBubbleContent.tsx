import {Flex, Spin, Typography} from 'antd';
import styled from '@emotion/styled';
import {Elements} from '@/components/Chat/Element';
import {ChatMessage} from '@/types/staff/chat';
import {
    UI_DIMENSIONS,
    UI_COLORS,
    CONFIG_VALUES,
} from '../constants';

const UserContainer = styled.div`
    gap: ${UI_DIMENSIONS.SPACING_SMALL}px;
    padding: ${UI_DIMENSIONS.PADDING_XS};
    background: ${UI_COLORS.BACKGROUND_USER_BUBBLE};
    border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_MEDIUM}px;
    width: fit-content;
`;

const AIContainer = styled.div`
    width: 100%;
    max-width: 100%;
    margin-left: 50px;
`;
interface MessageBubbleContentProps {
    message: ChatMessage;
    isUser: boolean;
}

const MessageBubbleContent = ({message, isUser}: MessageBubbleContentProps) => {
    const isEmpty = !message.finish && !message?.elements?.length;

    if (isUser) {
        return (
            <Flex justify="end">
                <UserContainer>
                    <Elements items={message?.elements} />
                </UserContainer>
            </Flex>
        );
    }

    return (
        <Flex justify="start">
            <AIContainer>
                <Flex vertical gap={UI_DIMENSIONS.SPACING_LARGE}>
                    {isEmpty && (
                        <Flex align="center" gap={6}>
                            <Spin size={CONFIG_VALUES.SPIN_SIZE_SMALL} />
                            <Typography.Text type="secondary">思考中（预计用时15-30秒）</Typography.Text>
                        </Flex>
                    )}
                    <Elements items={message?.elements} />
                </Flex>
            </AIContainer>
        </Flex>
    );
};

export default MessageBubbleContent;
