import {useRef, useImperativeHandle, forwardRef, useCallback} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {
    RESPONSIVE_SPACING,
} from './constants';
import MessageList from './MessageList';

interface MessagePanelProps {
    show: boolean;
}

export interface MessagePanelRef {
    scrollToBottom: () => void;
}

const Container = styled.div<{ show: boolean }>`
    width: 100%;
`;

const MessagesContainer = styled.div`
    flex: 1;
    margin: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_TOP}px 0 80px;
    overflow-y: auto;
    height: 100%;

    ::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
`;

const MessagePanel = forwardRef<MessagePanelRef, MessagePanelProps>(
    ({show}, ref) => {
        const messagesEndRef = useRef<HTMLDivElement>(null);

        const scrollToBottom = useCallback(
            () => {
                messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
            },
            []
        );

        useImperativeHandle(
            ref,
            () => ({
                scrollToBottom,
            }),
            [scrollToBottom]
        );

        return (
            <Container show={show}>
                <Flex vertical style={{height: '100%'}}>
                    <MessagesContainer>
                        <MessageList />
                        <div ref={messagesEndRef} />
                    </MessagesContainer>
                </Flex>
            </Container>
        );
    }
);

MessagePanel.displayName = 'MessagePanel';

export default MessagePanel;
