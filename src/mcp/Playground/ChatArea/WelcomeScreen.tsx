import styled from '@emotion/styled';
import {Flex} from 'antd';
import {
    IconStep01,
    IconStep02,
    IconStep03,
    IconStep04,
    IconElipsis,
} from '../../../icons/mcp';
import {mcpPlaygroundStyles, stepTexts} from '../constants/styles';

const Wrapper = styled(Flex)`
    min-height: 460px;
    z-index: 1;
`;

const WelcomeTitle = styled.h1`
    font-weight: ${mcpPlaygroundStyles.typography.title.fontWeight};
    font-size: ${mcpPlaygroundStyles.typography.title.fontSize};
    line-height: ${mcpPlaygroundStyles.typography.title.lineHeight};
    color: ${mcpPlaygroundStyles.typography.title.color};
`;

const SubTitle = styled.p`
    font-weight: ${mcpPlaygroundStyles.typography.subtitle.fontWeight};
    font-size: ${mcpPlaygroundStyles.typography.subtitle.fontSize};
    line-height: ${mcpPlaygroundStyles.typography.subtitle.lineHeight};
    color: ${mcpPlaygroundStyles.typography.subtitle.color};
    margin-top: ${mcpPlaygroundStyles.typography.subtitle.marginTop};
    margin-bottom: ${mcpPlaygroundStyles.typography.subtitle.marginBottom};
`;

const StepDescription = styled.p`
    font-size: ${mcpPlaygroundStyles.typography.stepDescription.fontSize};
    color: ${mcpPlaygroundStyles.typography.stepDescription.color};
    line-height: ${mcpPlaygroundStyles.typography.stepDescription.lineHeight};
`;

const WelcomeScreen = () => {
    const stepIconStyle = mcpPlaygroundStyles.icons.stepIconSize;
    const ellipsisIconStyle = {
        fontSize: mcpPlaygroundStyles.icons.ellipsisIconSize,
    };
    const stepIcons = [IconStep01, IconStep02, IconStep03, IconStep04];

    return (
        <Wrapper
            vertical
            style={{
                paddingTop: '100px',
                paddingBottom: '80px',
            }}
        >
            <WelcomeTitle>欢迎来到MCP Playground</WelcomeTitle>
            <SubTitle>简单四步 开始体验海量MCP工具</SubTitle>
            {/* 文字内容超出的长度在这里搞定， paddingRight 用来控制文字不超出 */}
            <Flex justify="space-between" align="center" style={{paddingRight: 92}}>
                {stepIcons.map((StepIcon, index) => {
                    const isLast = index === stepIcons.length - 1;
                    return (
                        <>
                            <div style={{position: 'relative'}}>
                                <StepIcon style={stepIconStyle} />
                                <StepDescription
                                    key={index}
                                    style={{
                                        position: 'absolute',
                                        top: 84,
                                        width: 168,
                                    }}
                                >
                                    {stepTexts[index]}
                                </StepDescription>
                            </div>

                            {!isLast && (
                                <IconElipsis
                                    style={ellipsisIconStyle}
                                />
                            )}
                        </>
                    );
                })}
            </Flex>
        </Wrapper>
    );
};

export default WelcomeScreen;
