import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPServerListType} from './ServerListTabs';

interface GetDataSourceParams {
    onlySeleted: boolean;
    tabsKey: MCPServerListType;
    squareServers: MCPServerBase[];
    exampleServers: MCPServerBase[];
    selectedRowKeys: number[];
}
export const getDataSource = ({
    onlySeleted,
    tabsKey,
    squareServers,
    exampleServers,
    selectedRowKeys,
}: GetDataSourceParams) => {
    if (onlySeleted) {
        if (tabsKey === 'square') {
            return squareServers.filter((item: MCPServerBase) => {
                return selectedRowKeys.includes(item.id);
            });
        }
        return exampleServers?.filter((item: MCPServerBase) => {
            return selectedRowKeys.includes(item.id);
        });
    }
    if (tabsKey === 'square') {
        return squareServers;
    }
    return exampleServers;
};


export const generateServerListApiParams = ({keywords, labels}: {keywords?: string, labels: number[]}) => {
    return {
        keywords,
        platformType: 'hub' as const,
        serverProtocolType: 'SSE',
        labels: labels?.join(','),
        pn: 1,
        size: 1000, // 接口层不支持批量查询server，所以这里走前端分页吧
    };
};
