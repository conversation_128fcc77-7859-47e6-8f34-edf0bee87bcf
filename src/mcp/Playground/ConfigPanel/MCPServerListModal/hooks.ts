import {useEffect} from 'react';
import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPServerListType} from './ServerListTabs';
import {setMCPServerAtom} from './region';

interface UseInitServerListParams {
    open: boolean;
    request: () => void;
    requestExamples: () => void;
    initialValue: number[];
    type?: MCPServerListType;
    servers?: MCPServerBase[];
    exampleServers: MCPServerBase[];
    setSelectedRowKeys: (value: number[]) => void;
}
/* 加载组件时初始化表格数据和选中值 */
export const useInitServerList = ({
    open,
    request,
    servers,
    requestExamples,
    initialValue,
    setSelectedRowKeys,
    type,
    exampleServers,
}: UseInitServerListParams) => {
    useEffect(
        () => {
            if (open) {
                request();
                requestExamples();
            }
        },
        [open, request, requestExamples]
    );
    useEffect(
        () => {
            if (open) {
                setSelectedRowKeys(initialValue);
            }
        },
        [open, initialValue, setSelectedRowKeys]
    );
    useEffect(
        () => {
            if (type === 'example' && open) {
                setSelectedRowKeys(exampleServers?.map((item: MCPServerBase) => item.id));
            }
        },
        [open, type, exampleServers, setSelectedRowKeys]
    );

    useEffect(
        () => {
            if (servers) {
                servers?.forEach(
                    server => setMCPServerAtom(server.id, server)
                );
            }
        },
        [servers]
    );

    useEffect(
        () => {
            if (exampleServers) {
                exampleServers?.forEach(
                    server => setMCPServerAtom(server.id, server)
                );
            }
        },
        [exampleServers]
    );
};
