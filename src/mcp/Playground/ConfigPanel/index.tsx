import styled from '@emotion/styled';
import {Flex, Form, FormInstance, Typography} from 'antd';
import {css} from '@emotion/css';
import {createContext, useContext, useEffect, useRef} from 'react';
import {useSearchParams, useSearchParamsUpdate} from '@panda-design/router';
import {message} from '@panda-design/components';
import {isNil} from 'lodash';
import {apiGetPlaygroundConfig, apiPostPlaygroundConfig, PlaygroundConfig} from '@/api/mcp/playground';
import MCPServerListField from './MCPServerListField';
import SeverConfigButton from './SeverConfigButton';
import SystemPromptField from './SystemPromptField';
import ModelField from './ModelField';
import {MCPPlaygroundFormValues} from './types';
import {getMergedServerListFieldValues, sanitizePlaygroundConfig, transformFormValuesToPlaygroundConfig} from './utils';

const ConfigPanelContext = createContext({config: {openServerConfigId: null}});

export const useConfigPanelContext = () => {
    return useContext(ConfigPanelContext);
};

const formCss = css`
    .ant-5-form-item-label > label {
        width: 100%;
    }
`;

const StyledFlex = styled(Flex)`
   height: 100%;
   overflow: hidden;
`;

const Body = styled.div`
    overflow: auto;
    flex: 1;
    padding: 0 24px;
`;

const Footer = styled.div`
    flex: 0;
    border-top: 1px solid #e8e8e8;
    padding: 8px 24px;
`;

export default function ConfigPanel({form}: {form: FormInstance}) {
    const {serverId: serverIdStr} = useSearchParams();
    const updateSearchParams = useSearchParamsUpdate();
    const isInitialled = useRef(false);
    const serverId = serverIdStr ? Number(serverIdStr) : undefined;
    const configRef = useRef({openServerConfigId: null});
    useEffect(
        () => {
            if (!isInitialled.current) {
                const init = async () => {
                    const config: Partial<PlaygroundConfig> = await apiGetPlaygroundConfig() || {};
                    if (serverId) {
                    // 如果是Server试用，只用这一个Server的配置
                        const mergedMcpServers = await getMergedServerListFieldValues(
                            [],
                            [serverId]
                        );
                        config.mcpServers = mergedMcpServers;
                        // 仅取第一项是因为这里肯定只有一个server。
                        const shouldWriteServerConfig =
                        mergedMcpServers[0].serverParams.find(param => param.required && isNil(param.value));
                        if (shouldWriteServerConfig) {
                            configRef.current.openServerConfigId = serverId;
                        }
                    }
                    const formValues = sanitizePlaygroundConfig(config);
                    form.setFieldsValue(formValues);
                    if (serverId) {
                        form.submit();
                    }

                    isInitialled.current = true;

                    updateSearchParams({serverId: undefined});
                };
                init();
            }
        },
        [form, serverId, configRef, updateSearchParams]
    );
    const handleSubmit = async (values: MCPPlaygroundFormValues) => {
        try {
            await apiPostPlaygroundConfig(transformFormValuesToPlaygroundConfig(values));
        }
        catch (error) {
            message.error('保存配置失败');
        }
    };

    const handleValuesChange = async (changedValues: any, allValues: MCPPlaygroundFormValues) => {
        if (changedValues.systemPrompt === undefined) {
            await handleSubmit(allValues);
        }
    };

    return (
        <ConfigPanelContext.Provider value={{config: configRef.current}}>
            <StyledFlex vertical>
                <Typography.Title level={4} style={{padding: '16px 24px 24px'}}>
                    自定义配置
                </Typography.Title>
                <Body>
                    <Form
                        form={form}
                        layout="vertical"
                        onValuesChange={handleValuesChange}
                        className={formCss}
                        onFinish={handleSubmit}
                    >
                        <ModelField />
                        <SystemPromptField />
                        <MCPServerListField />
                    </Form>
                </Body>
                <Footer>
                    <SeverConfigButton />
                </Footer>
            </StyledFlex>
        </ConfigPanelContext.Provider>
    );
}
