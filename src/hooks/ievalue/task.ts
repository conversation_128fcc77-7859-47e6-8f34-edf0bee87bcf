/* eslint-disable max-statements */
/* eslint-disable max-lines */
import {useFieldValue, useFormContext} from '@panda-design/path-form';
import dayjs from 'dayjs';
import {
    useActionPending,
    useDebouncedEffect,
    useDebouncedValue,
    useInputValue,
    useLocalStorage,
    useRequest,
    useRequestCallback,
} from 'huse';
import {head, isEqual, uniqBy} from 'lodash';
import {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import {useParams} from 'react-router-dom';
import {useResource} from 'react-suspense-boundary';
import {
    apiCaseEvaluateList,
    apiCaseHistoryList,
    apiChatGetStatus,
    apiChatLinkRecordList,
    apiChatRecordList,
    apiEvaluateCaseListColumn,
    apiEvaluateRecordFeatureListByRecordID,
    apiEvaluateRecordScoreHistory,
    apiGroupCaseFeatureList,
    apiGroupCaseInfo,
    apiGroupCaseList,
    apiLinkRecordList,
    apiStrategyList,
    apiStrategyTaskList,
    ChatRecordItem,
    ChatRecordListParams,
    ChatStatusItem,
    ChatStepRecordListParams,
    LinkRecordListParams,
} from '@/api/ievalue/case';
import {apiEvaluateFileList} from '@/api/ievalue/dataset';
import {apiEvaluatePolicyInfo} from '@/api/ievalue/model';
import {
    apiMonitorPlanBaselines,
    apiMonitorPlanTasks,
    apiMonitorPlanTemplates,
    PlanTemplatesParams,
} from '@/api/ievalue/monitor';
import {
    apiGetPromptExcuteResult,
    PromptExcuteResultListItem,
} from '@/api/ievalue/prompt-version';
import {
    apiBatchInferenceTasksPost,
    apiDelineateWordTagList,
    apiGetRecordModifyHistory,
    apiGetShareList,
    apiGroupCaseStageTimeInitialize,
    apiGroupFeatureUserList,
    apiIcafeSpaceList,
    apiMultiDelineateWordTagList,
    apiOfflineTaskInfo,
    apiOfflineTaskList,
    apiPredictChatRecordNum,
    apiStageNameList,
    apiTaskCaseListGet,
    apiTaskCaseStatsGet,
    apiTaskCaseUsernamesGet,
    apiTaskEvaluatorQueueList,
    apiTaskGroupListGet,
    apiTaskList,
    apiTaskMapHasFunctionFieldsPost,
    apiTaskModelDisplayList,
    apiTaskModelList,
    apiTaskModelLogInfo,
    apiTaskPieChart,
    apiTaskPromptVersionList,
    apiTaskScoreListGet,
    apiTaskStageIsBlind,
    apiTaskTemplateList,
    BatchInferenceTaskParams,
    CustomSelectColumnItem,
    DelineateWordItem,
    MultiDelineateWordItem,
    TaskListParams,
    TaskModelItem,
    TaskPieChartParams,
    TaskTemplate,
    TaskTemplateListParams,
    TaskTemplateParams,
} from '@/api/ievalue/task';
import {CaseStageStatusEnum, ChatStatusEnum} from '@/constants/ievalue/case';
import {
    CaseSelectValueEnum,
    CaseSelectValueMap,
} from '@/constants/ievalue/evaluate';
import {
    TaskPredictRoundEnum,
    TaskStageEnum,
    UserUnAssignedObj,
} from '@/constants/ievalue/task';
import {useCurrentUser} from '@/regions/user/currentUser';
import {
    apiReordCardList,
    apiTaskGroupInfo,
    apiTaskInfo,
    apiTaskStageList,
} from '@/api/ievalue/task';
import {LayoutOptionEnum} from '@/constants/ievalue/taskDetail';
import {apiStrategyTagList} from '@/api/ievalue/tags';
import {resetSelectedRows} from '@/regions/ievalue/task/selectedRows';
import {convertTemplateOption} from '@/utils/ievalue/task';
import {usePagination, usePaginationSearchObj} from './dashboard';
import {useEvaluateEvaluatorList, useSpaceUserList} from './settings';
import {useSpaceCode, useSpaceCodeSafe} from './spacePage';

export function useIsAdmin() {
    const [allAdminList] = useSpaceUserList('admin');
    const user = useCurrentUser();
    const isAdmin = useMemo(
        () => {
            return allAdminList?.userList?.some(
                (item: any) => item.username === user.username
            );
        },
        [allAdminList, user.username]
    );
    return isAdmin;
}

export function useIsDeveloper() {
    const [list] = useSpaceUserList('editor');
    const user = useCurrentUser();
    const isDeveloper = useMemo(
        () => {
            return list?.userList?.some(
                (item: any) => item.username === user.username
            );
        },
        [list, user.username]
    );
    return isDeveloper;
}

const formatUserData = (data: string[]) => {
    return data
        ?.map(item => {
            return item === UserUnAssignedObj.label
                ? UserUnAssignedObj.value
                : item;
        })
        ?.join(',');
};

interface UseTaskListParams {
    pn: number;
    size: number;
    setPn: (pn: number) => void;
}

export function useTaskList({pn, size, setPn}: UseTaskListParams) {
    const dirID = useFieldValue('dirID');
    const privateCount = useFieldValue('_count');
    const {getValues} = useFormContext();
    const userInfo = useCurrentUser();
    const [values, setValues] = useState<TaskListParams>({});

    useEffect(
        () => {
            setPn(1);
        },
        [privateCount, setPn]
    );

    useEffect(
        () => {
            resetSelectedRows();
        },
        [dirID, privateCount]
    );

    useEffect(
        // eslint-disable-next-line complexity
        () => {
            const formData = getValues();
            const tempValue: TaskListParams = {};
            if (formData.name) {
                tempValue.name = formData.name;
            }
            if (formData.promptIDs?.length) {
                tempValue.promptIDs = formData.promptIDs.join(',');
            }
            if (formData.stageList?.length) {
                tempValue.stageList = formData.stageList.join(',');
            }
            if (formData.tagIDs?.length) {
                tempValue.tagIDs = formData.tagIDs.join(',');
            }
            if (formData.stageTemplateList) {
                tempValue.stageTemplateList = formData.stageTemplateList;
            }
            if (formData.reviewer?.length) {
                tempValue.reviewer = formatUserData(formData.reviewer);
            }
            if (formData.auditer?.length) {
                tempValue.auditer = formatUserData(formData.auditer);
            }
            if (formData.stageStatus) {
                tempValue.stageStatus = formData.stageStatus;
            }
            if (formData.ownerChecked) {
                tempValue.owner = userInfo.username;
            }
            if (formData.creatorChecked) {
                tempValue.creator = userInfo.username;
            }
            if (formData.timeRange) {
                tempValue.createTimeStart = dayjs(formData.timeRange[0]).format(
                    'YYYY-MM-DD HH:mm:ss'
                );
                tempValue.createTimeEnd = dayjs(formData.timeRange[1]).format(
                    'YYYY-MM-DD HH:mm:ss'
                );
            }
            if (formData.target) {
                tempValue.target = formData.target;
            }
            if (formData.models) {
                tempValue.models = formData.models;
            }
            if (formData.evaluateMode) {
                tempValue.evaluateMode = formData.evaluateMode;
            }
            if (formData.showMethod) {
                tempValue.showMethod = formData.showMethod;
            }
            if (formData.predictRound) {
                tempValue.predictRound = formData.predictRound;
            }
            if (formData.policyID) {
                tempValue.policyID = formData.policyID;
            }
            tempValue.pn = pn;
            tempValue.size = size;
            tempValue.dirID = formData.dirID;
            tempValue.withUserAuth = 1;
            setValues(tempValue);
        },
        [privateCount, pn, size, userInfo.username, getValues, dirID]
    );
    return values;
}

export function useTaskGroupFeatureID() {
    const {groupFeatureID} = useParams();
    if (!groupFeatureID) {
        throw new Error('feature group ID is required');
    }
    return +groupFeatureID;
}

export function useTaskCaseFeatureID() {
    const {caseFeatureID} = useParams();
    if (!caseFeatureID) {
        throw new Error('feature case ID is required');
    }
    return +caseFeatureID;
}

export function useTaskTaskID() {
    const {taskID} = useParams();
    if (!taskID) {
        throw new Error('Task ID is required');
    }
    return +taskID;
}

export function useTaskStageID() {
    const {stageID} = useParams();
    if (!stageID) {
        throw new Error('Stage ID is required');
    }
    return +stageID;
}
export function useTarget() {
    const {target} = useParams();
    if (!target) {
        return 'LLM';
    }
    return target;
}

export function useTaskTemplateID() {
    const {templateID} = useParams();
    if (!templateID) {
        throw new Error('templateID is required');
    }
    return +templateID;
}

export function useTaskTemplateList(params?: TaskTemplateListParams) {
    const paramTarget = useTarget();
    const spaceCode = useSpaceCode();
    return useResource(apiTaskTemplateList, {
        target: params?.target || paramTarget,
        stageType: params?.stageType,
        spaceCode: params?.spaceCode || spaceCode,
    });
}

export function useAllTemplateSelectList() {
    const [templateList, {refresh}] = useResource(apiTaskTemplateList, {});
    const resultList = templateList?.map(convertTemplateOption) || [];
    return [resultList, refresh] as const;
}

export function useTaskTemplateData(
    templateIDTrans?: number,
    spaceCode?: string
) {
    const [templateList] = useTaskTemplateList({spaceCode});
    const {templateID} = useParams();
    const searchTemplateID = templateIDTrans ?? templateID;
    const templateData = templateList?.find(
        (item: TaskTemplate) => item.templateID === Number(searchTemplateID)
    );
    return templateData;
}

export function useTaskTemplateStageTypeList(templateIDTrans?: number) {
    const templateData = useTaskTemplateData(templateIDTrans);
    const stageTypes =
        templateData?.stages?.map(item => item.stageType) ?? [];
    return stageTypes;
}

export function useTaskTemplateName(templateIDTrans?: number) {
    const templateData = useTaskTemplateData(templateIDTrans);
    return templateData?.name ?? '';
}

export function useTaskTemplateStageType(templateIDTrans?: number) {
    const stageTypes = useTaskTemplateStageTypeList(templateIDTrans);
    return stageTypes.join(',');
}

export function useTaskRealTemplateID(templateIDTrans?: any) {
    const {templateID} = useParams();
    return templateID ?? templateIDTrans;
}

export function useTaskModelList() {
    const taskID = useTaskTaskID();
    return useResource(apiTaskModelList, {taskID});
}

export function useLocalStorageEvaluateLayout() {
    return useLocalStorage<any>(
        'IEVALUE/Task/Evaluate/Layout',
        LayoutOptionEnum.Vertical
    );
}

export function useLocalStorageDisplayList() {
    return useLocalStorage<any>('IEVALUE/Task/Evaluate/Display', []);
}

export function useLocalStorageChatDisplayList() {
    return useLocalStorage<any>('IEVALUE/Task/Evaluate/Chat/Display', []);
}
export function useLocalStorageAnswerCardSelect() {
    return useLocalStorage<CaseSelectValueEnum>(
        'IEVALUE/Task/Evaluate/AnswerCard/Select',
        CaseSelectValueEnum.ALL
    );
}

export function useLocalStorageCustomList() {
    const userInfo = useCurrentUser();
    const spaceCode = useSpaceCodeSafe();
    const taskID = useTaskTaskID();
    const uniqStr = `${spaceCode}-${taskID}`;
    const [allCustomSelectList, setAllCustomSelectList] = useLocalStorage<
        CustomSelectColumnItem[]
    >(`IEVALUE/Task/Evaluate/CustomList/${userInfo.username}`, []);

    const [allCustomList, setCustomList] = useState(allCustomSelectList);

    useEffect(
        () => {
            setAllCustomSelectList(allCustomList);
        },
        [allCustomList, setAllCustomSelectList]
    );

    const currentData = useMemo(
        () => {
            return (
                allCustomList?.find(item => item.uniqStr === uniqStr) || {
                    uniqStr,
                    list: [],
                    groupList: [],
                    groupName: '展示全部',
                }
            );
        },
        [allCustomList, uniqStr]
    );

    // 本地最多存10个任务得自定义列表
    const setCustomData = useCallback(
        (data: Partial<CustomSelectColumnItem>) => {
            setCustomList(pre => {
                const currentData = pre?.find(
                    item => item.uniqStr === uniqStr
                ) || {
                    uniqStr,
                    list: [],
                    groupList: [],
                    groupName: '展示全部',
                };
                const defaultData = {...currentData, ...data};
                if (
                    !defaultData.groupList.find(
                        item => item.name === defaultData.groupName
                    )
                ) {
                    defaultData.groupName = '展示全部';
                }
                const result = [defaultData, ...pre];
                const uniqResult = uniqBy(result, 'uniqStr');
                const resultList = uniqResult?.slice(0, 10);
                return resultList;
            });
        },
        [uniqStr]
    );

    return {
        customSelectList: currentData.list,
        customGroupList: currentData.groupList,
        groupName: currentData.groupName,
        setCustomData,
    } as const;
}

export function useSubTaskList({
    pn,
    size,
    status,
    stageID,
    name,
    userName,
}: {
    stageID?: number;
    userName?: string;
    status?: string;
    name?: string;
    pn: number;
    size: number;
}) {
    const taskID = useTaskTaskID();
    return useResource(apiTaskGroupListGet, {
        pn,
        size,
        taskID,
        status,
        stageID,
        userName,
        name,
    });
}

export function useTaskInfo() {
    const taskID = useTaskTaskID();
    const [taskInfo, {refresh}] = useResource(apiTaskInfo, {taskID});
    return [taskInfo, refresh] as const;
}

export function useModelOptions() {
    const [taskInfo] = useTaskInfo();
    return (
        taskInfo?.modelParams?.map(({modelName}) => ({
            label: modelName,
            value: modelName,
            tagName: modelName,
        })) ?? []
    );
}

export function useTagOptions() {
    const [taskInfo] = useTaskInfo();
    const spacePolicyID = taskInfo?.spacePolicyID;
    const data = useResource(apiEvaluatePolicyInfo, {ID: spacePolicyID});
    return data?.[0]?.tags?.map((tag: any) => ({
        label: tag,
        value: tag,
        tagName: tag,
    }));
}

export function useReordCardList() {
    const taskID = useTaskTaskID();
    return useResource(apiReordCardList, {taskID});
}

export function useTaskModelLogInfo() {
    const taskID = useTaskTaskID();
    return useResource(apiTaskModelLogInfo, {taskID});
}

export function useTaskStageList() {
    const taskID = useTaskTaskID();
    return useResource(apiTaskStageList, {taskID});
}

export enum SearchTypeEnum {
    SUBTASK = 'SUBTASK',
    MANAGER = 'MANAGER',
}

export function useSearchSubTaskList() {
    const searchInputProps = useInputValue('');
    const searchValue = useDebouncedValue(searchInputProps.value, 300);
    const [stageName, setStageName] = useState('');
    const [status, setStatus] = useState('');
    const [searchType, setSearchType] = useState(SearchTypeEnum.SUBTASK);
    const {pn, size, setSize, setPn} = usePagination();
    const [spaceListPagination, {refresh}] = useSubTaskList({
        pn,
        size,
        status,
        userName:
            searchType === SearchTypeEnum.SUBTASK ? undefined : searchValue,
        name: searchType === SearchTypeEnum.SUBTASK ? searchValue : undefined,
    });
    useEffect(
        () => {
            refresh();
        },
        // 只调一次
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return {
        dataSource: spaceListPagination.list,
        searchInputProps,
        status,
        stageName,
        setStatus,
        setStageName,
        searchType,
        setSearchType,
        pagination: {
            current: pn,
            pageSize: size,
            showSizeChanger: true,
            total: spaceListPagination.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
        },
        refresh,
    };
}
export function useTaskGroupInfo(groupID: number) {
    const taskID = useTaskTaskID();
    return useRequestCallback(apiTaskGroupInfo, {taskID, groupID: groupID});
}

export function useGroupID() {
    const {groupID, groupFeatureID} = useParams();
    if (!groupID && !groupFeatureID) {
        throw new Error('group ID is required');
    }
    return +(groupID || groupFeatureID);
}

export function useGroupInfo() {
    const groupID = useGroupID();
    const taskID = useTaskTaskID();
    return useResource(apiTaskGroupInfo, {taskID, groupID});
}

export function useGroupStageInfo() {
    const [groupInfo, {refresh: groupInfoRefresh}] = useGroupInfo();
    const stageInfo = groupInfo.caseStageNum.find(
        item => item.stageID === groupInfo.stageID
    );

    useEffect(
        () => {
            groupInfoRefresh?.();
        },
        // 只加载时刷新一次
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    return [stageInfo, groupInfoRefresh, groupInfo] as const;
}

export function useGroupCaseID() {
    const {caseID, caseFeatureID} = useParams();
    if (!caseID && !caseFeatureID) {
        throw new Error('case ID is required');
    }
    return +(caseID || caseFeatureID);
}

export function useCaseHistoryList() {
    const caseID = useGroupCaseID();
    const [caseHistoryList, {refresh: caseHistoryListRefresh}] = useResource(
        apiCaseHistoryList,
        {
            caseID,
        }
    );
    return [caseHistoryList.list, caseHistoryListRefresh] as const;
}

export function useGroupCaseList(pn: number = 1, size: number = 1000) {
    const groupID = useGroupID();
    const [groupCaseResponse, {refresh: groupCaseListRefresh}] = useResource(
        apiGroupCaseList,
        {
            groupID,
            pn,
            size,
        }
    );

    useEffect(
        () => {
            groupCaseListRefresh();
        },
        // 保证每次变化都会调用一次
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return {
        groupCaseListRefresh,
        groupCaseList: groupCaseResponse.list,
        groupCaseTotal: groupCaseResponse.total,
    } as const;
}

interface GroupCaseListPaginationParams {
    pass?: number;
    feedbacks?: string[];
    stageStatus?: CaseStageStatusEnum;
    caseStatus?: CaseSelectValueEnum;
}

export function useGroupCaseListPagination(
    params?: GroupCaseListPaginationParams
) {
    const {pn, size, setPn, setSize, searchObj, setSearchObj} =
        usePaginationSearchObj({
            pass: params?.pass,
            feedbacks: params?.feedbacks,
            stageStatus: params?.stageStatus,
            caseStatus: params?.caseStatus,
        });
    const groupID = useGroupID();
    const setFeedbacks = useCallback(
        (feedbacks: string[]) => {
            setSearchObj((pre: any) => ({...pre, feedbacks}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const setCaseStatus = useCallback(
        (caseStatus: CaseSelectValueEnum) => {
            setSearchObj((pre: any) => ({...pre, caseStatus}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const setStageStatus = useCallback(
        (stageStatus: CaseStageStatusEnum) => {
            setSearchObj((pre: any) => ({...pre, stageStatus}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const status = useMemo(
        () => {
            const caseStatus = searchObj?.caseStatus as CaseSelectValueEnum;
            if (caseStatus) {
                return CaseSelectValueMap?.[caseStatus]?.join(',');
            } else {
                return undefined;
            }
        },
        [searchObj?.caseStatus]
    );

    const {refresh, data, pending} = useRequest(apiGroupCaseList, {
        groupID,
        pn,
        size,
        status,
        pass: searchObj?.pass,
        stageStatus: searchObj?.stageStatus,
        feedbacks:
            searchObj?.feedbacks?.length > 0
                ? searchObj?.feedbacks?.join(',')
                : undefined,
    });
    return {
        dataSource: data?.list ?? [],
        versionInfos: data?.versionInfos ?? [],
        caseStatus: searchObj?.caseStatus,
        setCaseStatus,
        setFeedbacks,
        feedbacks: searchObj?.feedbacks ?? [],
        stageStatus: searchObj?.stageStatus,
        setStageStatus,
        setSearchObj,
        pagination: {
            current: pn,
            pageSize: size,
            showSizeChanger: true,
            total: data?.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
            pageSizeOptions: ['10', '20', '50', '100', '200', '500'],
        },
        pending,
        refresh,
    };
}

export function useRequestCaseEvaluateList() {
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    const {stageID} = useParams();
    return useResource(apiCaseEvaluateList, {
        caseID,
        taskID,
        stageID: Number(stageID),
    });
}

export function useCaseEvaluateList() {
    const caseID = useGroupCaseID();
    const [caseEvaluateResponse, {refresh: caseEvaluateRefresh}] =
        useRequestCaseEvaluateList();
    useEffect(
        () => {
            caseEvaluateRefresh();
        },
        // 保证每次变化都会调用一次
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [caseID]
    );
    return {
        caseEvaluateRecords: caseEvaluateResponse.records ?? [],
        caseEvaluateRankMap: caseEvaluateResponse.rankMap,
        caseEvaluateRefresh,
    } as const;
}

export function useStrategyList() {
    const spaceCode = useSpaceCodeSafe();
    return useResource(apiStrategyList, {
        spaceCode,
    });
}

export function useStrategyTaskList() {
    const taskID = useTaskTaskID();
    return useResource(apiStrategyTaskList, {
        taskID,
    });
}

export function useTaskStrategyInfo() {
    const [strategyList] = useStrategyTaskList();
    return head(strategyList);
}

export function useStageNameList() {
    const taskID = useTaskTaskID();
    const [stageNameList, {refresh: stageNameListRefresh}] = useResource(
        apiStageNameList,
        {
            taskID,
        }
    );
    return [stageNameList.list, stageNameListRefresh] as const;
}

export function useShareList() {
    const taskID = useTaskTaskID();
    const groupID = useGroupID();
    const [shareList, {refresh: shareListRefresh}] = useResource(
        apiGetShareList,
        {
            taskID,
            shareType: 'GROUP',
            groupID,
        }
    );
    return [shareList, shareListRefresh] as const;
}

export function useEvaluateTagList(policyID?: number) {
    const spaceCode = useSpaceCodeSafe();
    return useResource(apiStrategyTagList, {
        spaceCode,
        policyID: policyID ?? 0,
    });
}

export function useEvaluateDatasetFileList() {
    const taskID = useTaskTaskID();
    return useResource(apiEvaluateFileList, {
        taskID,
    });
}

export function useEvaluateRecordScoreHistory(recordID: number) {
    const taskID = useTaskTaskID();
    return useResource(apiEvaluateRecordScoreHistory, {
        taskID,
        recordID: recordID,
    });
}

export function useOfflineTaskInfo() {
    const taskID = useTaskTaskID();
    return useResource(apiOfflineTaskInfo, {
        taskID,
    });
}

export function useOfflineTaskList({pn, size}: {pn: number, size: number}) {
    const taskID = useTaskTaskID();
    return useResource(apiOfflineTaskList, {
        pn,
        size,
        taskID,
    });
}

export function useSearchPromptTaskList() {
    const {pn, size, setSize, setPn} = usePagination();
    const [listPagination, {refresh}] = useOfflineTaskList({
        pn,
        size,
    });

    return {
        dataSource: listPagination.list,
        total: listPagination.total,
        finish: listPagination.finish,
        pagination: {
            current: pn,
            pageSize: size,
            showSizeChanger: true,
            total: listPagination.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
        },
        refresh,
    };
}

export function useGroupCaseInfo() {
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    return useResource(apiGroupCaseInfo, {
        taskID,
        caseID,
    });
}

export function useGetPredictRecordList() {
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    return useResource(apiGetPromptExcuteResult, {
        caseID,
        taskID,
    });
}

export function useGetPromptExcute() {
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    const resuce = useResource(apiGetPromptExcuteResult, {
        caseID,
        taskID,
    });
    const [_, {refresh}] = resuce;
    useEffect(
        () => {
            refresh();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [caseID]
    );

    return resuce;
}

interface IChatStatus {
    status: ChatStatusEnum;
    predictRecordID?: number;
    callback?: (chatTaskID: string) => void;
}

export const GroupChatContext = createContext<{
    statusData: ChatStatusItem[] | undefined;
    refs: any;
}>({statusData: [], refs: {}});
export function useGroupChatContext() {
    return useContext(GroupChatContext);
}
export const GroupPanelContext = createContext<{
    checksRef: any;
    getChats: () => any[];
    open: boolean;
        }>({checksRef: {}, getChats: () => [], open: false});
export function useGroupPanekContext() {
    return useContext(GroupPanelContext);
}
export function useChatState(modelID: number) {
    const ref = useRef<ChatStatusItem>();
    const {statusData} = useGroupChatContext();
    const statusItem = useMemo(
        () => {
            return statusData?.find((e: ChatStatusItem) => e.modelID === modelID);
        },
        [modelID, statusData]
    );
    ref.current = statusItem;
    return {statusItem, getStatusItem: () => ref.current};
}
export const useWatchStatus = (modelID: number) => {
    const tagRef = useRef<IChatStatus>();
    const clearPolling = () => {
        tagRef.current = undefined;
    };
    const {statusItem} = useChatState(modelID);
    useEffect(
        () => {
            if (
                tagRef.current
                && statusItem
                && statusItem.status !== tagRef.current.status
            ) {
                tagRef.current?.callback?.(statusItem.chatTaskID);
                clearPolling();
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [statusItem?.status]
    );

    const startPolling = (props: IChatStatus) => {
        tagRef.current = props;
    };

    return {
        startPolling,
        clearPolling,
    };
};
export const useWatchStatusList = () => {
    const tagRef = useRef<any>();
    const {statusData} = useGroupChatContext();
    const clearPolling = () => {
        tagRef.current = undefined;
    };
    useEffect(
        () => {
            if (tagRef.current?.status) {
                const idx = statusData?.findIndex(
                    item => item.status === tagRef.current?.status
                );
                if (idx === -1) {
                    tagRef.current?.callback?.();
                    clearPolling();
                }
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [statusData]
    );

    const startPolling = (props: IChatStatus) => {
        tagRef.current = props;
    };

    return {
        startPolling,
        clearPolling,
    };
};
export const useProviderGetStatus = () => {
    const intervalRef = useRef<any>();
    const [statusData, setStatusData] = useState<ChatStatusItem[]>();
    const clearPolling = () => {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
    };
    const getStatus = async (caseID: number) => {
        const data = await apiChatGetStatus({caseID});
        setStatusData(pre => {
            return isEqual(pre, data) ? pre : data;
        });
    };

    const startPolling = (caseID: number) => {
        if (intervalRef.current) {
            clearPolling();
        } else {
            intervalRef.current = setInterval(() => {
                getStatus(caseID);
            }, 1000);
        }
    };

    return {
        statusData,
        setStatusData,
        startPolling,
        clearPolling,
    };
};

export const useGetChats = () => {
    const chatsRef = useRef<any>();
    const [list, {refresh}] = useGetPromptExcute();
    const {caseEvaluateRecords} = useCaseEvaluateList();
    const chats = useMemo(
        () => {
            const keys: any[] = Object.keys(list);
            if (keys.length > 0) {
                const headList = list[keys[0]];
                // 根据records 对chatslist进行排序
                const sortList: PromptExcuteResultListItem[] = [];
                caseEvaluateRecords.forEach(e => {
                    const item = headList?.find(
                        item => item.ID === e.origin.predictRecordID
                    );
                    if (item) {
                        sortList.push(item);
                    }
                });
                chatsRef.current = sortList;
                return sortList;
            }
            chatsRef.current = [];
            return [];
        },
        [caseEvaluateRecords, list]
    );

    const getChats = () => {
        return chatsRef.current;
    };
    return {chats, getChats, refresh};
};

export function useChatRecordList({
    predictRecordID,
    chatTaskID,
}: {
    predictRecordID?: number;
    chatTaskID?: string;
    chatRecordID?: number;
}) {
    const {checksRef} = useGroupPanekContext();
    const [data, setData] = useState<ChatRecordItem[]>();
    const [chatRecordList, pendingCount] = useActionPending(apiChatRecordList);
    const [strategyList] = useStrategyTaskList();
    const {getChats} = useGroupPanekContext();
    const strategyMetricList = useMemo(
        () => {
            return head(strategyList)?.metric ?? [];
        },
        [strategyList]
    );
    useEffect(
        () => {
            return () => {
                checksRef.current = {};
            };
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const onRefresh = async (chatTaskID?: string) => {
        if (chatTaskID) {
            const chatTaskIds: string[] = Object.keys(checksRef.current);
            const newChatTaskId: any = {};
            const chats = getChats();
            chatTaskIds.forEach((key: any) => {
                if (chats.find(item => item.chatTaskID === key)) {
                    newChatTaskId[key] = checksRef.current?.[key];
                }
            });
            checksRef.current = newChatTaskId;
            const res = await chatRecordList({
                predictRecordID,
                chatTaskID,
            });

            const newData = res?.map(item => {
                const scoreUnChecked =
                    item.score?.length !== strategyMetricList.length;
                if (item.score === null) {
                    item.score = strategyMetricList.map(_ => ({
                        desc: '',
                        score: undefined,
                        metric: null,
                        scoreName: null,
                    }));
                }
                return {
                    ...item,
                    scoreUnChecked:
                        scoreUnChecked
                        || item.score.some((t: any) => t.score === null),
                };
            });
            if (newData) {
                checksRef.current = {
                    ...checksRef.current,
                    [chatTaskID]: !newData?.some((t: any) => t.scoreUnChecked),
                };
            }
            setData(newData);
        }
    };
    useEffect(
        () => {
            onRefresh(chatTaskID);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [chatTaskID]
    );
    return {data, refresh: onRefresh, pending: !!pendingCount};
}

export interface CachListParam {
    predictRecordID?: number;
    chatTaskID?: string;
    list?: ChatRecordItem[];
}

export function useCacheChatRecordList() {
    const dataRef = useRef<any>({});
    const [list, setStateList] = useState<any>();
    const getList = ({predictRecordID, chatTaskID}: CachListParam) => {
        if (!dataRef.current) {
            return [];
        }
        return dataRef.current[`${predictRecordID}_${chatTaskID}`] || [];
    };
    const setList = ({predictRecordID, chatTaskID, list}: CachListParam) => {
        dataRef.current = {
            ...dataRef.current,
            [`${predictRecordID}_${chatTaskID}`]: list || [],
        };
        setStateList(list);
    };
    return {
        list,
        getList,
        setList,
    };
}

export function useEvaluatorQueueList() {
    const [evaluatorList] = useEvaluateEvaluatorList();
    const [evaluatorID, setEvaluatorID] = useState(
        head(evaluatorList)?.ID ?? 1
    );
    const {
        refresh: refreshEvaluatorQueueList,
        data: evaluatorQueueList,
        pending,
    } = useRequest(apiTaskEvaluatorQueueList, {evaluatorID});
    return {
        evaluatorQueueList: evaluatorQueueList ?? [],
        refreshEvaluatorQueueList,
        pending,
        setEvaluatorID,
        evaluatorID,
        evaluatorList: evaluatorList ?? [],
    };
}

export const useFindModel = (findFun = (_: TaskModelItem) => true) => {
    const [taskModelList] = useTaskModelList();
    return taskModelList.find(findFun);
};
export function useIcafeSpaceList() {
    return useResource(apiIcafeSpaceList, {kwords: ''});
}
export const useSpaceMoveView = (selector: string, isSpace = true) => {
    const curScrollElement = useRef<any>();
    const scrollRef = useRef<any>({
        mouseDownX: 0,
        // 鼠标停止移动时的位置
        mouseMoveOver: 0,
        // 排行列表当前的X轴位置
        listX: 0,
        // 向左最大拖拽距离
        maxLeftX: 0,
    });
    const holdSpace = useRef<boolean>(false);
    const holdDown = useRef<boolean>(false);
    useDebouncedEffect(
        () => {
            const scrollElement: any = document.querySelector(selector);
            if (!scrollElement) {
                return;
            }
            scrollRef.current.maxLeftX =
                scrollElement.rankingListEl?.clientWidth
                - scrollElement.rankingListEl?.scrollWidth;
            scrollElement.addEventListener('mousedown', (e: any) => {
                if (holdSpace.current && e.button === 0) {
                    e.preventDefault();
                    scrollRef.current.mouseDownX = e.clientX;
                    scrollRef.current.mouseMoveOver = null;
                    holdDown.current = true;
                }
            });
            scrollElement.addEventListener('wheel', (e: any) => {
                if (isSpace && !holdSpace.current) {
                    return;
                }
                const left = -e.wheelDelta || e.deltaY / 2;
                scrollElement.scrollLeft = scrollElement.scrollLeft + left;
            });

            addEventListener('mouseup', () => {
                curScrollElement.current = null;
            });

            addEventListener('keydown', e => {
                holdSpace.current = e.code === 'Space';
                if (holdSpace.current) {
                    e.preventDefault();
                }
            });
            addEventListener('keyup', () => {
                holdSpace.current = false;
                holdDown.current = false;
            });
        },
        [],
        1000
    );
};
export function useTaskPromptVersionList() {
    const taskID = useTaskTaskID();
    return useResource(apiTaskPromptVersionList, {taskID});
}
export const useIsPromptTask = (taskInfo: any) => {
    const isPrompt = useMemo(
        () => {
            return taskInfo.target === 'Prompt' && taskInfo.isFast === 0;
        },
        [taskInfo.target, taskInfo.isFast]
    );
    return isPrompt;
};

export const useMultiDelineateWordTagList = (predictRecordID: number) => {
    const [data, setData] = useState<MultiDelineateWordItem[]>();
    const loadData = useCallback(
        async (callback?: (data: MultiDelineateWordItem[]) => void) => {
            const res = await apiMultiDelineateWordTagList({
                predictRecordID,
            });
            setData(res);
            callback?.(res);
        },
        [predictRecordID]
    );

    return {refresh: loadData, data};
};
const apiMultiDelineateWordTagListSilent = async (params: {
    predictRecordID: number;
}) => {
    try {
        if (!params?.predictRecordID) {
            return;
        }

        const result = await apiMultiDelineateWordTagList(params);
        return result;
    } catch (e) {
        // do nothing
    }
};
const apiDelineateWordTagListSilent = async (params: {
    predictRecordID: number;
}) => {
    try {
        if (!params?.predictRecordID) {
            return;
        }

        const result = await apiDelineateWordTagList(params);
        return result;
    } catch (e) {
        // do nothing
    }
};

export const useResourceMultiDelineateWordTagList = (
    predictRecordID: number
) => {
    const callbackRef = useRef<(data: MultiDelineateWordItem[]) => void>();
    const [data, {refresh, pending}] = useResource(
        apiMultiDelineateWordTagListSilent,
        {predictRecordID}
    );
    const refreshData = useCallback(
        async (callback?: (data: MultiDelineateWordItem[]) => void) => {
            callbackRef.current = callback;
            refresh();
        },
        [refresh]
    );
    useEffect(
        () => {
            if (!pending && callbackRef.current) {
                callbackRef.current?.(data);
            }
        },
        [data, pending]
    );

    return [data, {refresh: refreshData, pending}] as const;
};
export const useDelineateWordTagList = (predictRecordID: number) => {
    const callbackRef = useRef<(data: DelineateWordItem[]) => void>();
    const [data, {refresh, pending}] = useResource(
        apiDelineateWordTagListSilent,
        {predictRecordID}
    );
    const refreshData = useCallback(
        async (callback?: (data: DelineateWordItem[]) => void) => {
            callbackRef.current = callback;
            refresh();
        },
        [refresh]
    );
    useEffect(
        () => {
            if (!pending && callbackRef.current) {
                callbackRef.current?.(data);
            }
        },
        [data, pending]
    );

    return [data, {refresh: refreshData, pending}] as const;
};

export function useMonitorPlanTasks() {
    const spaceCode = useSpaceCodeSafe();
    return useResource(apiMonitorPlanTasks, {spaceCode});
}

export function useMonitorPlanBaselineTasks() {
    const spaceCode = useSpaceCodeSafe();
    return useResource(apiMonitorPlanTasks, {spaceCode, isFilter: 1});
}

export function useMonitorPlanTemplates(params: PlanTemplatesParams) {
    const spaceCode = useSpaceCodeSafe();
    return useResource(apiMonitorPlanTemplates, {
        ...params,
        currentSpaceCode: spaceCode,
    });
}
// 任务模板
export function useTaskTemplateListNew(
    formData: any,
    pn: number,
    size: number,
    setPn: (pn: number) => void
) {
    const userInfo = useCurrentUser();
    const [values, setValues] = useState<TaskTemplateParams>({});
    useEffect(
        () => {
            setPn(1);
        },
        [formData, setPn]
    );
    useEffect(
        () => {
            const tempValue: TaskTemplateParams = {};
            if (formData.name) {
                tempValue.name = formData.name;
            }
            if (formData.models) {
                tempValue.models = formData.models;
            }

            tempValue.pn = pn;
            tempValue.size = size;

            setValues(tempValue);
        },
        [formData, pn, size, userInfo.username]
    );
    return values;
}

export function useMyAllTaskList() {
    const spaceCode = useSpaceCodeSafe();
    const userInfo = useCurrentUser();
    const [data, {refresh: taskListRefresh}] = useResource(apiTaskList, {
        spaceCode,
        owner: userInfo.username,
        creator: userInfo.username,
    });
    useEffect(
        () => {
            taskListRefresh?.();
        },
        // 强制加载刷新
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    return [data?.list ?? [], taskListRefresh] as const;
}

export function useTaskCaseStats() {
    const taskID = useTaskTaskID();
    const [data, {refresh: taskCaseStatsRefresh}] = useResource(
        apiTaskCaseStatsGet,
        {
            taskID,
        }
    );
    useEffect(
        () => {
            taskCaseStatsRefresh?.();
        },
        // 强制加载刷新
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    return [data, taskCaseStatsRefresh] as const;
}

export function useTaskCaseListPagination(params?: any) {
    const taskID = useTaskTaskID();
    const {pn, size, setPn, setSize, searchObj, setSearchObj} =
        usePaginationSearchObj({
            stageID: params?.stageID,
            stageStatus: params?.stageStatus,
            caseStatus: params?.caseStatus,
            username: params?.username,
            feedbacks: params?.feedbacks,
            pass: params?.pass,
            sampled: params?.sampled,
        });
    const setFeedbacks = useCallback(
        (feedbacks: string[]) => {
            setSearchObj((pre: any) => ({...pre, feedbacks}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const setUsername = useCallback(
        (username: string) => {
            setSearchObj((pre: any) => ({...pre, username}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const setStageStatus = useCallback(
        (stageStatus: CaseStageStatusEnum) => {
            setSearchObj((pre: any) => ({...pre, stageStatus}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const setStageID = useCallback(
        (stageID: number) => {
            setSearchObj((pre: any) => ({...pre, stageID}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const status = useMemo(
        () => {
            const caseStatus = searchObj?.caseStatus as CaseSelectValueEnum;
            if (caseStatus) {
                return CaseSelectValueMap?.[caseStatus]?.join(',');
            } else {
                return undefined;
            }
        },
        [searchObj?.caseStatus]
    );
    const {stageStatus, stageID, username, pass, sampled} = searchObj;
    const {refresh, data, pending} = useRequest(apiTaskCaseListGet, {
        taskID,
        pn,
        size,
        stageStatus,
        stageID,
        status,
        username,
        feedbacks:
            searchObj?.feedbacks?.length > 0
                ? searchObj.feedbacks.join(',')
                : undefined,
        pass,
        sampled,
    });
    return {
        dataSource: data?.list ?? [],
        stageStatus,
        setStageStatus,
        stageID,
        setStageID,
        username,
        setUsername,
        setFeedbacks,
        setSearchObj,
        pagination: {
            current: pn,
            pageSize: size,
            showSizeChanger: true,
            total: data?.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
        },
        pending,
        refresh,
    };
}

export function useTaskAllCaseList() {
    const stageID = useTaskStageID();
    const taskID = useTaskTaskID();
    const {refresh, data, pending} = useRequest(apiTaskCaseListGet, {
        stageID,
        taskID,
        pn: 1,
        size: 10000,
    });
    return {
        dataSource: data?.list ?? [],
        pending,
        refresh,
    };
}
export function useTaskCaseUsernames() {
    const taskID = useTaskTaskID();
    const [data, {refresh}] = useResource(apiTaskCaseUsernamesGet, {
        taskID,
    });
    return [data?.list ?? [], refresh] as const;
}

export function useIsCaseDispatch() {
    const [taskInfo] = useTaskInfo();
    return !!taskInfo.dispatchType;
}

export function useIsMegAgent() {
    const [taskInfo] = useTaskInfo();
    return taskInfo?.partner === 'MEG_AGENT';
}

export function useIsMegPosterior() {
    const [taskInfo] = useTaskInfo();
    return taskInfo?.partner === 'MegPosterior';
}

export function useIsMegAgentLowQ() {
    const [taskInfo] = useTaskInfo();
    return taskInfo?.partner === 'MegAgentLowQ';
}

export function useIsChatbot() {
    const [taskInfo] = useTaskInfo();
    return taskInfo.predictRound === TaskPredictRoundEnum.MULTI;
}

export function useCaseStageType(searchStageID?: number) {
    const stageNum = useTaskStageID();
    const stageID = searchStageID ?? stageNum;
    const [TaskCaseStatsObj] = useTaskCaseStats();
    return TaskCaseStatsObj?.[stageID].stageType;
}

export function useMonitorPlanBaselines() {
    const spaceCode = useSpaceCodeSafe();
    return useResource(apiMonitorPlanBaselines, {spaceCode});
}

export function useChatLinkRecordList(params: ChatStepRecordListParams) {
    return useResource(apiChatLinkRecordList, params);
}

export function useLinkRecordList(params: LinkRecordListParams) {
    return useResource(apiLinkRecordList, params);
}

export function useTaskCaseListColumns(column: string) {
    const groupID = useGroupID();
    const [list, {refresh}] = useResource(apiEvaluateCaseListColumn, {
        groupID,
        column,
    });
    return [list, refresh] as const;
}

export function useTaskStageIsBlind(tranStageID?: number) {
    const taskID = useTaskTaskID();
    const routeStageID = useTaskStageID();
    const stageID = tranStageID ?? routeStageID;
    const [res] = useResource(apiTaskStageIsBlind, {taskID, stageID});
    return !!res?.needBlind;
}

export function useBatchInferenceTasks(params: BatchInferenceTaskParams) {
    const [res, {refresh}] = useResource(apiBatchInferenceTasksPost, params);
    return [res, refresh] as const;
}

export function useChatRecordMsgList(params: ChatRecordListParams) {
    return useResource(apiChatRecordList, params);
}

export function useGroupFeatureUserList() {
    const taskID = useTaskTaskID();
    const groupID = useGroupID();
    return useResource(apiGroupFeatureUserList, {taskID, groupID});
}

export function useGroupFeatureCaseListPagination(groupFeatureID: number) {
    const {pn, size, setPn, setSize, searchObj, setSearchObj} =
        usePaginationSearchObj({
            pass: undefined,
            tags: undefined,
            stageStatus: undefined,
            status: undefined,
        });
    const setTags = useCallback(
        (tags: string[]) => {
            setSearchObj((pre: any) => ({...pre, tags}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const setStageStatus = useCallback(
        (stageStatus: CaseStageStatusEnum) => {
            setSearchObj((pre: any) => ({...pre, stageStatus}));
            setPn(1);
        },
        [setPn, setSearchObj]
    );

    const {refresh, data, pending} = useRequest(apiGroupCaseFeatureList, {
        groupFeatureID,
        pn,
        size,
        stageStatus: searchObj?.stageStatus,
        tags:
            searchObj?.tags?.length > 0
                ? searchObj?.tags?.join(',')
                : undefined,
    });
    return {
        dataSource: data?.list ?? [],
        setTags,
        tags: searchObj?.tags ?? [],
        stageStatus: searchObj?.stageStatus,
        setStageStatus,
        setSearchObj,
        pagination: {
            current: pn,
            pageSize: size,
            showSizeChanger: true,
            total: data?.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
        },
        pending,
        refresh,
    };
}

export function useGroupFeatureCaseList(pn: number = 1, size: number = 1000) {
    const groupFeatureID = useTaskGroupFeatureID();
    const {
        refresh: groupCaseListRefresh,
        data: groupCaseResponse,
        pending,
    } = useRequest(apiGroupCaseFeatureList, {
        groupFeatureID,
        pn,
        size,
    });

    return {
        pending,
        groupCaseListRefresh,
        groupCaseList: groupCaseResponse?.list || [],
        groupCaseTotal: groupCaseResponse?.total || 0,
    } as const;
}

export function useRecordFeatureListByRecordID(predictRecordID: number) {
    const taskID = useTaskTaskID();
    const [data, {refresh: taskCaseStatsRefresh}] = useResource(
        apiEvaluateRecordFeatureListByRecordID,
        {
            taskID,
            predictRecordID,
        }
    );
    return [data?.recordFeatures ?? [], taskCaseStatsRefresh] as const;
}

export function useTaskPieChart({stageName, modelID}: TaskPieChartParams) {
    const taskID = useTaskTaskID();
    const {data: pieChartData, pending} = useRequest(apiTaskPieChart, {
        taskID,
        stageName,
        modelID,
    });

    return {
        pending,
        pieChartData,
    } as const;
}

export function usePredictChatRecordNum() {
    const taskID = useTaskTaskID();
    const groupID = useGroupID();
    const [res, {refresh}] = useResource(apiPredictChatRecordNum, {
        taskID,
        groupID,
    });
    return [res, refresh] as const;
}

export function useMetricOptions() {
    const strategyInfo = useTaskStrategyInfo();
    return strategyInfo?.metric?.map(({desc}) => ({
        label: desc,
        value: desc,
        tagName: desc,
    }));
}

export function useTaskScoreList() {
    const taskID = useTaskTaskID();
    return useResource(apiTaskScoreListGet, {taskID});
}

export function useTaskMapHasFunctionFields() {
    const taskID = useTaskTaskID();
    return useResource(apiTaskMapHasFunctionFieldsPost, {taskID});
}

export function useTaskModelDisplayList() {
    const taskID = useTaskTaskID();
    const [taskModelDisplayList, {refresh}] = useResource(
        apiTaskModelDisplayList,
        {taskID}
    );
    return [taskModelDisplayList, refresh] as const;
}

export function useGroupCaseStageTimeInitialize() {
    const caseID = useGroupCaseID();
    const stageID = useTaskStageID();

    useEffect(
        () => {
            // 调用 API 初始化阶段时间
            apiGroupCaseStageTimeInitialize({caseID, stageID});
        },
        [caseID, stageID]
    );
}

export function useTaskRecordModifyHistory(recordID: number) {
    const taskID = useTaskTaskID();
    const {refresh, data, pending} = useRequest(apiGetRecordModifyHistory, {
        taskID,
        recordID,
    });
    return {
        dataSource: data ?? [],
        pending,
        refresh,
    };
}

export function useIsCaseDispatchAuditing() {
    const isCaseDispatch = useIsCaseDispatch();
    const [stageInfo] = useTaskStageList();
    const isCaseDispatchAuditing = useMemo(
        () => {
            return (
                isCaseDispatch
                && stageInfo?.find(i => i.stageType === TaskStageEnum.AUDITING)
            );
        },
        [isCaseDispatch, stageInfo]
    );

    return isCaseDispatchAuditing;
}
