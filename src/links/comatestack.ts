/* eslint-disable max-len */
import {ParamsProjectUuid, ParamsWorkspaceUuid} from '@/types/params/pageParams';
import {ParamsUserGroupDetailLink} from '@/types/params/userGroup';
import {createComatestackLink, createLinkNoIcon} from './createLink';

export const HomeLink = createComatestackLink('/lab');

export const WorkspaceLink = createComatestackLink<ParamsWorkspaceUuid>('/workspace/{workspaceUuid}');

export const WorkspaceSettingsLink = createComatestackLink<ParamsWorkspaceUuid>('/workspace/{workspaceUuid}/settings');

export const WorkspaceKeysLink = createComatestackLink<ParamsWorkspaceUuid>('/workspace/{workspaceUuid}/keys');

export const WorkspaceMembersLink = createComatestackLink<ParamsWorkspaceUuid>('/workspace/{workspaceUuid}/members');

export const ApplyWorkspacePermissionLink = createComatestackLink<ParamsWorkspaceUuid>('/workspace/{workspaceUuid}/applyPermission');

// 暂时直接去 files，不然 ievalue 的 referer 有问题
export const ProjectLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/home');

export const ProjectEffectEvaluateLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/lab/evaluate');

export const ProjectPromptDevLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/lab/promptDev');

export const ProjectScenario = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/scenario');

export const ProjectDataAgentLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/dataAgent');

interface ParamsDocIntroduction {
    projectUuid: string;
    docID: number;
}
export const DocIntroductionLink = createComatestackLink<ParamsDocIntroduction>('/app/{projectUuid}/home/<USER>/{docID}');

export const ProjectScenarioChatLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/home/<USER>');

export const ProjectFilesLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/tree');

interface ParamsFilesFolder {
    projectUuid: string;
    filePath: string;
}

export const ProjectFilesFolderLink = createComatestackLink<ParamsFilesFolder>('/app/{projectUuid}/tree/master/{filePath}');

export const ProjectModelLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/model');

export const CreateProjectModelLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/createModel');

export const ProjectApprovalLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/AITraining/approval');

export const ErnieLabelLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/ernieLabel');

export const ProjectTrainingLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/AITraining/training');

interface ParamsProjectTrainingTask {
    projectUuid: string;
    taskId: number;
    taskIndex: number;
}

export const ProjectTrainingTaskLink = createComatestackLink<ParamsProjectTrainingTask>('/app/{projectUuid}/AITraining/training/task');

export const ProjectTrainingCreateLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/AITraining/training/task');

interface ParamsProjectEvalueTaskCreate {
    projectUuid: string;
    // 31 自动评估 17 人工评估
    templateId: number;
    stackServiceId: number;
    taskId: number;
}

export const ProjectEvalueCreateLink = createComatestackLink<ParamsProjectEvalueTaskCreate>('/app/{projectUuid}/task/create/LLM/detail/{templateId}?comateStackServiceId={stackServiceId}&trainTaskId={taskId}#part-1');

export const ProjectServicesLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/services');

export const ProjectSettingsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/settings');

export const ProjectStatisticsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/statistics');

export const ProjectMembersLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/members');

export const ProjectEvaluateSettingsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/setting/model');

export const ProjectGroupUserSettingsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/setting/groupUser');

export const ProjectCreateRoleLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/createRole');

interface ParamsProjectRolePermission {
    projectUuid: string;
    roleId: number;
}

export const ProjectPermissionConfigLink = createComatestackLink<ParamsProjectRolePermission>('/app/{projectUuid}/permission/{roleId}');

export const ProjectDocEditLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/lab/docEdit');

export const ProjectEvaluateDocDetailLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/lab/evaluateDocDetail');

export const ProjectCampaignLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/campaign/home');

export const ProjectPromptsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/prompt/list');

export const ProjectPromptFlowsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/promptflow/list');

export const ProjectPromptDiagnosisLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/promptTools/promptDiagnosis');

export const ProjectPromptOptimizeLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/promptTools/promptOptimize');

export const ProjectTasksLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/task/list');

export const ProjectEvaluateDatasetLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/dataset/list');

export const ProjectMonitorsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/monitor/task');

export const ProjectPromptTasksLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/promptTools/promptTasklist');

export const ProjectPromptGenerateLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/promptTools/promptGenerate');

export const ProjectReportsLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/report/list');

export const ReflowLink = createComatestackLink('/reflow');

export const CreateReflowLink = createComatestackLink('/createReflow');

export const ProjectReflowLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/reflow');

interface ParamsProjectCampaignDetail {
    projectUuid: string;
}

export const ProjectCampaignDetailLink = createComatestackLink<ParamsProjectCampaignDetail>('/app/{projectUuid}/campaign/detail');

export const ErnieInstrumentalizationLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/instrumentalization');

export const ErnieAutomationLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/automation');

export const ApplyProjectPermissionLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/applyPermission');

export const UserGroupDetailLink = createComatestackLink<ParamsUserGroupDetailLink>('/account/userGroup/{userGroupId}');

export const ErnieInstrumentalizationTaskListLink = createComatestackLink<ParamsProjectUuid>('/app/{projectUuid}/instrumentalization/task-list');

interface ParamsCreateLabelLink {
    projectUuid: string;
    taskName: string;
    templateId: string;
    filePath?: string;
}

export const CreateLabelLink = createComatestackLink<ParamsCreateLabelLink>(
    '/app/{projectUuid}/createLabel?filePath={filePath}&taskName={taskName}&templateId={templateId}'
);

export const UploadOpenSourceDatasetLink = createLinkNoIcon('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/iXnwY0AkKAh1Ir');

