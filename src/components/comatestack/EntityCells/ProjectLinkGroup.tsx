import {Button, marginRight} from '@panda-design/components';
import styled from '@emotion/styled';
import {Dropdown, MenuProps} from 'antd';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {Project} from '@/types/comatestack/project';
import {ProjectPromptsLink, ProjectTasksLink, ProjectTrainingLink} from '@/links/comatestack';
import {IconEvaluateTask, IconLabel, IconMore, IconPromptPublish, IconQianfan} from '@/icons/leftNav';
import {stopPropagation} from '@/utils/eventHandler';
import {LabelProjectListLink} from '@/links/label';

const Container = styled.div`
    display: flex;
    gap: 4px;
    .ant-5-btn.ant-5-btn-text, .ant-5-btn.ant-5-btn-link {
        font-size: 13px;
        line-height: 24px;
        padding: 0 2px;
        gap: 4px;
    }
`;

const items: MenuProps['items'] = [
    {
        key: 'training',
        label: <span><IconQianfan className={marginRight(8)} />模型训练</span>,
    },
    {
        key: 'tasks',
        label: <span><IconEvaluateTask className={marginRight(8)} />开始评估</span>,
    },
];

interface Props {
    type: 'card' | 'table';
    project: Project;
}

export const ProjectLinkGroup = ({type, project}: Props) => {
    const navigate = useNavigate();
    const size = type === 'card' ? 'small' : 'middle';
    const {uuid} = project;

    const handleDropdownClick: MenuProps['onClick'] = useCallback(
        ({key, domEvent}) => {
            domEvent?.stopPropagation();
            if (key === 'training') {
                navigate(ProjectTrainingLink.toUrl({projectUuid: uuid}));
                return;
            }
            if (key === 'tasks') {
                navigate(ProjectTasksLink.toUrl({projectUuid: uuid}));
            }
        },
        [navigate, uuid]
    );

    if (type === 'card') {
        return (
            <Container onClick={stopPropagation}>
                <ProjectPromptsLink projectUuid={uuid}>
                    <Button size={size} type="text" icon={<IconPromptPublish />}>Prompt管理</Button>
                </ProjectPromptsLink>
                <LabelProjectListLink projectUuid={uuid}>
                    <Button size={size} type="text" icon={<IconLabel />}>数据标注</Button>
                </LabelProjectListLink>
                <Dropdown menu={{items, onClick: handleDropdownClick}} trigger={['click']}>
                    <Button size="small" icon={<IconMore />} type="text" onClick={stopPropagation}>更多</Button>
                </Dropdown>
            </Container>
        );
    }

    return (
        <Container onClick={stopPropagation}>
            <ProjectPromptsLink projectUuid={uuid}>
                <Button size={size} type="text" icon={<IconPromptPublish />}>Prompt管理</Button>
            </ProjectPromptsLink>
            <LabelProjectListLink projectUuid={uuid}>
                <Button size={size} type="text" icon={<IconLabel />}>数据标注</Button>
            </LabelProjectListLink>
            <ProjectTrainingLink projectUuid={uuid}>
                <Button size={size} type="text" icon={<IconQianfan />}>模型训练</Button>
            </ProjectTrainingLink>
            <ProjectTasksLink projectUuid={uuid}>
                <Button size={size} type="text" icon={<IconEvaluateTask />}>开始评估</Button>
            </ProjectTasksLink>
        </Container>
    );
};
