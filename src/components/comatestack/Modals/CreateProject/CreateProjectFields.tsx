/* eslint-disable max-lines */
import {useCallback, useState, useEffect} from 'react';
import {
    Field,
    useFormContext,
    useFieldValue,
    useFieldDefaultProps,
} from '@panda-design/path-form';
import {Input} from 'antd';
import {LoadingOutlined} from '@ant-design/icons';
import {Button, marginTop, message} from '@panda-design/components';
import {v4 as uuidv4} from 'uuid';
import {colors} from '@/constants/colors';
import {MemberSelect} from '@/components/comatestack/FieldCells/MemberSelect';
import {SecLevelRadio} from '@/components/comatestack/FieldCells/SecLevelRadio';
import {WorkspaceSelect} from '@/components/comatestack/FieldCells/WorkspaceSelect';
import {hideFirstRemoveIcon} from '@/styles/components';
import {ICloudAccountCascader} from '@/components/comatestack/FieldCells/ICloudAccountCascader';
import {WorkspaceCreateTypeRadio} from '@/components/comatestack/FieldCells/WorkspaceCreateTypeRadio';
import {ProjectVisibilityRadio} from '@/components/comatestack/FieldCells/ProjectVisibilityRadio';
import {secLevelTip, projectNameTip} from '@/constants/comatestack/projectTips';
import {apiGetGenerateUuid} from '@/api/project';
import {loadWorkspaceList} from '@/regions/workspace/workspaceList';
import {ReflowLink} from '@/links/comatestack';
import {useCreateProjectMode} from '@/regions/project/createProject';
import {
    validateName,
    validateUuid,
    validateAccount,
    validateWorkspaceUuid,
    validateAdmins,
} from './validations';

export const CreateProjectFields = () => {
    const {setFieldValue} = useFormContext();
    const projectName = useFieldValue('name');
    const projectUuid = useFieldValue('uuid');
    const createWorkspace = useFieldValue('createWorkspace');
    const createProjectMode = useCreateProjectMode();
    const [isGeneratingProjectUuid, setIsGeneratingProjectUuid] = useState(false);
    const [uuidSessionId, setUuidSessionId] = useState(uuidv4());
    const props = useFieldDefaultProps();

    useEffect(
        () => {
            loadWorkspaceList({scope: 'WRITE'});
        },
        []
    );

    const startGenerateUuid = useCallback(
        async (text: string, uuidSessionId: string) => {
            setIsGeneratingProjectUuid(true);
            const generatedProjectUuid = await apiGetGenerateUuid({content: text, sessionId: uuidSessionId});
            setIsGeneratingProjectUuid(false);
            if (generatedProjectUuid) {
                setFieldValue('uuid', generatedProjectUuid);
            }
        }
        ,
        [setFieldValue]
    );

    const handleGenerateProjectUuid = useCallback(
        async () => {
            if (projectName) {
                const newSessionId = uuidv4();
                setUuidSessionId(newSessionId);
                startGenerateUuid(projectName, newSessionId);
            }
        },
        [projectName, startGenerateUuid]
    );

    const handleReGenerateProjectUuid = useCallback(
        async () => {
            if (projectName) {
                startGenerateUuid(projectName, uuidSessionId);
            }
            else {
                message.info('请先填写项目名称');
            }
        },
        [projectName, uuidSessionId, startGenerateUuid]
    );

    const isInWorkspaceCreateProject = createProjectMode === 'IN_WORKSPACE';

    return (
        <>
            <Field
                required
                name="name"
                label="项目名称"
                extra={props.disabled && (
                    <>
                        如需在其他已有项目或新的项目中创建回流任务，请在<ReflowLink>数据回流总览</ReflowLink>内操作
                    </>
                )}
                validate={validateName}
                tooltip={projectNameTip}
            >
                <Input placeholder="如“代码智能评审项目”、“XXX组织的项目”" onBlur={handleGenerateProjectUuid} />
            </Field>
            <Field required name="uuid" label="项目标识" validate={props.disabled ? null : validateUuid}>
                <Input
                    placeholder="可使用英文、数字、横线、下划线，不能填写空格，需全局唯一"
                    prefix={isGeneratingProjectUuid && <LoadingOutlined />}
                    addonAfter={
                        projectName && projectUuid
                            ? (
                                <Button
                                    type="link"
                                    size="small"
                                    disabled={props.disabled}
                                    onClick={handleReGenerateProjectUuid}
                                >
                                    重新生成
                                </Button>
                            )
                            : null
                    }
                />
            </Field>
            <Field required name="visibleType" label="项目类型">
                <ProjectVisibilityRadio />
            </Field>
            <Field required name="secLevel" label="数据分级" tooltip={secLevelTip}>
                <SecLevelRadio />
            </Field>
            <Field
                required
                name="accountId"
                label="资源账户"
                validate={validateAccount}
                style={{width: 'calc(100% - 120px)'}}
            >
                <ICloudAccountCascader placeholder="支持关键字搜索或选择，用于绑定您所属业务线" />
            </Field>
            <Field
                required
                name="createWorkspace"
                label="所属空间"
            >
                <WorkspaceCreateTypeRadio disabled={isInWorkspaceCreateProject} />
                {
                    createWorkspace && <span style={{color: colors['gray-6']}}>我们将按规则，自动为您创建一个空间</span>
                }
            </Field>
            <Field
                className={marginTop(-20)}
                name="workspaceUuid"
                label=" "
                validate={!createWorkspace && validateWorkspaceUuid}
                extra={(
                    <>
                        如果需要管理多个项目，您可以自行创建一个空间。
                    </>
                )}
                hidden={createWorkspace}
            >
                <WorkspaceSelect disabled={isInWorkspaceCreateProject} />
            </Field>
            <Field name="description" label="项目描述">
                <Input.TextArea placeholder="请添加对于项目的描述，方便其他同学了解并使用" />
            </Field>
            <Field required name="admins" label="项目管理员" validate={validateAdmins}>
                <MemberSelect className={hideFirstRemoveIcon} />
            </Field>
        </>
    );
};
