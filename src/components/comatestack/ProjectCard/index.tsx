import styled from '@emotion/styled';
import {Text} from '@panda-design/components';
import {Typography} from 'antd';
import CharAvatar from '@baidu/devops-design/es/components/Avatar/CharAvatar';
import {css} from '@emotion/css';
import {fontSize} from '@panda-design/components';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {colors} from '@/constants/colors';
import {ProjectLink} from '@/links/comatestack';
import UsersAvatarGroup from '@/design/UsersAvatarGroup';
import {Project} from '@/types/comatestack/project';
import {ProjectLinkGroup} from '@/components/comatestack/EntityCells/ProjectLinkGroup';
import {getRecentProject, loadProjectListById} from '@/regions/project/recentProjectList';
import {loadAllProjectList} from '@/regions/project/allProjectList';
import {buttonAnimationCss} from '@/styles/components';
import FavoriteStar from '../FavoriteStar';

const favoriteStarCss = css`
    position: absolute !important;
    top: 10px;
    right: 10px;
    opacity: 0;
`;

const CardContainer = styled.div`
    min-width: 332px;
    border: 1px solid ${colors['gray-5']};
    border-radius: 4px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :hover {
        .${favoriteStarCss} {
            opacity: 1 !important;
        }
    }
`;

const HeaderContainer = styled.div`
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
`;

const FooterContainer = styled.div`
    min-width: 300px;
    display: flex;
    justify-content: space-between;
    align-items: center;
`;

const handleSave = () => {
    loadAllProjectList({scope: 'MINE'});
    const uuids = getRecentProject();
    if (uuids.length) {
        loadProjectListById({uuids});
    }
};

interface Props {
    project: Project;
}

function ProjectCard({project}: Props) {
    const navigate = useNavigate();
    const {uuid} = project;

    const handleNavigate = useCallback(
        () => {
            navigate(ProjectLink.toUrl({projectUuid: uuid}));
        },
        [navigate, uuid]
    );

    return (
        <CardContainer className={buttonAnimationCss} onClick={handleNavigate}>
            <HeaderContainer>
                <CharAvatar name={project?.name} className={fontSize(28)} />
                <div className={css`flex: 1; width: 0;`}>
                    <Typography.Title
                        level={5}
                        ellipsis={{tooltip: project?.name}}
                    >
                        {project?.name}
                    </Typography.Title>
                    <div>
                        <Text
                            type="tertiary"
                            ellipsis={{tooltip: project?.description}}
                            className={fontSize(12)}
                        >
                            {project?.description || '暂无描述'}
                        </Text>
                    </div>
                </div>
                <FavoriteStar
                    className={favoriteStarCss}
                    type="PROJECT"
                    isFavorite={project.isFavorite}
                    uuid={project.uuid}
                    onSave={handleSave}
                />
            </HeaderContainer>
            <FooterContainer>
                <ProjectLinkGroup type="card" project={project} />
                <UsersAvatarGroup usernames={project?.admins?.map(item => item?.name)} />
            </FooterContainer>
        </CardContainer>
    );
}

export default ProjectCard;

