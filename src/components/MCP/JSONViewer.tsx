import styled from '@emotion/styled';
import {Flex} from 'antd';
import ReactJson from '@microlink/react-json-view';
import {ComponentProps, ReactNode, useMemo} from 'react';
import {css} from '@emotion/css';
import {CopyButton} from '@/components/ievalue/CopyButton/CopyButton';
import {IconFullscreen} from '@/icons/mcp';
import withFullscreenModal from './WithFullscreenModal/withFullscreenModal';

// 如果不加word-break: break-all;的话，JSONViewer的宽度会把容器撑大，不合适，应该是他的宽度跟着容器走
const Wrapper = styled(Flex)<{fullscreen?: boolean}>`
    background-color: #F5F7FA;
    border-radius: 6px;
    height: ${({fullscreen}) => (fullscreen ? 'calc(100vh - 100px)' : 'auto')};
    .string-value{
        word-break: break-all;
    }
`;

const Header = styled(Flex)`
    border-radius: 6px 6px 0 0;
    background-color: rgba(225, 227, 229, 0.5);
    padding: 3px 8px;
`;

interface Props {
    header?: ReactNode | null;
    json: Record<string, any> | string;
    toggleFullscreen: () => void;
    fullscreen?: boolean;
}

const iconCss = css`
    margin-left: 12px;
    cursor: pointer;
`;


function JSONViewer({
    json,
    header,
    toggleFullscreen,
    fullscreen,
}: Props) {
    const copyContent = useMemo(
        () => {
            if (typeof json === 'string') {
                return json;
            } else {
                return JSON.stringify(json, null, 4);
            }
        },
        [json]
    );
    return (
        <Wrapper vertical className="json-viewer-container" fullscreen={fullscreen}>
            {header === null ? null : (
                <Header justify="end" align="center">
                    <CopyButton text={copyContent} />
                    {!fullscreen && <IconFullscreen onClick={toggleFullscreen} className={iconCss} />}
                </Header>
            )}
            {
                typeof json === 'string' ? (
                    <pre style={{padding: '0 12px', color: '#BFBFBF', textWrap: 'wrap'}}>{json}</pre>
                ) : (
                    <ReactJson
                        src={json}
                        collapsed={false}
                        name={false}
                        indentWidth={2}
                        iconStyle="square"
                        displayDataTypes={false}
                        displayObjectSize={false}
                        enableClipboard={false}
                        style={{overflow: 'auto', padding: 12}}
                    />
                )
            }
        </Wrapper>
    );
}

// @ts-ignore
const JSONViewerWithFullscreen = withFullscreenModal<ComponentProps<typeof JSONViewer>>(
    JSONViewer, 'JSON Viewer', false
);

export default JSONViewerWithFullscreen;
