import {Tabs} from 'antd';
import {useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {marginLeft, marginTop} from '@panda-design/components';
import {cx} from '@emotion/css';
import {LabelViewProvider} from '@/label-view/providers/LabelViewProvider';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {useCodeTemplate} from '@/hooks/label/useCodeTemplate';
import {useCurrentLabelTaskDetail} from '@/regions/projectLabelTask/labelTaskDetail';
import {useIsMultiMediaLabelMode} from '@/label-view/regions/multiMediaLabelMode';
import {useLabelMode} from '@/regions/label/labelTask';
import LabelPredictionDetail from '../LabelPredictionDetail';
import LabelRegions from '../LabelRegions';
import {LabelReview} from '../LabelReview';

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-content, .ant-5-tabs-tabpane {
        height: 100% !important;
    }
`;

const LabelSidebar = () => {
    const labelMode = useLabelMode();
    const {labelStudioProjectId} = useLabelParams();
    const code = useCodeTemplate(labelStudioProjectId);
    const labelTaskDetail = useCurrentLabelTaskDetail();
    // 多模标注模式
    const isMultiMediaMode = useIsMultiMediaLabelMode();
    const [activeKey, setActiveKey] = useState('');

    const tabItems = useMemo(
        () => {
            const tabs = [
                {
                    label: '预标注详情',
                    key: 'LabelPredictionDetail',
                    children: <LabelPredictionDetail />,
                },
                {
                    label: '标注审核',
                    key: 'LabelReview',
                    children: <LabelReview />,
                },
            ];

            // 后续其他 Region 场景（如：划词 Region）在这里追加判断条件即可
            if (isMultiMediaMode) {
                tabs.unshift(
                    {
                        label: '划选区域',
                        key: 'LabelRegions',
                        children: <LabelRegions />,
                    }
                );
            }

            return tabs;
        },
        [isMultiMediaMode]
    );

    const defaultActiveKey = useMemo(
        () => {
            if (labelMode === 'Review' || labelTaskDetail?.review_result) {
                return 'LabelReview';
            }

            // 多模标注模式默认激活划选区域
            if (isMultiMediaMode) {
                return 'LabelRegions';
            }

            return 'LabelPredictionDetail';
        },
        [isMultiMediaMode, labelMode, labelTaskDetail?.review_result]
    );

    return (
        <LabelViewProvider
            code={code}
            originData={labelTaskDetail?.data}
        >
            <StyledTabs
                size="small"
                tabBarGutter={20}
                className={cx(marginLeft(8), marginTop(5))}
                style={{height: '100%'}}
                items={tabItems}
                activeKey={activeKey || defaultActiveKey}
                onChange={setActiveKey}
            />
        </LabelViewProvider>
    );
};

export default LabelSidebar;
