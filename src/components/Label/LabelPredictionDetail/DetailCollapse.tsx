import styled from '@emotion/styled';
import {useMemo, useEffect} from 'react';
import {useParams} from 'react-router-dom';
import {Text} from '@panda-design/components';
import {colors} from '@/constants/colors';
import EmptyData from '@/assets/empty/emptyData.svg';
import {useCurrentLabelTaskDetail} from '@/regions/projectLabelTask/labelTaskDetail';
import {loadPredictionConfigDetail, usePredictionConfigDetail} from '@/regions/preMarkPrompt/predictionConfigDetail';
import {useCurrentDatasetName} from '@/hooks/current/routeParams';
import {usePromptDetailIds, pushPromptDetailId} from '@/regions/preMarkPrompt/promptDetailIds';
import {PreMarkHelpLink} from '@/links/external';
import PromptTitle from './PromptTitle';
import DetailField from './DetailField';
import ResultField from './ResultField';

const FlexColumnContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 10px;
`;

const PromptItemContainer = styled.div`
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #F5F7FA 0%, rgba(245, 247, 250, 0.4) 100%);
    border-radius: 6px;
    padding: 10px;
    border: 1px solid ${colors['gray-3']};
`;

const EmptyContainer = styled.div`
    padding-top: 10%;
    text-align: center;
`;

function DetailCollapse() {
    const {predictions = []} = useCurrentLabelTaskDetail() ?? {};
    const datasetName = useCurrentDatasetName();
    const promptDetailIds = usePromptDetailIds();
    const {projectUuid} = useParams();

    const params = useMemo(
        () => ({
            datasetName,
            ruleJobIds: predictions?.map(v => (v.prediction_type === 'RULE' ? v.model_run : undefined)).filter(Boolean),
            project: projectUuid,
            modelJobIds: predictions?.map(
                v => (v.prediction_type === 'IDATASET_MODEL' ? v.model_run : undefined)
            ).filter(Boolean),
        }),
        [datasetName, predictions, projectUuid]
    );
    const configDetails = usePredictionConfigDetail(params);

    useEffect(
        () => {
            loadPredictionConfigDetail(params);
        },
        [params]
    );

    useEffect(
        () => {
            if (!promptDetailIds?.length && predictions?.length > 0) {
                pushPromptDetailId(predictions[0]?.model_run);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <FlexColumnContainer>
            {
                predictions.length > 0 ? (
                    predictions?.map(v => {
                        const {prediction_type, model_run, id, is_change} = v ?? {};
                        const currentDetail = prediction_type === 'IDATASET_MODEL'
                            ? configDetails?.modelDetails?.find(v => v.modelJobId === model_run)
                            : configDetails?.ruleDetails?.find(v => v.ruleJobId === model_run);
                        // @ts-expect-error
                        const {promptName, creator, name, startTime} = currentDetail ?? {};
                        return (
                            <PromptItemContainer key={id}>
                                <PromptTitle
                                    predictionType={prediction_type}
                                    promptName={promptName || name}
                                    creator={creator}
                                    id={id}
                                    startTime={startTime}
                                    model_run={model_run}
                                />
                                {promptDetailIds.includes(model_run) && (
                                    <>
                                        <DetailField currentDetail={currentDetail} predictionType={prediction_type} />
                                        <ResultField
                                            currentPrediction={v}
                                            predictionType={prediction_type}
                                            isChange={is_change}
                                        />
                                    </>
                                )}
                            </PromptItemContainer>
                        );
                    })
                ) : (
                    <EmptyContainer>
                        <div><img src={EmptyData} /></div>
                        <Text type="secondary">暂无预标注数据</Text>
                        <br />
                        <Text type="secondary">想提升标注效率，</Text>
                        <PreMarkHelpLink>点我查看方案</PreMarkHelpLink>
                    </EmptyContainer>
                )
            }
        </FlexColumnContainer>
    );
}

export default DetailCollapse;
