/* eslint-disable max-lines */
import {Button} from '@panda-design/components';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {Popconfirm} from 'antd';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {useSetBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {labelTaskDetailAsyncEffect, useReloadLabelTaskTableData} from '@/regions/projectLabelTask/labelAsyncEffect';
import {useLabelTaskId} from '@/hooks/label/useLabelTaskId';
import {usePermissions} from '@/hooks/permission/permission';
import {useIsModelPrediction} from '@/hooks/label/useIsModelPrediction';
import {useLabelView} from '@/label-view/providers/LabelViewProvider';
import {
    loadReviewResultItem,
    setLabelTaskIsChecked,
    useLabelTaskIsChecked,
    useReviewResultItem,
} from '@/regions/projectLabelTask/LabelTaskReview';
import {
    loadAssignedTaskDetail,
} from '@/regions/projectLabelTask/LabelTaskAssign';
import {useLabelMode} from '@/regions/label/labelTask';
import {getChineseWithName} from '@/label-view/utils/getChineseWithName';
import {colors} from '@/constants/colors';
import {batchLabelComponents} from '@/constants/label/batchLabelComponents';
import {loadDmProject} from '@/regions/projectLabelTask/dmProject';
import NoPermissionTip from '../../comatestack/Permission/NoPermissionTip';
import {useHandleCommit} from './useHandleCommit';
import {useRequiredNames} from './useRequiredNames';

export const CommitButton = () => {
    const {labelStudioProjectId} = useLabelParams();
    const taskId = useLabelTaskId();
    const hasLabelTaskPermission = usePermissions(['LABEL_TASK_LABEL']);
    const reloadLabelTaskTableData = useReloadLabelTaskTableData();
    const [isDisableLabel, setIsDisableLabel] = useState(true); // 默认先禁用提交
    // 确保在 LabelViewProvider 下
    const {annotation, requiredNameMap} = useLabelView();
    const {forward} = useSetBatchMarkTaskIndex();
    const isModelPrediction = useIsModelPrediction();
    const {result: reviewData} = useReviewResultItem({taskId}) ?? {};
    const isChecked = useLabelTaskIsChecked();
    const isReview = reviewData?.result === 'PASS' || reviewData?.result === 'FAILED';
    const [open, setOpen] = useState(false);
    const {isSubmitting, handleNormalLabelCommit, handleBatchlLabelCommit} = useHandleCommit();
    const requiredNames = useRequiredNames();
    const labelMode = useLabelMode();
    const buttonText = labelMode === 'Batch'
        ? '提交'
        : ((annotation?.id > 0 || isModelPrediction) ? '更新，标下一条' : '提交，标下一条');

    const filterdRequiredNames = useMemo(
        () => {
            if (labelMode === 'Batch') {
                return requiredNames.filter(name => batchLabelComponents.includes(requiredNameMap[name]));
            }

            return requiredNames;
        },
        [labelMode, requiredNameMap, requiredNames]
    );

    useEffect(
        () => {
            setIsDisableLabel(Boolean(filterdRequiredNames?.length));
        },
        [filterdRequiredNames]
    );

    const getEmptyRequiredMessage = useCallback(
        () => {
            if (!requiredNameMap) {
                return '当前模版中存在未填写的必填组件，请填写完毕后再提交或更新。';
            }

            const emptyComponents = filterdRequiredNames
                .map(name => {
                    const componentType = requiredNameMap[name];
                    if (!componentType) {
                        return null;
                    }
                    const chineseName = getChineseWithName(componentType);
                    return `${name}（${chineseName}）`;
                })
                .filter(Boolean);

            return (
                <>
                    当前模版中存在尚未填写的必填组件，分别是：
                    <span style={{color: colors.error}}>{emptyComponents.join('、')}</span>
                    ，请在必填组件填写完毕后再提交或更新。
                </>
            );
        },
        [filterdRequiredNames, requiredNameMap]
    );

    const handleUpdate = useCallback(
        async () => {
            const success = await handleNormalLabelCommit();
            if (success) {
                labelTaskDetailAsyncEffect({taskId, labelStudioProjectId});
                loadDmProject({project: labelStudioProjectId});
                if (labelMode === 'Normal') {
                    forward();
                }
            }
        },
        [handleNormalLabelCommit, taskId, labelStudioProjectId, labelMode, forward]
    );

    const handleClick = useCallback(
        () => {
            if ((isReview || isChecked) && !isModelPrediction) {
                setOpen(true);
            } else {
                handleUpdate();
            }
        },
        [isReview, isChecked, isModelPrediction, handleUpdate]
    );

    const handleBatchCommit = useCallback(
        async () => {
            await handleBatchlLabelCommit();
            reloadLabelTaskTableData();
            loadAssignedTaskDetail({labelStudioProjectId});
        },
        [handleBatchlLabelCommit, labelStudioProjectId, reloadLabelTaskTableData]
    );

    const handleConfirm = useCallback(
        async () => {
            try {
                await handleUpdate();
                setOpen(false);
                await loadReviewResultItem({taskId});
                if (isChecked) {
                    setLabelTaskIsChecked(false);
                }
                reloadLabelTaskTableData();
                loadAssignedTaskDetail({labelStudioProjectId});

            } catch (error) {
                console.error('更新失败:', error);
            }
        },
        [handleUpdate, isChecked, labelStudioProjectId, reloadLabelTaskTableData, taskId]
    );

    const title = useMemo(
        () => {
            return isChecked ? (
                <>当前标注已审核并验收，更新将清空审核<br />与验收结果，确定要更新吗？
                </>

            ) : (
                <>当前标注已审核，更新将清空审核结果。<br />确定要更新吗
                </>
            );
        },
        [isChecked]
    );

    const disabledReason = hasLabelTaskPermission ? getEmptyRequiredMessage() : <NoPermissionTip />;

    return (
        <Popconfirm
            title={title}
            open={open}
            onCancel={() => setOpen(false)}
            onConfirm={handleConfirm}
        >
            <Button
                type="primary"
                loading={isSubmitting}
                onClick={labelMode === 'Batch' ? handleBatchCommit : handleClick}
                disabled={!hasLabelTaskPermission || isDisableLabel}
                disabledReason={disabledReason}
            >
                {buttonText}
            </Button>
        </Popconfirm>
    );
};
