import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {useCurrentLabelTaskDetail} from '@/regions/projectLabelTask/labelTaskDetail';
import {loadReviewResultItem, useReviewResultItem} from '@/regions/projectLabelTask/LabelTaskReview';
import {apiPostReviewResultItem} from '@/api/labelTaskAssign';

interface Props {
    result: string;
    reason: string;
}

export const useReviewResult = () => {
    const labelTaskDetail = useCurrentLabelTaskDetail();
    const {annotations, id} = labelTaskDetail || {};
    const {labelStudioProjectId} = useLabelParams();
    const {result: reviewData} = useReviewResultItem({taskId: id}) ?? {};

    const createReviewResult = useCallback(
        async ({result, reason}: Props) => {
            try {
                await apiPostReviewResultItem({
                    labelStudioProjectId,
                    taskId: id,
                    taskAnnotationId: annotations?.length > 0 ? annotations[0]?.id : null,
                    result,
                    reason,
                });
                message.success('审核结果提交成功');
                loadReviewResultItem({taskId: id});
                return true;
            } catch (error) {
                message.error('审核结果提交失败');
                return false;
            }
        },
        [labelStudioProjectId, id, annotations]
    );

    const updateReviewResult = useCallback(
        async ({result, reason}: Props) => {
            try {
                await apiPostReviewResultItem({
                    labelStudioProjectId,
                    reviewRecordId: reviewData?.id,
                    result,
                    reason,
                });
                message.success('审核结果修改成功');
                loadReviewResultItem({taskId: id});
                return true;
            } catch (error) {
                message.error('审核结果更新失败');
                return false;
            }
        },
        [id, labelStudioProjectId, reviewData?.id]
    );

    return {
        updateReviewResult,
        createReviewResult,
    };
};
