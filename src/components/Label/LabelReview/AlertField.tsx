import {marginBottom} from '@panda-design/components';
import {Alert} from 'antd';
import {useLabelTaskReviewStatus} from '@/regions/projectLabelTask/LabelTaskReview';
export const AlertField = () => {
    const status = useLabelTaskReviewStatus();
    return (
        <>
            {status === 'FORBIDDEN_REVIEW' && (
                <Alert message="未标注的数据项暂不支持审核，请等待标注人提交后再审" type="error" className={marginBottom(15)} />
            )}
            {(status === 'PASS' || status === 'FAILED') && (
                <Alert message="修改后将覆盖当前审核结果" type="warning" className={marginBottom(15)} />
            )}
        </>
    );
};
