import {Descriptions} from 'antd';
import {css} from '@emotion/css';
import UserAvatar from '@/design/UserAvatar';
import {ReviewResultItemDetail} from '@/types/label/labelTaskReview';

interface Props {
    reviewData: ReviewResultItemDetail;
}

const desCss = css`
   margin-left: 10px !important;
   margin-bottom: 15px !important;
`;

export const RecentReviewInfo = ({reviewData}: Props) => {
    const items = [
        {label: '最近审核人', children: <UserAvatar username={reviewData?.operator} showText />},
        {label: '最近审核时间', children: reviewData?.gmtModified || reviewData?.gmtCreate},
    ];

    return (
        <>
            <Descriptions
                column={1}
                items={items}
                colon={false}
                labelStyle={{width: 110, color: '#5C5C5C'}}
                contentStyle={{color: 'black'}}
                className={desCss}
            />
        </>
    );
};
