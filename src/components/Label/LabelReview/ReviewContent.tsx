
import {Button, message, Text} from '@panda-design/components';
import {useFormContext, useFormSubmit} from '@panda-design/path-form';
import {useEffect} from 'react';
import styled from '@emotion/styled';
import {useLabelTaskReviewStatus} from '@/regions/projectLabelTask/LabelTaskReview';
import {ReviewResultItemDetail} from '@/types/label/labelTaskReview';
import {useSetBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {loadAssignedTaskDetail} from '@/regions/projectLabelTask/LabelTaskAssign';
import {useReloadLabelTaskTableData} from '@/regions/projectLabelTask/labelAsyncEffect';
import {useLabelMode} from '@/regions/label/labelTask';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {ReviewResultField} from './ReviewResultField';
import {ReviewReasonField} from './ReviewReasonField';
import {useReviewResult} from './useReviewResult';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-top: 30px;
    gap: 10px;
`;

interface Props {
    reviewData: ReviewResultItemDetail;
    taskId: number;
}

export const ReviewContent = ({reviewData, taskId}: Props) => {
    const {labelStudioProjectId} = useLabelParams();
    const status = useLabelTaskReviewStatus();
    const {setFieldValue, resetFields} = useFormContext();
    const {createReviewResult, updateReviewResult} = useReviewResult();
    const labelMode = useLabelMode();
    const reloadLabelTaskTableData = useReloadLabelTaskTableData();
    const {forward} = useSetBatchMarkTaskIndex();

    useEffect(
        () => {
            if (status === 'PASS' || status === 'FAILED') {
                setFieldValue('reviewResult', reviewData?.result);
                setFieldValue('reviewReason', reviewData?.reason);
            } else {
                resetFields();
            }
        },
        [reviewData, setFieldValue, resetFields, status, taskId]
    );

    const handleSubmit = useFormSubmit(
        async values => {
            const {reviewResult, reviewReason} = values || {};
            if (reviewResult) {
                if (status === 'CLEAR') {
                    await updateReviewResult({result: reviewResult, reason: reviewReason || ''});
                } else {
                    await createReviewResult({result: reviewResult, reason: reviewReason || ''});
                }
                if (labelMode === 'Review') {
                    forward();
                }
                reloadLabelTaskTableData();
                loadAssignedTaskDetail({labelStudioProjectId});
            } else {
                message.error('请填写审核结果');
            }
        }
    );


    return (
        <>
            <ReviewResultField />
            <ReviewReasonField reason={reviewData?.reason} />
            {
                (status === 'UNREVIEWED' || status === 'CLEAR') && (
                    <Container>
                        <Button gradient onClick={handleSubmit}>
                            确定
                        </Button>
                        {labelMode === 'Review' && (
                            <Text type="tertiary" style={{fontSize: 12}}>点击确定将自动跳转下一条数据</Text>
                        )}
                    </Container>
                )
            }
        </>
    );
};
