import {FormProvider} from '@panda-design/path-form';
import styled from '@emotion/styled';
import {useEffect} from 'react';
import {Loading} from '@panda-design/components';
import {useReviewResultItem} from '@/regions/projectLabelTask/LabelTaskReview';
import {useCurrentLabelTaskDetail} from '@/regions/projectLabelTask/labelTaskDetail';
import {setLabelTaskReviewStatus} from '@/regions/projectLabelTask/LabelTaskReview';
import {useLabelView} from '@/label-view/providers/LabelViewProvider';
import {AlertField} from './AlertField';
import {RecentReviewInfo} from './RecentReviewInfo';
import {ReviewContent} from './ReviewContent';
import {ReviewHistory} from './ReviewHistory';

const Container = styled.div`
    padding: 15px 15px 15px 0;
    max-height: 100%;
    overflow: auto;
    .ant-5-form-item{
        margin-bottom: 12px;
    }
`;

export const LabelReview = () => {
    const labelTaskDetail = useCurrentLabelTaskDetail();
    const {annotations, id} = labelTaskDetail || {};
    const {result: reviewData, history: reviewHistoryList} = useReviewResultItem({taskId: id}) ?? {};
    const isDraft = annotations?.length === 1 && annotations[0]?.id < 0;
    const status = isDraft ? 'FORBIDDEN_REVIEW' : (reviewData?.result || 'UNREVIEWED');
    const {loading} = useLabelView();

    useEffect(
        () => {
            setLabelTaskReviewStatus(status);
        },
        [status]
    );

    if (loading) {
        return <Loading />;
    }

    return (
        <Container>
            <AlertField />
            {
                (status === 'PASS' || status === 'FAILED') && (
                    <RecentReviewInfo reviewData={reviewData} />
                )
            }
            <FormProvider>
                <ReviewContent reviewData={reviewData} taskId={id} />
            </FormProvider>
            {reviewHistoryList?.length > 0 && <ReviewHistory reviewHistoryList={reviewHistoryList} />}
        </Container>
    );
};
