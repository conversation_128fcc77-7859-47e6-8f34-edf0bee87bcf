import {FieldLayout} from '@panda-design/path-form';
import {Tag} from '@panda-design/components';
import {marginBottom} from '@panda-design/components';
import UserAvatar from '@/design/UserAvatar';
import {ReviewResultItemDetail} from '@/types/label/labelTaskReview';

interface Props {
    reviewData: ReviewResultItemDetail;
}

export const HistoryItem = ({reviewData}: Props) => {
    const isPass = reviewData?.result === 'PASS';

    return (
        <div className={marginBottom(10)}>
            <FieldLayout label="审核人" hasGap={false} width={100}>
                <UserAvatar username={reviewData?.operator} showText />
            </FieldLayout>
            <FieldLayout label="审核时间" hasGap={false} width={100}>
                {reviewData?.gmtModified || reviewData?.gmtCreate}
            </FieldLayout>
            <FieldLayout label="审核结果" hasGap={false} width={100}>
                <Tag color={isPass ? 'success' : 'error'} round type="flat">
                    {isPass ? '通过' : '不通过'}
                </Tag>
            </FieldLayout>
            <FieldLayout label="审核意见" hasGap={false} width={100}>
                {reviewData?.reason || '暂无意见'}
            </FieldLayout>
        </div>
    );
};
