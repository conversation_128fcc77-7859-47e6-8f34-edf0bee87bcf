import {Divider, Steps, Typography} from 'antd';
import styled from '@emotion/styled';
import {useMemo} from 'react';
import {ReviewResultItemDetail} from '@/types/label/labelTaskReview';
import {HistoryItem} from './HistoryItem';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
`;

interface Props {
    reviewHistoryList: ReviewResultItemDetail[];
}

export const ReviewHistory = ({reviewHistoryList}: Props) => {
    const items = useMemo(
        () => reviewHistoryList?.map(item => {
            return {
                description: <HistoryItem reviewData={item} />,
            };
        }),
        [reviewHistoryList]
    );

    return (
        <Container>
            <Divider>
                <Typography.Title level={5}>审核历史</Typography.Title>
            </Divider>
            {reviewHistoryList?.length > 1 ? (
                <Steps
                    progressDot
                    current={reviewHistoryList?.length}
                    direction="vertical"
                    items={items}
                />
            ) : (
                <HistoryItem reviewData={reviewHistoryList?.[0]} />
            )}
        </Container>
    );
};
