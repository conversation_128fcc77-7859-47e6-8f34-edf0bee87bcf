import {Input, Modal} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import {closeOpen, useOpenProps} from '@/regions/ievalue/prompt/openRegion';

export const FullScreenTextAreaModal = ({modalKey}: {modalKey: string}) => {
    const props = useOpenProps(modalKey);
    const [value, setValue] = useState<string>();
    useEffect(
        () => {
            if (props?.value !== undefined) {
                setValue(String(props.value));
            }
        },
        [props?.value]
    );

    const onOk = useCallback(
        () => {
            props.onChange(value);
            closeOpen(modalKey);
        },
        [modalKey, props, value]
    );
    if (!props) {
        return null;
    }
    return (
        <Modal
            width={900}
            open
            title={props.modalTitle || '输入'}
            onCancel={() => closeOpen(modalKey)}
            onOk={onOk}
        >
            <Input.TextArea
                {...props}
                autoSize={{minRows: 10, maxRows: 20}}
                value={value}
                onChange={(e: any) => setValue(e.target?.value)}
                height="500px"
                width="100%"
                placeholder="请输入"
                showMsg
            />
        </Modal>
    );
};
