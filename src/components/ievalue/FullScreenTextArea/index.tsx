import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {forwardRef, useCallback, useRef} from 'react';
import {Input} from 'antd';
import {TextAreaProps} from 'antd/es/input';
import {IconFullScreen} from '@/icons/ievalue';
import {getUniqueId} from '@/utils/ievalue';
import {openWithProps} from '@/regions/ievalue/prompt/openRegion';
import {FullScreenTextAreaModal} from './FullScreenTextAreaModal';
const fullIconCss = css`
    display: none;
    position: absolute;
    top: 8px;
    right: 2px;
    z-index: 1000;
    cursor: pointer;
    background: #fff;
`;
const Container = styled.div`
    position: relative;
    :hover {
        .${fullIconCss} {
            display: block;
        }
    }
`;
interface FullScreenTextAreaProps extends TextAreaProps {
    modalTitle?: string;
}
export const FullScreenTextArea = forwardRef((
    props: FullScreenTextAreaProps,
    ref: any
) => {
    const modalKeyRef = useRef('FullScreenTextArea' + getUniqueId());
    const onFullClick = useCallback(
        () => {
            openWithProps(modalKeyRef.current, props);
        },
        [props]
    );
    return (
        <Container>
            <Input.TextArea
                ref={ref}
                {...props}
                placeholder="请输入"
            />
            <FullScreenTextAreaModal modalKey={modalKeyRef.current} />
            <IconFullScreen className={fullIconCss} onClick={onFullClick} />
        </Container>
    );
});
