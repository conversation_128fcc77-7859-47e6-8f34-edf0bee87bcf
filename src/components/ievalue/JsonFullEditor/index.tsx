import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {useCallback, useRef} from 'react';
import {IconFullScreen} from '@/icons/ievalue';
import {getUniqueId} from '@/utils/ievalue';
import {openWithProps} from '@/regions/ievalue/prompt/openRegion';
import {CustomJson, CustomJsonProps} from '../JsonEditor/CustomJson';
import {JsonFullEditorModal} from './JsonFullEditorModal';
const fullIconCss = css`
    display: none;
    position: absolute;
    top: 8px;
    right: 2px;
    z-index: 1000;
    cursor: pointer;
`;
const Container = styled.div`
    position: relative;
    :hover {
        .${fullIconCss} {
            display: block;
        }
    }
`;

export const JsonFullEditor = (
    props: CustomJsonProps & { modalTitle?: string }
) => {
    const modalKeyRef = useRef('jsonFullEditor' + getUniqueId());
    const onFullClick = useCallback(
        () => {
            openWithProps(modalKeyRef.current, props);
        },
        [props]
    );
    return (
        <Container>
            <CustomJson
                {...props}
                height="180px"
                width="100%"
                placeholder="请输入JSON"
                showMsg
            />
            <JsonFullEditorModal modalKey={modalKeyRef.current} />
            <IconFullScreen className={fullIconCss} onClick={onFullClick} />
        </Container>
    );
};
