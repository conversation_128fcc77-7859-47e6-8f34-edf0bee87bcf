import {ConfigProvider, Form} from 'antd';
import {useMemo, useRef} from 'react';
import {DisplayDebugItem} from '@/api/ievalue/model';
import {DisplayDebugItemCompProps} from '..';
import {TipComp} from '../TipComp';
import {FormLabel} from '../FormLabel';
import {formTooltip} from '../FormTooltip';
import {FullScreenTextArea} from '../../FullScreenTextArea';

export const TextAreaFormItem = ({
    item,
    isSingle,
    paths,
    showUseParam,
}: DisplayDebugItemCompProps) => {
    const debugParams = Form.useWatch('debugParams');
    const isHistory = Form.useWatch('isHistory');
    const {componentDisabled} = ConfigProvider.useConfig();

    const functions = useMemo(
        () => {
            return debugParams?.find((e: DisplayDebugItem) => e.id === 'functions')
                ?.value;
        },
        [debugParams]
    );
    const textAreaProps = {
        maxLength: item.maxLength > -1 ? item.maxLength : NaN,
        disabled:
            componentDisabled
            || isHistory
            || (item.id === 'system' && !!functions),
        autoSize: {minRows: item.id === 'system' ? 6 : 1, maxRows: 6},
    };
    const ref = useRef<HTMLInputElement>(null);
    if (isSingle) {
        return (
            <Form.Item
                name={[...paths, 'value']}
                label={(!showUseParam || !item?.inUseDisplay) && <FormLabel displayName={item.displayName} />}
                tooltip={formTooltip(item.description)}
            >
                {/* <Input.TextArea style={{width: '100%'}} {...textAreaProps} /> */}
                <FullScreenTextArea
                    ref={ref}
                    style={{width: '100%'}}
                    modalTitle={item.displayName}
                    {...textAreaProps}
                />
            </Form.Item>
        );
    }

    return (
        <div style={{marginBottom: 8}} onClick={e => e.stopPropagation()}>
            <TipComp label={item.displayName} tip={item.description} />
            <Form.Item noStyle name={[...paths, 'value']}>
                <FullScreenTextArea
                    ref={ref}
                    style={{width: '100%'}}
                    modalTitle={item.displayName}
                    {...textAreaProps}
                />
                {/* <Input.TextArea
                    ref={ref}
                    style={{width: '100%'}}
                    {...textAreaProps}
                    onClick={() => {
                        ref.current?.focus();
                    }}
                /> */}
            </Form.Item>
        </div>
    );
};
