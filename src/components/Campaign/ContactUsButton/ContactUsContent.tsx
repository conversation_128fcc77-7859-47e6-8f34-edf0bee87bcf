import {Descriptions, Divider, Space} from 'antd';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {DutyRosterComateStackLink} from '@/links/external';
import QRCodeSrc from '@/assets/img/serviceNumberQRcode.png';
import ContactNames from './ContactNames';


const Container = styled.div`
    font-size: 12px;
    max-width: 260px;
    padding: 6px;
`;

const DutyRosterLinkContainer = styled(DutyRosterComateStackLink)`
    color: #4471EC;
`;

const Title = styled.div`
    font-size: 14px;
    font-weight: 500;
`;

const imgCss = css`
    width: 170px
`;

const items = [
    {
        key: '1',
        label: 'Comate Stack产品负责',
        children: <ContactNames users={['sulin01', 'chenyaozong']} />,
    },
    {
        key: '2',
        label: '评估功能研发负责',
        children: <ContactNames users={['huangyihe']} />,
    },
    {
        key: '3',
        label: 'Prompt功能研发负责',
        children: <ContactNames users={['zhangzhaoxin01']} />,
    },
    {
        key: '4',
        label: '数据及其他功能研发负责',
        children: <ContactNames users={['wangwenjin01']} />,
    },
];

export function ContactUsContent() {
    return (
        <Container>
            <Descriptions
                labelStyle={{fontSize: 12, width: 145}}
                contentStyle={{fontSize: 12, color: 'black'}}
                style={{width: '255px', marginBottom: 10}}
                column={1}
                items={items}
                colon={false}
                size="small"
            />
            <div>也可直接查询
                <DutyRosterLinkContainer>
                    Comate Stack值班表
                </DutyRosterLinkContainer>
            </div>
            <Divider dashed style={{margin: '10px 0'}} />
            <Space direction="vertical">
                <Title>Comate Stack服务号</Title>
                <span style={{color: 'gray'}}>关注服务号，及时获取平台动态，跟进任务分配及执行信息。</span>
                <img className={imgCss} src={QRCodeSrc} />
            </Space>
        </Container>
    );
}
