import {IconRuliu} from '@baidu/ee-icon';
import {useEffect} from 'react';
import {css} from '@emotion/css';
import {RuliuContactLink} from '@/links/createLink';
import {loadUserInfo, useUserInfo} from '@/regions/userGroup/userGroup';
import {UserDetail} from '@/types/comatestack/userGroup';

interface Props {
    users: string[];
}

const iconCss = css`
    vertical-align: middle;
    margin-Left: 4px;
`;

const ContactNames = ({users}: Props) => {
    useEffect(
        () => {
            loadUserInfo({users});
        },
        [users]
    );

    const userDetails = useUserInfo({users});

    return (
        <div>
            {
                userDetails?.map((user: UserDetail, index) => {
                    return (
                        <div key={index}>
                            <span>{user.name}</span>
                            <RuliuContactLink ruliuId={user.hiNumber} style={{color: '#000000'}}>
                                <IconRuliu className={iconCss} />
                            </RuliuContactLink>
                        </div>
                    );
                })
            }
        </div>
    );
};
export default ContactNames;
