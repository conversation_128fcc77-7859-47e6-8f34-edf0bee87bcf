import {useCallback, useEffect, useState} from 'react';
import {Checkbox, FloatButton, Popover} from 'antd';
import styled from '@emotion/styled';
import {useLocation} from 'react-router-dom';
import {IconContactUs} from '@/icons/common';
import {myToken} from '@/constants/colors';
import {setButtonPosition, setIsShowButton, useButtonPosition, useIsShowButton} from './buttonRegion';
import {ContactUsContent} from './ContactUsContent';

const Container = styled.div`
    display: flex;
    align-items: center;
    padding: 6px;
    justify-content: space-between;
`;

const StyledSpan = styled.span`
    font-weight: normal;
`;

const StyledFloatButton = styled(FloatButton)`
    position: fixed !important;
    background: ${myToken.gradientSolid2} ;
    .ant-5-float-btn-body {
        background: none !important;
    }
`;

export const ContactUsButton = () => {
    const position = useButtonPosition();
    const isShowButton = useIsShowButton();
    const {pathname} = useLocation();
    const isHomePage = pathname === '/comatestack' || pathname === '/comatestack/';
    const [isDragging, setIsDragging] = useState(false);
    const [isOpenPopover, setIsOpenPopover] = useState(false);

    const handleMouseDown = () => {
        setIsDragging(true);
        setIsOpenPopover(false);
    };

    const handleMouseMove = useCallback(
        (e: MouseEvent) => {
            if (isDragging) {
                const newY = ((e.clientY - 20) / window.innerHeight) * 100;
                const boundedY = Math.min(Math.max(newY, 10), 90);
                setButtonPosition(prevPosition => ({x: prevPosition.x, y: `${boundedY}%`}));
            }
        },
        [isDragging]
    );

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    useEffect(
        () => {
            window.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('mouseup', handleMouseUp);
            return () => {
                window.removeEventListener('mousemove', handleMouseMove);
                window.removeEventListener('mouseup', handleMouseUp);
            };
        },
        [handleMouseMove, isDragging]
    );

    const handleHideForever = () => {
        setIsOpenPopover(false);
        setIsShowButton(false);
    };

    if (!isShowButton && !isHomePage) {
        return null;
    }

    return (
        <Popover
            title={
                <Container>
                    联系我们
                    {!isHomePage && (
                        <Checkbox onChange={handleHideForever}>
                            <StyledSpan>不再显示</StyledSpan>
                        </Checkbox>
                    )}
                </Container>
            }
            content={<ContactUsContent />}
            placement="left"
            open={isOpenPopover}
            onOpenChange={setIsOpenPopover}
        >
            <StyledFloatButton
                style={{left: position.x, top: position.y}}
                onMouseDown={handleMouseDown}
                icon={<IconContactUs style={{color: 'white'}} />}
            />
        </Popover>
    );
};
