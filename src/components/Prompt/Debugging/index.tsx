/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable max-depth */
import {css} from '@emotion/css';
import {Modal, message} from '@panda-design/components';
import Split from '@uiw/react-split';
import {Col, Form, Row} from 'antd';
import {DisabledContextProvider} from 'antd/es/config-provider/DisabledContext';
import {debounce} from 'lodash';
import {useCallback, useEffect} from 'react';
import {useLocation} from 'react-router-dom';
import {
    apiPromptUpdate,
    PromptRunParams,
    PromptSingleRunResult,
    PromptUpdateParams,
} from '@/api/ievalue/prompt';
import {getSelectModel} from '@/components/ievalue/ModelDebugParameters';
import {InputTypeEnum} from '@/constants/ievalue/prompt';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {usePromptID, usePromptResultList} from '@/hooks/ievalue/prompt';
import {MessageModelTypes, ModelTypeEnum} from '@/constants/ievalue/model';
import {getUploadAccept} from '@/utils/ievalue/model';
import {usePromptContext} from '@/components/Prompt/PromptProvider/PromptProvider';
import {usePromptWebSocketContext} from '@/components/PromptChat/PromptWebSocketProvider';
import {useInitialValues} from './useInitialValues';
import {EditorPanel} from './components/EditorPanel';
import {ModelPanel} from './components/ModelPanel';
import {ResultPanel} from './components/ResultPanel';
import {ResultPanelHeader} from './components/ResultPanelHeader';
import {VariablePanel} from './components/VariablePanel';
import {FormLogic} from './components/FormLogic';
import {useFormPromptResult} from './useFormPromptResult';
import {useUiConfig} from './useUiConfig';

export type FormData = Omit<PromptRunParams, 'spaceCode' | 'promptID'> & {
    // 在这里增加字段，还需要在表单中增加相应Form.Item
    promptResult?: PromptSingleRunResult;
    isHistory?: boolean;
};

export const onPromptUpdate = ({
    params,
    callback,
    catchCallback,
}: {
    params: PromptUpdateParams;
    callback?: () => void;
    catchCallback?: () => void;
}) => {
    apiPromptUpdate(params)
        .then(_ => {
            callback?.();
        })
        .catch(_ => {
            catchCallback?.();
        });
};
export const debouncedPromptUpdate = debounce(onPromptUpdate, 1000);


// export const DraftContext = createContext<FormData | undefined>(undefined);


export const PromptDebugging = () => {
    const {
        form,
        setValues,
        isHistoryVersion,
        isResultHistory,
        versionID,
        refreshVersionList,
        modelList,
        currentVersion,
        promptList,
        modelOptions,
        runPending,
    } = usePromptContext();
    const {startAndSendMessage} = usePromptWebSocketContext();
    const spaceCode = useSpaceCodeSafe();
    const promptID = usePromptID();
    const uiConfig = useUiConfig();
    const [_, {refresh: refreshResultList}] = usePromptResultList(promptID);
    const modelID = Form.useWatch(['modelID'], form);
    const inputType = Form.useWatch(['inputType'], form);
    const messages = Form.useWatch(['messages'], form);
    const debugParams = Form.useWatch(['debugParams'], form);

    useEffect(
        () => {
            const values = form.getFieldsValue();
            setValues(values);
        },
        [form, setValues, debugParams, modelID, inputType, messages]
    );
    const onValuesChange = useCallback(
        (changed: any, value: FormData) => {
            setValues(value);
            const key = Object.keys(changed)[0];
            let inputType = value.inputType
                ? value.inputType
                : InputTypeEnum.TEXT;
            if (key === 'modelID' && currentVersion) {
                const modelID = value[key];
                const selectedModel = getSelectModel(
                    modelID,
                    currentVersion,
                    modelList
                );
                if (inputType === InputTypeEnum.MESSAGES && !MessageModelTypes.includes(
                    selectedModel?.modelType as ModelTypeEnum
                )) {
                    inputType = InputTypeEnum.TEXT;
                }
                const debugParams = selectedModel?.debugParams?.map(e => {
                    if (e.id === 'system') {
                        return {
                            ...e,
                            value: currentVersion?.system || e.value,
                        };
                    }
                    return e;
                });
                const uploadAccept = getUploadAccept(selectedModel);
                if (!uploadAccept && value?.multiModal) {
                    form.setFieldValue('multiModal', undefined);
                }
                form.setFieldsValue({
                    debugParams,
                    inputType,
                });
                if (!isHistoryVersion && !isResultHistory) {
                    const params = {
                        modelID,
                        debugParams,
                        inputType,
                        promptID,
                    };
                    debouncedPromptUpdate({
                        params,
                        callback: refreshVersionList,
                    });
                }

            } else if (key === 'debugParams') {
                const valueIdx = changed.debugParams?.findIndex(
                    (e: any) => !!e
                );
                const debugObj = value.debugParams[valueIdx];
                const params = {...value, promptID, inputType};
                if (debugObj.id === 'system') {
                    params.system = debugObj.value;
                }
                if (!isHistoryVersion && !isResultHistory) {
                    debouncedPromptUpdate({
                        params,
                        callback: () => {
                            if (
                                !['text', 'multiModal', 'messages'].some(
                                    e => e === key
                                )
                            ) {
                                refreshVersionList();
                            }
                        },
                    });
                }
            } else if (!isHistoryVersion && !isResultHistory) {
                debouncedPromptUpdate({
                    params: {...value, promptID, inputType},
                    callback: () => {
                        if (
                            !['text', 'multiModal', 'messages'].some(
                                e => e === key
                            )
                        ) {
                            refreshVersionList();
                        }
                    },
                });
            }
        },
        [currentVersion, form, isHistoryVersion, isResultHistory, modelList, promptID, refreshVersionList, setValues]
    );
    const handleNewRunResult = useCallback(
        (newResult: PromptSingleRunResult) => {
            form.setFieldsValue({promptResult: newResult});
            uiConfig.result.toggle(true);
        },
        [form, uiConfig.result]
    );
    const handleFinish = useCallback(
        async (values: FormData) => {
            if (values.inputType === InputTypeEnum.MESSAGES) {
                if (!values.messages || values.messages?.length === 0) {
                    message.warning('请输入messages');
                    return;
                } else {
                    const item = values.messages.find(e => !e.content);
                    if (item) {
                        message.warning(`请输入${item?.role}`);
                        return;
                    }
                }
            } else if (
                values.inputType === InputTypeEnum.TEXT
                    && !values.text
            ) {
                message.warning('请输入text');
                return;
            }
            const promptName = promptList.find(
                item => promptID === item.promptID
            )?.promptName;
            startAndSendMessage({
                params: {
                    ...values,
                    spaceCode,
                    promptID,
                    promptName,
                    versionID,
                },
                onMessage: data => {
                    handleNewRunResult(data);
                },
                onClose: () => {
                    /** */
                    refreshResultList();
                    refreshVersionList();
                },
                onError: (err: any) => {
                    if (err.cause?.desc?.includes('下线') || err.cause?.desc?.includes('删除')) {
                        Modal.warning({
                            title: '模型失效通知',
                            content: err.cause?.desc,
                        });
                        return;
                    }
                    const msg = err.message || '运行失败';
                    message.error(
                        err.cause?.desc ? `${msg}：${err.cause?.desc}` : msg
                    );
                },
            });
        },
        [
            promptList,
            startAndSendMessage,
            spaceCode,
            promptID,
            versionID,
            handleNewRunResult,
            refreshResultList,
            refreshVersionList,
        ]
    );
    const {state} = useLocation();
    const {setPromptResult, clear} = useFormPromptResult();
    useEffect(
        () => {
            if (state?.output) {
                setPromptResult(state);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [state]
    );

    const initialValues = useInitialValues();
    useEffect(
        () => {
            uiConfig.result.toggle(false);
            if (versionID && !isResultHistory) {
                clear();
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [isHistoryVersion, versionID]
    );

    return (
        <div
            className={css`
                padding-left: 14px;
                height: 100%;
            `}
        >
            {initialValues && (
                <Form
                    form={form}
                    onFinish={handleFinish}
                    style={{height: '100%'}}
                    onValuesChange={onValuesChange}
                    initialValues={initialValues}
                >
                    {/* 额外前端控制用状态，不需要提交给后端 */}
                    <Form.Item hidden name="promptResult" />
                    <Form.Item hidden name="isHistory" />
                    <FormLogic />

                    <Row style={{height: '100%', flexWrap: 'nowrap'}}>
                        <Col
                            style={{
                                height: '100%',
                                borderRight: '1px solid var(--color-gray-5)',
                            }}
                        >
                            <DisabledContextProvider
                                disabled={isHistoryVersion || isResultHistory}
                            >
                                <ModelPanel
                                    modelList={modelList}
                                    modelOptions={modelOptions}
                                />
                            </DisabledContextProvider>
                        </Col>
                        <Split
                            mode="vertical"
                            lineBar
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                flex: 1,
                                width: 'calc(100% - 280px)',
                            }}
                            onDragging={uiConfig.result.handleResultDragging}
                        >
                            <Split
                                lineBar
                                style={{
                                    display: 'flex',
                                    alignItems: 'stretch',
                                    flex: 1,
                                    minHeight: '100px',
                                }}
                            >
                                <div style={{minWidth: '460px', flex: 1}}>
                                    <DisabledContextProvider
                                        disabled={isHistoryVersion || isResultHistory}
                                    >
                                        <EditorPanel />
                                    </DisabledContextProvider>
                                </div>
                                <div style={{minWidth: '240px'}}>
                                    <DisabledContextProvider
                                        disabled={isResultHistory}
                                    >
                                        <Form.Item noStyle name="variables">
                                            <VariablePanel />
                                        </Form.Item>
                                    </DisabledContextProvider>
                                </div>
                            </Split>
                            <Row
                                style={{minHeight: '76px', height: 0}}
                                ref={uiConfig.result.ref}
                            >
                                <Col flex={1} style={{height: '100%'}}>
                                    <div
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            height: '100%',
                                        }}
                                    >
                                        <Col
                                            span={24}
                                            style={{flexBasis: '76px'}}
                                        >
                                            <ResultPanelHeader
                                                loading={runPending}
                                                submit={() => form.submit()}
                                                resultAction={uiConfig.result}
                                            />
                                        </Col>
                                        <Col
                                            span={24}
                                            style={{
                                                display: uiConfig.result.show
                                                    ? 'unset'
                                                    : 'none',
                                                borderTop:
                                                    '1px solid var(--color-gray-5)',
                                                borderRight:
                                                    '1px solid var(--color-gray-5)',
                                                flex: 1,
                                            }}
                                        >
                                            <ResultPanel />
                                        </Col>
                                    </div>
                                </Col>
                            </Row>
                        </Split>
                    </Row>
                </Form>
            )}
        </div>
    );
};
