import {EllipsisOutlined} from '@ant-design/icons';
import {css} from '@emotion/css';
import {Dropdown} from 'antd';

export const PromptVariableOpration = ({
    items,
}: {
    items: any[];
}) => {
    return (
        <Dropdown
            menu={{items}}
            rootClassName={css`
                .ant-5-popover {
                    padding-right: 20px !important;
                }
                .ant-5-dropdown-menu-item {
                    padding: 0 !important;
                    height: 32px;
                }
                .ant-5-dropdown-menu-title-content {
                    display: inline-block;
                    > div {
                        display: flex !important;
                        align-items: center;
                        padding: 5px 12px;
                    }
                }
            `}
        >
            <a onClick={e => e.preventDefault()}>
                <EllipsisOutlined />
            </a>
        </Dropdown>
    );
};
