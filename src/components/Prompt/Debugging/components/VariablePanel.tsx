/* eslint-disable max-lines */
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {Card, Empty, Input, Space, Typography} from 'antd';
import {useCallback, useMemo, useState} from 'react';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {VariablesLabel} from '@/constants/ievalue/prompt';
import {PromptVariable} from '@/api/ievalue/prompt-version';
import {usePromptContext} from '@/components/Prompt/PromptProvider/PromptProvider';
import {PanelContainer} from './ModelPanel';
import {VariableFullButton} from './VariableFullButton';
import {VariableFullModal} from './VariableFullButton/VariableFullModal';
import {PromptVariableOpration} from './PromptVariableOpration';
import {VideoParseButton} from './VideoParseButton';
import {VideoParseModal} from './VideoParseButton/VideoParseModal';
import {VariableLabelTag} from './VariableLabelTag';
import {VariableHistoryButton} from './VariableHistoryButton';

export interface Variable {
    key: string;
    value: string;
    label?: VariablesLabel;
    nid?: string;
}
function NoVariables() {
    return (
        <div
            style={{
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: 200,
            }}
        >
            <Empty
                description={
                    <span style={{color: '#8f8f8f'}}>
                        您还没有使用任何变量。
                        <br />
                        在编辑中使用 “ {'{{变量}}'}” 来创建变量。
                    </span>
                }
            />
        </div>
    );
}

export function PromptVariableItem({
    variable: v,
    onChange,
    textAreaProps = {},
}: {
    variable: PromptVariable;
    onChange?: (v: PromptVariable) => void;
    textAreaProps?: any;
}) {
    const [expend, setExpend] = useState(true);
    const {isResultHistory} = usePromptContext();
    const items = useMemo(
        () => {
            const menuItems: any[] = [
                {
                    key: '1',
                    label: <VariableFullButton
                        variable={v}
                        onChange={onChange}
                    />,
                },
                {
                    key: '2',
                    label: <div onClick={() => setExpend(!expend)}>{expend ? '收起' : '展开'}</div>,
                },
            ];
            if (!isResultHistory) {
                menuItems.push({
                    key: '3',
                    disabled: isResultHistory,
                    label: <VideoParseButton
                        value={v}
                        onChange={onChange}
                        disabled={isResultHistory}
                    />,
                });
                menuItems.push({
                    key: '4',
                    disabled: isResultHistory,
                    label: <VariableHistoryButton
                        value={v}
                        onChange={onChange}
                    />,
                });
            }
            return menuItems;
        },
        [expend, isResultHistory, onChange, v]
    );

    return (
        <Card
            className={css`
                :hover {
                    .expendIcon {
                        display: block;
                    }
                }
                .expendIcon {
                    display: none;
                }
            `}
            title={
                <FlexLayout
                    align="center"
                    justify="space-between"
                    style={{width: '100%'}}
                >
                    <Typography.Text
                        type="secondary"
                        ellipsis={{tooltip: v.key}}
                    >
                        {v.key}
                    </Typography.Text>
                    <Space>
                        {v.label && (
                            <VariableLabelTag
                                variable={v}
                                onDelete={() => onChange({...v, label: '' as any})}
                                disabled={isResultHistory}
                            />
                        )}
                        <PromptVariableOpration items={items} />
                    </Space>

                </FlexLayout>
            }
            bodyStyle={{padding: 0}}
            headStyle={{
                padding: '2px 12px',
                minHeight: '16px',
                background: 'var(--color-gray-2)',
                fontWeight: 'unset',
                color: 'var(--color-gray-2)',
            }}
        >
            <Input.TextArea
                value={v.value}
                onChange={e => onChange({...v, value: e.target.value})}
                bordered={false}
                placeholder="请输入变量"
                style={{resize: 'none'}}
                autoSize={{minRows: 1, maxRows: expend ? 30 : 2}}
                {...textAreaProps}
            />
        </Card>
    );
}

const PromptVariableContainer = styled.div`
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fill, minmax(180px, 100%));
`;

interface Props {
    value?: PromptVariable[];
    onChange?: (newVariables: PromptVariable[]) => void;
}

export function VariablePanel({value, onChange}: Props) {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const variables: PromptVariable[] = value ?? [];

    const handleChange = useCallback(
        (key: string, newValue: PromptVariable) => {
            const newVars = variables.map((v): PromptVariable => {
                if (v.key === key) {
                    return newValue;
                }
                return v;
            });
            onChange?.(newVars);
        },
        [onChange, variables]
    );
    return (
        <PanelContainer
            style={{
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
            }}
        >
            <Typography.Title level={5} style={{marginBottom: '8px'}}>
                变量
            </Typography.Title>
            {variables.length === 0 ? (
                <NoVariables />
            ) : (
                <div style={{flex: 1, overflow: 'auto'}}>
                    <PromptVariableContainer>
                        {variables.map(v => {
                            return (
                                <PromptVariableItem
                                    key={v.key}
                                    variable={v}
                                    onChange={(value: PromptVariable) =>
                                        handleChange(v.key, value)
                                    }
                                />
                            );
                        })}
                    </PromptVariableContainer>
                </div>
            )}
            <VariableFullModal />
            <VideoParseModal />
        </PanelContainer>
    );
}
