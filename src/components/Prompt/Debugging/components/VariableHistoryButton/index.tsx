import {useState} from 'react';
import {Popover} from 'antd';
import {css} from '@emotion/css';
import {PromptVariable} from '@/api/ievalue/prompt-version';
import {VariableHistoryPopoverContent} from './VariableHistoryPopoverContent';
export const videoParseModelKey = 'videoParseModelKey';
export const VariableHistoryButton = ({
    value,
    onChange,
}: {
    value: PromptVariable;
    onChange: (value: PromptVariable) => void;
}) => {
    const [visible, setVisible] = useState(false);
    return (
        <Popover
            open={visible}
            onOpenChange={setVisible}
            arrow={false}
            rootClassName={css`
                .ant-5-popover-inner {
                    padding: 8px 0 !important;
                }
                .ant-5-popover-inner-content {
                    padding: 0 !important;
                    overflow: hidden;
                }
            `}
            content={
                <VariableHistoryPopoverContent
                    value={value}
                    onChange={onChange}
                    visible={visible}
                />
            }
            placement="leftTop"
            align={{
                offset: [28, -12], // [水平偏移, 垂直偏移]
            }}
        >
            <div style={{cursor: 'pointer', width: '100%', height: '100%'}}>
                历史值
            </div>
        </Popover>
    );
};
