import {Empty, Flex, Menu, Spin, Typography} from 'antd';
import {useMemo, useEffect, useTransition} from 'react';
import {css} from '@emotion/css';
import {usePromptID} from '@/hooks/ievalue/prompt';
import {
    usePromptVariableValuesByRegion,
    usePromptVariableValuesLoadingByRegion,
    loadPromptVariableValuesByRegion,
} from '@/regions/ievalue/prompt/promptVariableValues';
import {PromptVariable} from '@/api/ievalue/prompt-version';

export const VariableHistoryPopoverContent = ({
    value,
    onChange,
    visible,
}: {
    value: PromptVariable;
    onChange: (value: PromptVariable) => void;
    visible: boolean;
}) => {
    const promptID = usePromptID();
    const params = useMemo(
        () => ({
            promptID,
            variableKey: value.key,
        }),
        [promptID, value.key]
    );
    const promptVariableValues = usePromptVariableValuesByRegion(params);
    const loading = usePromptVariableValuesLoadingByRegion(params);
    const [startLoading, startTransition] = useTransition();
    useEffect(
        () => {
            if (visible) {
                startTransition(() => {
                    loadPromptVariableValuesByRegion(params);
                });
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [visible]
    );
    const items = useMemo(
        () => {
            return promptVariableValues?.values?.map(value => ({
                key: value,
                label: (
                    <Flex
                        style={{width: 150, height: '100%', fontWeight: 500}}
                        align="center"
                        justify="start"
                    >
                        <Typography.Text ellipsis={{tooltip: value}}>
                            {value}
                        </Typography.Text>
                    </Flex>
                ),
            }));
        },
        [promptVariableValues?.values]
    );
    if (!promptVariableValues?.values?.length) {
        return (
            <Spin spinning={loading || startLoading}>
                <Empty
                    description="暂无历史值"
                    className={css`
                        width: 150px !important;
                        padding: 20px 0 !important;
                        .ant-5-empty-image {
                            height: 60px !important;
                        }
                        .ant-5-empty-description{
                            font-size: 12px !important;
                        }
                    `}
                    style={{padding: '10px 0'}}
                />
            </Spin>
        );
    }

    return (
        <Menu
            className={css`
                .ant-5-menu-css-var {
                    padding: 0 !important;
                }
                .ant-5-menu-item {
                    height: 32px !important;
                    line-height: 32px !important;
                    padding: 0 12px !important;
                    min-width: 150px !important;
                }
            `}
            items={items}
            mode="vertical"
            onClick={({key}) => {
                onChange({
                    ...value,
                    value: key,
                });
            }}
        />
    );
};
