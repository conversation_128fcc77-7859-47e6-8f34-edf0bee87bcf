/* eslint-disable max-lines, max-statements */
import {Form, FormInstance} from 'antd';
import {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
    MutableRefObject,
} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {ModelItem} from '@/api/ievalue/model';
import {
    PromptModel,
    PromptMonitorTaskInfo,
    PromptReleaseRecordListItem,
    PromptResultList,
    PromptRunParams,
    PromptSingleRunResult,
    apiPromptRun,
} from '@/api/ievalue/prompt';
import {PromptVersionVersionItem} from '@/api/ievalue/prompt-version';
import {getSelectModel} from '@/components/ievalue/ModelDebugParameters';
import {
    PromptMenuKeyEnum,
} from '@/constants/ievalue/prompt';
import {
    usePromptID,
    usePromptMonitorTaskInfo,
    usePromptReleaseRecordList,
    useVersionList,
    useWorkflowQuery,
} from '@/hooks/ievalue/prompt';
import {
    useSpaceCodeSafe,
} from '@/hooks/ievalue/spacePage';
import useGetState from '@/hooks/ievalue/useGetState';
import {PromptUrl} from '@/links/ievalue/prompt';
import {ModelOptionItem} from '@/types/ievalue/prompt';
import {useActionLoading} from '@/third-party/panda-design-hooks/useActionLoading';
import {loadPromptReleaseIdcList} from '@/regions/ievalue/prompt/promptReleaseIdc';
import {SetState} from '@/types/common';
import {
    debouncedPromptUpdate,
    FormData,
} from '@/components/Prompt/Debugging';
import {useUiConfig} from '@/components/Prompt/Debugging/useUiConfig';
import {usePromptWebSocketContext} from '@/components/PromptChat/PromptWebSocketProvider';
import {useModelInfo} from './useModelInfo';
import {usePromptListInfo} from './usePromptListInfo';
import {useInitSelectedComparaVersion} from './useInitSelectedComparaVersion';
import {useUpdataCurrentVersion} from './useUpdataCurrentVersion';
import {useNavigatToVersionDetail} from './useNavigatToVersionDetail';

export interface PromptMenuItem {
    key: PromptMenuKeyEnum;
    label: string | ReactNode;
    rigghtExtra?: ReactNode;
    tooltip?: string | ReactNode;
    disabled?: boolean;
    desc?: string;
    enableSelect?: boolean;
    content?: ReactNode;
    children: PromptMenuItem[];
    onClick?: (item: PromptMenuItem) => void;
}

interface ContextValue {
    form: FormInstance<FormData>;
    values: any;
    setValues: SetState<FormData | undefined>;
    generate: any;
    setGenerate: SetState<any>;
    active: PromptMenuKeyEnum;
    setActive: SetState<PromptMenuKeyEnum>;
    uiConfig: any;
    expend: boolean;
    setExpend: SetState<boolean>;
    currentPrompt: PromptModel | undefined;
    currentVersion: PromptVersionVersionItem | undefined;
    lastVersion: PromptVersionVersionItem | undefined;
    versionList: PromptVersionVersionItem[];
    refreshVersionList: () => void;
    setCurrentVersion: SetState<PromptVersionVersionItem | undefined>;
    getCurrentVersion: () => PromptVersionVersionItem | undefined;
    selectedVersions: PromptVersionVersionItem[];
    setSelectedVersions: SetState<PromptVersionVersionItem[]>;
    allSelected: PromptVersionVersionItem[];
    setAllSelected: SetState<PromptVersionVersionItem[]>;
    versionID: number;
    isResultHistory: boolean;
    isHistoryVersion: boolean;
    applyVersion: () => void;
    rollbackToLastVersion: () => void;
    rollbackToGivenPromptVersion: (
        version: PromptResultList | PromptVersionVersionItem
    ) => void;
    updateLoading: boolean;
    submitRef: MutableRefObject<HTMLFormElement | null>;
    publishRef: MutableRefObject<HTMLFormElement | null>;
    onVersionChange: (e: number) => void;
    onSubmitCallback: (version: PromptVersionVersionItem) => void;
    releaseRecordList: PromptReleaseRecordListItem[];
    refreshReleaseList: () => void;
    handlePromptChange: (promptID: number) => void;
    datasetColumnMapID: number;
    setDatasetColumnMapID: SetState<number>;
    datasetID: number;
    setDatasetID: SetState<number>;
    modelList: ModelItem[];
    modelOptions: ModelOptionItem[];
    modelPending: boolean;
    modelListRefresh: () => void;
    promptList: PromptModel[];
    monitorTaskInfoRefresh: () => void;
    monitorTaskInfo: PromptMonitorTaskInfo;
    uploadAccept: string;
    uploadAcceptFromSelectVersions: string;
    actionPromptRun: (
        params: PromptRunParams
    ) => Promise<PromptSingleRunResult>;
    runPending: boolean;
}

const Context = createContext<ContextValue>({} as ContextValue);
export const PromptProvider = ({children}: { children: ReactNode }) => {
    const [values, setValues] = useState<any>();
    const [generate, setGenerate] = useState<any>();
    const [currentVersion, setCurrentVersion, getCurrentVersion] = useGetState<
        PromptVersionVersionItem | undefined
    >();
    const [expend, setExpend] = useState<boolean>(false);
    const hash = window?.location?.hash || PromptMenuKeyEnum.develop;
    const [active, setActive] = useState<PromptMenuKeyEnum>(
        hash.replace('#', '') as PromptMenuKeyEnum
    );
    const [form] = Form.useForm<FormData>();
    const uiConfig = useUiConfig();
    const {resultID} = useParams();
    const spaceCode = useSpaceCodeSafe();
    const modelID = Form.useWatch('modelID', form);
    const [actionPromptRun] = useActionLoading(apiPromptRun);

    const [monitorTaskInfo, {refresh: monitorTaskInfoRefresh}] =
        usePromptMonitorTaskInfo({
            promptVersionID: currentVersion?.promptVersionID,
        });

    const isHistoryVersion = useMemo(
        () => {
            return currentVersion?.submitStatus === 1;
        },
        [currentVersion?.submitStatus]
    );
    const {versionID: paramsVersionID} = useParams();
    const versionID = useMemo(
        () => {
            return +paramsVersionID || currentVersion?.promptVersionID || 0;
        },
        [currentVersion?.promptVersionID, paramsVersionID]
    );

    const rollbackToGivenPromptVersion = useCallback(
        (version: PromptVersionVersionItem | PromptResultList) => {
            form.setFieldsValue({isHistory: true, ...version});
        },
        [form]
    );
    const promptID = usePromptID();

    const [
        {list: versionList},
        {
            refresh: refreshVersionList,
            onRefreshVersionList,
            pending: versionListPending,
        },
    ] = useVersionList();
    const firstVersion = useMemo(
        () => {
            return versionList?.find(
                (item: PromptVersionVersionItem) => item.submitStatus === 1
            );
        },
        [versionList]
    );

    const [selectedVersions, setSelectedVersions] = useState<
        PromptVersionVersionItem[]
    >(firstVersion ? [firstVersion] : []);
    const [allSelected, setAllSelected] = useState<PromptVersionVersionItem[]>(
        firstVersion ? [firstVersion] : []
    );

    const [datasetColumnMapID, setDatasetColumnMapID] = useState<number>();
    const [datasetID, setDatasetID] = useState<number>();
    const {promptList, currentPrompt} = usePromptListInfo();
    const navigate = useNavigate();

    const {
        uploadAccept,
        uploadAcceptFromSelectVersions,
        modelOptions,
        modelList,
        modelPending,
        modelListRefresh,
    } = useModelInfo({selectedVersions, modelID});
    useEffect(
        () => {
            modelListRefresh();
            loadPromptReleaseIdcList();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const [updateLoading, setUpdateLoading] = useState(false);

    const updateLastVersionForm = useCallback(
        (version: PromptVersionVersionItem) => {
            const selectedModel = getSelectModel(
                version.modelID,
                version,
                modelList
            );
            form.setFieldsValue({
                ...version,
                isHistory: version?.submitStatus === 1,
                debugParams: selectedModel.debugParams,
            });
        },
        [form, modelList]
    );
    const handlePromptChange = useCallback(
        (promptID: number) => {
            const url = PromptUrl.detail.toUrl({
                spaceCode,
                promptID,
                versionID: 0,
            });
            navigate(url);
            setActive(PromptMenuKeyEnum.develop);
        },
        [navigate, spaceCode]
    );
    const navigatToVersionDetail = useNavigatToVersionDetail();

    const onVersionChange = useCallback(
        (e: number) => {
            const item = versionList?.find(
                item => item.promptVersionID === e
            );
            setCurrentVersion(item);
            navigatToVersionDetail(item);
            setActive(PromptMenuKeyEnum.develop);
            updateLastVersionForm(item);
            setDatasetColumnMapID(undefined);
            setDatasetID(undefined);
            setSelectedVersions([item]);
        },
        [versionList, setCurrentVersion, navigatToVersionDetail, updateLastVersionForm]
    );

    const lastVersion = useMemo(
        () => {
            return versionList.find(
                (e: PromptVersionVersionItem) => e.submitStatus === 0
            );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [versionList]
    );

    const applyVersion = useCallback(
        () => {
            const values = form.getFieldsValue();
            setUpdateLoading(true);
            debouncedPromptUpdate({
                params: {...values, promptID},
                callback: () => {
                    form.setFieldsValue({isHistory: false});
                    setCurrentVersion({...lastVersion, ...values});
                    onRefreshVersionList(() => {
                        setUpdateLoading(false);
                        navigatToVersionDetail({...lastVersion, ...values});
                    });
                },
                catchCallback: () => {
                    setUpdateLoading(false);
                },
            });
        },
        [
            form,
            lastVersion,
            navigatToVersionDetail,
            promptID,
            onRefreshVersionList,
            setCurrentVersion,
        ]
    );
    const rollbackToLastVersion = useCallback(
        () => {
            setCurrentVersion(lastVersion);
            updateLastVersionForm(lastVersion);
            navigatToVersionDetail(lastVersion);
        },
        [
            lastVersion,
            navigatToVersionDetail,
            setCurrentVersion,
            updateLastVersionForm,
        ]
    );
    const onSubmitCallback = useCallback(
        (version?: any) => {
            form.setFieldsValue({
                isHistory: version?.submitStatus === 1,
            });
            navigatToVersionDetail(version);
            setSelectedVersions([version]);
            setAllSelected([version]);
        },
        [form, navigatToVersionDetail]
    );

    useInitSelectedComparaVersion({
        setSelectedVersions,
        setAllSelected,
        versionList,
        currentVersion,
    });

    const {updataCurrentVersion} = useUpdataCurrentVersion({
        setCurrentVersion,
        modelList,
        currentVersion,
        versionListPending,
        form,
    });

    useEffect(
        () => {
            onRefreshVersionList(data => {
                const initData = updataCurrentVersion(data?.list ?? []);
                form.setFieldsValue(initData);
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );


    useEffect(
        () => {
            updataCurrentVersion(versionList);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [versionList, modelList?.length]
    );

    const submitRef = useRef<any>();
    const publishRef = useRef<any>();
    const [{list: releaseRecordList}, {refresh: refreshReleaseList}] =
        usePromptReleaseRecordList(versionID);
    const {delayRefresh} = useWorkflowQuery();
    const {readyState, close} = usePromptWebSocketContext();
    useEffect(
        () => {
            delayRefresh();
            close?.();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [versionID, currentVersion?.submitStatus]
    );

    return (
        <Context.Provider
            value={{
                form,
                values,
                setValues,
                generate,
                setGenerate,
                active,
                setActive,
                uiConfig,
                expend,
                setExpend,
                lastVersion,
                versionList,
                refreshVersionList,
                currentPrompt,
                currentVersion,
                setCurrentVersion,
                getCurrentVersion,
                selectedVersions,
                setSelectedVersions,
                allSelected,
                setAllSelected,
                versionID,
                isResultHistory: !!resultID,
                applyVersion,
                isHistoryVersion: isHistoryVersion,
                rollbackToLastVersion,
                rollbackToGivenPromptVersion,
                updateLoading,
                submitRef,
                publishRef,
                onVersionChange,
                onSubmitCallback,
                releaseRecordList,
                refreshReleaseList,
                handlePromptChange,
                datasetColumnMapID,
                setDatasetColumnMapID,
                datasetID,
                setDatasetID,
                modelList,
                modelOptions,
                modelPending,
                modelListRefresh,
                promptList,
                monitorTaskInfoRefresh,
                monitorTaskInfo,
                uploadAccept,
                uploadAcceptFromSelectVersions,
                actionPromptRun,
                runPending: readyState === 'OPEN' || readyState === 'CONNECTING',
            }}
        >
            {children}
        </Context.Provider>
    );
};

export const usePromptContext = () => {
    return useContext(Context);
};
