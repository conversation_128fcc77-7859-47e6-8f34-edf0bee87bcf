/* eslint-disable max-lines */
import {message, Modal} from '@panda-design/components';
import {Form} from 'antd';
import {useCallback, useState} from 'react';
import {useFeedbackModalProps, closeFeedbackModal} from '@/regions/feedback/feedbackModalOpen';
import {ScreenshotProvider, useScreenshotContext} from '@/providers/ScreenshotProvider';
import {apiFileUpload} from '@/api/ievalue/dataset';
import {FeedbackType, apiUserFeedbackAdd} from '@/api/ievalue/user';
import {useTrackClick, LocationEnum, EventActionEnum} from '@/api/ievalue/track';
import {FeedbackFormContent} from '../Feedback/FeedbackFormContent';
import {FeedbackProvider, useFeedbackContext} from '../Feedback/FeedbackProvider';
import {fields} from '../Feedback/fields';

export const FeedbackModalContent = () => {
    const [form] = Form.useForm();
    const title = useFeedbackModalProps();
    const {feedbacks, feedbackType} = useFeedbackContext();
    const {
        handleCapture,
    } = useScreenshotContext();
    const [loading, setLoading] = useState(false);
    const {trackClick} = useTrackClick();
    const onFinish = useCallback(
        async (values: FeedbackType) => {
            try {
                setLoading(true);
                const formData = new FormData();
                const {file} = await handleCapture();
                formData.append('file', file);
                const res = await apiFileUpload(formData);
                await apiUserFeedbackAdd({...values, screenshotUrl: res.url});
                trackClick({
                    location: LocationEnum.feedback,
                    action: EventActionEnum.ClickSubmitFeedback,
                    param: {...values, screenshotUrl: res.url},
                });
                message.success('提交成功，感谢您宝贵建议，我们会尽快根据您的反馈进行优化！');
                setLoading(false);
                closeFeedbackModal();
            } catch (error) {
                setLoading(false);
            }
        },
        [handleCapture, trackClick]
    );

    if (!title) {
        return null;
    }

    return (
        <Modal
            open
            title={title}
            onOk={() => form.submit()}
            okButtonProps={{loading}}
            onCancel={closeFeedbackModal}
            destroyOnClose
            width={800}
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                preserve={false}
                style={{maxHeight: '80vh', overflowY: 'auto', overflowX: 'hidden', paddingRight: '16px'}}
                initialValues={{feedbacks, feedbackType}}
            >
                <FeedbackFormContent />
            </Form>
        </Modal>
    );
};

export const FeedbackModal = () => {
    return (
        <ScreenshotProvider padding="40px">
            <FeedbackProvider feedbacks={fields} feedbackType="issueFeedback">
                <FeedbackModalContent />
            </FeedbackProvider>
        </ScreenshotProvider>
    );
};
