import styled from '@emotion/styled';
import {useCallback, useRef} from 'react';
import {css, cx} from '@emotion/css';
import {Divider} from 'antd';
import {IconCollapse} from '@/icons/comatestack';
import {MenuItemType, MenuProps} from '@/types/menu/menu';
import {MenuVariables} from '@/constants/menu/menu';
import {useActiveMenuGroupKey, useMenuGroupElements} from '@/regions/menu/menu';
import {MenuList} from './MenuList';
import {Logo} from './Logo';
import {MenuItemContainer} from './MenuItemContainer';

const Container = styled.div`
    position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    top: ${MenuVariables.top};
    left: 0;
    bottom: 0;
    background: ${MenuVariables.bg};
    border-right: 1px solid ${MenuVariables.borderColor};
    overflow: hidden;
    transition: width 0.3s;
    z-index: ${MenuVariables.zIndex};
`;

const collapsedCss = css`
    width: ${MenuVariables.widthCollapsed};
`;

const expandedCss = css`
    width: ${MenuVariables.widthExpanded};
`;

const itemCss = css`
    height: 40px !important;
    margin: auto;
`;

const CollapseContainer = styled.div`
    display: flex;
    align-self: flex-end;
    justify-content: end;
    font-size: ${MenuVariables.iconSize};
    margin : 4px;
`;

const itemExpandedCss = css`
    width: 40px !important;
`;

const scrollbarHide = css`
    overflow-y: auto;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
    &::-webkit-scrollbar {
        display: none !important;
    }
    overscroll-behavior-y: contain;
`;

const WidthPlaceholder = styled.div`
    flex-shrink: 0;
    transition: width 0.3s;
`;

export const Menu = ({
    logo,
    className,
    style,
    items,
    childrenElement,
    enableCollapse = true,
    collapsed,
    onCollapse,
}: MenuProps) => {
    const activeMenuGroupKey = useActiveMenuGroupKey();
    const menuGroupElements = useMenuGroupElements(); // 滚动相关元素的Map存储，使用key获取对应list渲染出的元素
    const containerRef = useRef(null); // 这个是滚动容器

    const handleClick = useCallback(
        () => {
            onCollapse?.(!collapsed);
            // 折叠后滚动到活动组
            setTimeout(() => {
                if (!collapsed && activeMenuGroupKey && containerRef.current) {
                    const element = menuGroupElements.current.get(activeMenuGroupKey.split('-')[0]);
                    if (element) {
                        // 较为精确的滚动位置计算，考虑容器padding和元素margin
                        const containerRect = containerRef.current.getBoundingClientRect();
                        const elementRect = element.getBoundingClientRect();
                        const scrollPosition = elementRect.top - containerRect.top + containerRef.current.scrollTop;
                        containerRef.current.scrollTo({top: scrollPosition + 10, behavior: 'smooth', duration: 300});
                    }
                } else if (!collapsed && containerRef.current) {
                    containerRef.current.scrollTo({top: 0, behavior: 'smooth', duration: 300});
                }
            }, 300);
        },
        [collapsed, onCollapse, activeMenuGroupKey, menuGroupElements]
    );

    return (
        <>
            <Container
                className={cx(className, collapsed ? collapsedCss : expandedCss)}
                style={style}
            >
                <Logo item={{className: itemCss, ...logo}} collapsed={collapsed} />
                <Divider style={{margin: 0}} />
                <div className={scrollbarHide} ref={containerRef}>
                    <MenuList
                        level={1}
                        items={items}
                        childrenElement={childrenElement}
                        collapsed={collapsed}
                    />
                </div>
                {enableCollapse && (
                    <>
                        <Divider key={'divider-icon-collapse'} style={{margin: 0}} />
                        <CollapseContainer>
                            <MenuItemContainer
                                collapsed={collapsed}
                                isActive={false}
                                level={1}
                                item={{
                                    className: cx(itemCss, !collapsed && itemExpandedCss),
                                    onClick: handleClick,
                                } as MenuItemType}
                            >
                                <IconCollapse style={{
                                    transform: collapsed ? 'scaleX(-1)' : 'unset',
                                    color: '#BFBFBF',
                                }}
                                />
                            </MenuItemContainer>
                        </CollapseContainer>
                    </>
                )}
            </Container>
            <WidthPlaceholder className={collapsed ? collapsedCss : expandedCss} />
        </>
    );
};
