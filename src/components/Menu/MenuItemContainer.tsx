import {Link} from 'react-router-dom';
import {ReactNode} from 'react';
import {css, cx} from '@emotion/css';
import {MenuItemType} from '@/types/menu/menu';
import {MenuCalculated, MenuVariables} from '@/constants/menu/menu';

const containerCss = css`
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    transition: width 0.3s, height 0.3s;
`;

const getInteractiveCss = (
    shouldHighlight: boolean,
    isActive: boolean,
    textColor: string
) => css`
    color: ${textColor};
    background-color: ${shouldHighlight ? MenuVariables.itemBgActive : 'unset'};
    font-weight: ${shouldHighlight ? '500' : '400'};
    
    &:focus, 
    &:active {
      color: ${textColor};
      background-color: ${isActive ? MenuVariables.itemBgActive : 'unset'};
    }
    
    &:hover {
      color: ${textColor};
      background-color: ${MenuVariables.itemBgHover};
    }
`;

const collapsedContainerCss = css`
    width: ${MenuCalculated.innerWidthCollapsed};
    height: 48px;
    margin: 0 4px;
    border-radius: 4px;
`;

const expandedContainerCss = (level: number) => css`
    width: ${level === 3
        ? `calc(${MenuCalculated.innerWidthExpanded} - 20px)`
        : MenuCalculated.innerWidthExpanded};
    height: 40px;
    border-radius: 6px;
`;

interface Props {
    isActive: boolean;
    level: number;
    item: MenuItemType;
    collapsed: boolean;
    children: ReactNode;
    onItemClick?: () => void;
}

export const MenuItemContainer = ({
    isActive,
    level,
    item,
    collapsed,
    children,
    onItemClick,
}: Props) => {
    const {to, className, style, onClick} = item;
    const Component = item.to ? Link : 'div';
    const shouldHighlight = isActive && !(level === 2 && item?.children?.length);
    const textColor = isActive ? MenuVariables.activeColor : MenuVariables.color;

    return (
        <Component
            to={to as string}
            className={cx(
                containerCss,
                getInteractiveCss(shouldHighlight, isActive, textColor),
                collapsed ? collapsedContainerCss : expandedContainerCss(level),
                className as any
            )}
            style={style as any}
            onClick={e => {
                e.stopPropagation();
                onClick?.();
                onItemClick?.();
            }}
        >
            {children}
        </Component>
    );
};
