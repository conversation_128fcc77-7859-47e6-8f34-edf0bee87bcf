import styled from '@emotion/styled';
import {Link} from 'react-router-dom';
import {css, cx} from '@emotion/css';
import {MenuItemType} from '@/types/menu/menu';
import {MenuCalculated, MenuVariables} from '@/constants/menu/menu';

const LogoContainer = styled.div`
    padding: 0 12px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 6px;

    :hover {
        background-color: ${MenuVariables.itemBgHover};
    }
`;

const collapsedContainerCss = css`
    width: ${MenuCalculated.innerWidthCollapsed};
    height: 48px;
    border-radius: 4px;
    margin: 4px;
`;

const expandedContainerCss = css`
    width: ${MenuCalculated.innerWidthExpanded};
    height: 40px;
    border-radius: 6px;
    margin: 4px;
`;

const IconContainer = styled.div`
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: ${22 / 14};
    width: ${MenuVariables.logoIconSize};
    transition: top 0.3s, left 0.3s;

    svg {
        font-size: ${MenuVariables.logoIconSize};
        width: ${MenuVariables.logoIconSize};
        height: ${MenuVariables.logoIconSize};
        top: 0;
    }
`;

const IconLink = styled(Link)<{isCollapsed: boolean}>`
    color: ${MenuVariables.color};

    :focus,
    :active {
        background-color: ${'unset'};
    }

    :hover {
        color: ${({isCollapsed}) => (!isCollapsed && MenuVariables.activeColor)}
    }
`;

const TitleContainer = styled.div`
    font-size: 11px;
    margin: 4px;
    position: absolute;
    transition: top 0.3s, left 0.3s;
`;

const TitleLink = styled(Link)`
    position: absolute;
    transition: top 0.3s, left 0.3s;
    top: 9px;
    font-size: 14px;
    line-height: ${22 / 14};
    left: 40px;
    color: ${MenuVariables.color};
    background-color: ${'unset'};

    :focus,
    :active {
        color: ${MenuVariables.color};
        background-color: ${'unset'};
    }

    :hover {
        color: ${MenuVariables.activeColor};
    }
`;

const collapsedTitleCss = css`
    top: 24px;
    font-size: 12px;
`;

interface Props {
    collapsed: boolean;
    item: MenuItemType;
}

export const Logo = ({collapsed, item}: Props) => {
    const {icon, title} = item;

    return (
        <LogoContainer
            className={cx(
                collapsed ? collapsedContainerCss : expandedContainerCss)}
        >
            <IconContainer
                style={{
                    top: collapsed ? '6px' : '10px',
                    left: collapsed ? '14px' : '14px',
                }}
            >
                <IconLink to={item.iconTo} isCollapsed={collapsed}>{icon}</IconLink>
            </IconContainer>
            {collapsed ? (
                <TitleContainer className={collapsedTitleCss}>
                    {item.shortTitle}
                </TitleContainer>
            ) : <TitleLink to={item.titleTo}>{title}</TitleLink>}
        </LogoContainer>
    );
};
