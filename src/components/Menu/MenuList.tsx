import {Fragment, ReactNode, useState} from 'react';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {Typography} from 'antd';
import {Divider} from 'antd';
import {MenuProps} from '@/types/menu/menu';
import {MenuCalculated, MenuVariables} from '@/constants/menu/menu';
import {useMenuGroupElements} from '@/regions/menu/menu';
import {MenuItem} from './MenuItem';

const {Text} = Typography;

const getMenuListCss = (collapsed: boolean, level: number) => css`
    padding: ${level === 1 ? (collapsed ? 4 : 12) : 0}px 0;
    margin-left: ${level === 3 ? (collapsed ? 0 : 20) : 0}px;
    border-radius: ${collapsed ? '4px' : '6px'};
    width: ${collapsed ? MenuVariables.widthCollapsed : MenuCalculated.innerWidthExpanded};
`;

const GroupTitle = styled(Text)`
    height: 22px;
    margin-left: 18px;
    font-weight: 400;
    font-size: 12px !important;
    line-height: 26px !important;
    color: rgba(143, 143, 143, 1) !important;
`;

const Container = styled.div`
    display: flex;
    flex-direction: column;
    transition: width 0.3s;
`;

interface Props {
    level: number;
    collapsed: boolean;
    items?: MenuProps['items'];
    childrenElement?: ReactNode;
    groupKey?: string; // 添加分组key
}

export const MenuList = ({level, items, collapsed, childrenElement, groupKey}: Props) => {
    const menuGroupElements = useMenuGroupElements();

    // 初始化展开状态，如果有三级菜单被选中，则展开二级菜单，否则保持折叠状态
    const initValue = level === 2 && items?.reduce(
        (accumulator, item) => {
            return item?.children?.reduce(
                (childAccumulator, childItem) => {
                    return location.pathname.startsWith(childItem.to as string) || childAccumulator;
                },
                accumulator
            );
        },
        false
    );

    const [isExpanded, setIsExpanded] = useState(initValue);

    return (
        <Container className={getMenuListCss(collapsed, level)}>
            {items?.map((item, index) => {
                const elementItem = item;
                const uniqueKey = `${elementItem.type === 'group' ? elementItem.title : groupKey}-${index}`;
                // 如果当前元素是组，则设置ref并存元素到Map中，否则直接渲染MenuItem
                // 由于分级比较多所以使用index进行唯一key渲染，再split取groupName
                const element = elementItem.type === 'group' ? (
                    <div
                        key={`group-${uniqueKey}`}
                        ref={el => {
                            if (el || !menuGroupElements.current.has(uniqueKey)) {
                                menuGroupElements.current.set(uniqueKey.split('-')[0], el);
                            }
                            else {menuGroupElements.current.delete(uniqueKey.split('-')[0]);}
                        }}
                        style={{marginTop: collapsed ? 0 : 12}}
                    >
                        {collapsed ? (
                            <Divider style={{margin: '4px 0'}} />
                        ) : (
                            <GroupTitle>{elementItem.title}</GroupTitle>
                        )}
                    </div>
                ) : (
                    <MenuItem
                        key={`item-${uniqueKey}`}
                        level={level}
                        collapsed={collapsed}
                        item={elementItem}
                        onExpand={setIsExpanded}
                        isExpanded={isExpanded}
                        groupKey={uniqueKey} // 直接使用唯一键
                    />
                );

                // 如果当前元素有子元素，并且是二级菜单，那么只有在菜单为展开状态并且未折叠，才会递归渲染子元素
                if (elementItem.children && (level !== 2 || (isExpanded && !collapsed))) {
                    return (
                        <Fragment key={`list-${uniqueKey}`}>
                            {element}
                            <MenuList
                                level={level + 1}
                                items={elementItem.children}
                                collapsed={collapsed}
                                childrenElement={elementItem.childrenElement}
                                groupKey={uniqueKey} // 传递当前唯一键
                            />
                        </Fragment>
                    );
                }

                return element;
            })}
            {isExpanded && childrenElement}
        </Container>
    );
};
