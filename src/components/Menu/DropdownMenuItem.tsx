import {ReactNode, CSSProperties} from 'react';
import styled from '@emotion/styled';
import {Typography} from 'antd';
import {Link, useLocation} from 'react-router-dom';
import {Divider} from 'antd';
import {MenuItemType} from '@/types/menu/menu';
import {MenuVariables} from '@/constants/menu/menu';

const TitleContainer = styled(Typography.Text)`
    padding: 0 8px;
    display: flex;
    align-items: center;
    height: 26px;
    color: #8F8F8F !important;
    font-size: 12px;
    line-height: 22px;
`;

const DropdownMenuItemContainer = styled.div`
    width: 136px;
    display: flex;
    flex-direction: column;
`;

const ChildItemContainer = styled(Link)<{isActive?: boolean}>`
    padding: 0 8px;
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 6px;
    color: ${props => (props.isActive ? MenuVariables.activeColor : MenuVariables.color)};
    background-color: ${props => (props.isActive ? MenuVariables.itemBgActive : 'transparent')};
    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: ${props => (props.isActive ? 500 : 400)};
    line-height: 22px;

    &:hover {
        color: ${props => (props.isActive ? MenuVariables.activeColor : MenuVariables.color)};
        background-color: ${MenuVariables.itemBgHover};
    }

    &:focus,
    &:active {
        background-color: ${MenuVariables.itemBgActive};
    }
`;

interface DropdownMenuItemProps {
    item: MenuItemType;
    title?: string;
    childrenElement?: ReactNode;
    className?: string;
    style?: CSSProperties;
}

export const DropdownMenuItem = ({item, childrenElement, className, style, title}: DropdownMenuItemProps) => {
    const location = useLocation();

    // 判断子项是否活跃
    const isChildActive = (childItem: MenuItemType) => {
        return childItem.isActive ?? location.pathname.startsWith(childItem.to as string);
    };

    return (
        <>
            {(item.children || item.childrenElement) ? (
                <>
                    <TitleContainer>{item.title}</TitleContainer>
                    <Divider style={{margin: '4px 0'}} />
                    <DropdownMenuItemContainer className={className} style={style}>
                        {item.children
                            ?.map((childItem, index) => (
                                <ChildItemContainer
                                    key={`${childItem.title}-${index}`}
                                    to={childItem.to}
                                    onClick={childItem.onClick}
                                    isActive={isChildActive(childItem)}
                                >
                                    {childItem.title}
                                </ChildItemContainer>
                            ))
                        }
                        {childrenElement && (
                            <div>
                                {childrenElement}
                            </div>
                        )}
                    </DropdownMenuItemContainer>
                </>
            ) : (
                <div style={{color: 'white'}}>
                    {title}
                </div>
            )}
        </>
    );
};
