import styled from '@emotion/styled';
import {useLocation} from 'react-router-dom';
import {useLayoutEffect, useMemo, useRef} from 'react';
import {Tooltip} from 'antd';
import {css} from '@emotion/css';
import {Text} from '@panda-design/components';
import {IconMenuListArrow} from '@/icons/comatestack';
import {MenuItemType} from '@/types/menu/menu';
import {MenuVariables} from '@/constants/menu/menu';
import {setActiveMenuGroupKey} from '@/regions/menu/menu';
import {MenuItemContainer} from './MenuItemContainer';
import {DropdownMenuItem} from './DropdownMenuItem';

const IconContainer = styled.div({
    position: 'absolute',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: MenuVariables.iconSize,
    height: MenuVariables.iconSize,
    transition: 'top 0.3s, left 0.3s',
    svg: {
        fontSize: MenuVariables.iconSize,
        width: MenuVariables.iconSize,
        height: MenuVariables.iconSize,
        top: 0,
    },
});

const tooltipContainer = (shouldShowDropDown: boolean) => css`
    && {
        .ant-tooltip-inner,
        .ant-5-tooltip-inner,
        [class*='css-dev-only-do-not-override-'].ant-tooltip-inner,
        [class*='css-dev-only-do-not-override-'].ant-5-tooltip-inner {
            box-shadow: ${shouldShowDropDown && 'none'} !important;
        }
    }
`;

const TitleContainer = styled.div({position: 'absolute', transition: 'top 0.3s, left 0.3s'});
const collapsedTitleCss = css({top: '26px', fontSize: '11px'});
const expandedTitleCss = css({top: '9px', fontSize: '14px', lineHeight: 22 / 14});
const ExpandIconContainer = styled(Text)<{isActive?: boolean}>`
    position: absolute;
    right: 14px;
    margin: 'auto';
    color: ${props => (props.isActive ? '#0080FF' : '#000000')} !important;
    line-height: 10px;

    svg {
        width: auto;
        height: auto;
    }
`;

interface Props {
    level: number;
    item: MenuItemType;
    collapsed: boolean;
    isExpanded?: boolean;
    onExpand?: (value: boolean) => void;
    groupKey?: string;
}

// eslint-disable-next-line complexity
export const MenuItem = ({level, item, collapsed, isExpanded, onExpand, groupKey}: Props) => {
    const titleContainerRef = useRef<HTMLDivElement>(null);
    const location = useLocation();
    const {to, icon, title, shortTitle = title} = item;
    const shouldShowDropDown = collapsed && !item.children && !item.childrenElement;
    const isActive = item.isActive ?? location.pathname.startsWith(to as string);

    useLayoutEffect(
        () => {
            if (titleContainerRef.current) {
                const {width} = titleContainerRef.current?.getBoundingClientRect() ?? {};
                titleContainerRef.current.style.left = collapsed
                    ? width ? `${24 - width / 2}px` : 'unset'
                    : '40px';
            }
        },
        [collapsed]
    );

    const dropdownMenuItem = useMemo(
        () => {
            if (collapsed) {
                return <DropdownMenuItem item={item} title={title} />;
            }
        },
        [collapsed, title, item]
    );

    return (
        <Tooltip
            placement="rightTop"
            title={dropdownMenuItem}
            arrow={shouldShowDropDown}
            color={shouldShowDropDown && '#1F3364'}
            overlayClassName={tooltipContainer(shouldShowDropDown)}
        >
            {/* MenuItemContainer 内部的 Component 可能为 Link，为了防止与 Tooltip 冲突，包裹一个 Fragment */}
            <>
                <MenuItemContainer
                    isActive={isActive}
                    level={level}
                    item={item}
                    collapsed={collapsed}
                    onItemClick={() => {
                        if (item.children || item.childrenElement) {
                            onExpand(!isExpanded);
                        }

                        if (groupKey) {
                            setActiveMenuGroupKey(groupKey);
                        }
                    }}
                >
                    <IconContainer style={{
                        left: collapsed ? '16px' : '16px',
                        top: collapsed ? '7px' : '12px',
                        color: isActive ? '#0080FF' : '#545454',
                    }}
                    >
                        {icon}
                    </IconContainer>
                    <TitleContainer
                        ref={titleContainerRef}
                        className={collapsed ? collapsedTitleCss : expandedTitleCss}
                    >
                        {collapsed ? shortTitle : title}
                    </TitleContainer>
                    {!collapsed && (item.children || item.childrenElement) && (
                        <ExpandIconContainer isActive={isActive}>
                            {isExpanded ? (
                                <IconMenuListArrow
                                    onClick={() => onExpand(false)}
                                    style={{transform: 'rotate(180deg)'}}
                                />
                            ) : (<IconMenuListArrow onClick={() => onExpand(true)} />)
                            }
                        </ExpandIconContainer>
                    )}
                </MenuItemContainer>
            </>
        </Tooltip>
    );
};
